# 실제 데이터 기반 멀티 스포츠 분석 시스템

## 🎯 개요

이 시스템은 **실제 Supabase 데이터베이스 구조**를 정확히 분석하여 구현된 멀티 스포츠 분석 플랫폼입니다.

### 📊 실제 데이터베이스 구조 기반 설계

#### 테이블 구조
- **`target_games`**: 경기 기본 정보
- **`sportic_contents`**: 분석 콘텐츠 및 WDL 데이터
- **`sportic_sns`**: 소셜 미디어 콘텐츠
- **`sportic_pick`**: 베팅 픽
- **`team_stats`**: **야구 전용** 상세 통계

#### 스포츠별 데이터 처리 방식
- **⚾ 야구**: `team_stats` 테이블 + WDL 데이터 (가장 풍부한 데이터)
- **⚽ 축구**: WDL 데이터만 사용
- **🏀 농구**: WDL 데이터만 사용  
- **🏐 배구**: WDL 데이터만 사용

#### RPC 함수
- `get_sns_target_matches()`: 분석 대상 경기 조회

## 🏗️ 아키텍처

### CAG (Context-Aware Generation) 구조
1. **Context Layer**: 실제 데이터베이스 구조에 맞는 데이터 수집
2. **Analysis Layer**: 스포츠별 전문 에이전트
3. **Generation Layer**: 스포츠별 맞춤 콘텐츠 생성

### 스포츠별 전문 에이전트

#### ⚾ BaseballAgent
- **데이터 소스**: `team_stats` + WDL 데이터
- **분석 요소**: 
  - 투수 분석 (ERA, 피안타, 볼넷, 삼진)
  - 타격 분석 (타율, 득점, 안타, 홈런)
  - 수비 분석 (실책, 수비율)
  - 최근 폼 및 맞대결 기록

#### ⚽ SoccerAgent  
- **데이터 소스**: WDL 데이터만
- **분석 요소**:
  - 최근 폼 (승점, 승률, 득실점)
  - 홈/원정 성적 차이
  - 맞대결 기록 및 패턴
  - 전술적 특징

#### 🏀 BasketballAgent
- **데이터 소스**: WDL 데이터만
- **분석 요소**:
  - 최근 폼 (승률, 득점력, 수비력)
  - 홈/원정 성적 차이
  - 공격/수비 스타일 분석

#### 🏐 VolleyballAgent
- **데이터 소스**: WDL 데이터만
- **분석 요소**:
  - 최근 폼 (승률, 세트 득실)
  - 홈/원정 성적 차이
  - 공격/블로킹 스타일 분석

## 🚀 사용법

### 1. 시스템 테스트
```bash
# 전체 시스템 테스트 (데이터베이스 연결, 스포츠별 에이전트 등)
python test_multi_sport_system.py
```

### 2. 분석 대상 경기 확인
```bash
# 스포츠별로 분류된 분석 대상 경기 목록
python run_multi_sport.py list
```

출력 예시:
```
📋 분석 대상 경기 (5개)
============================================================

🏆 야구 (2개)
------------------------------
   1. BSW025073025
      🏟️ 키움 vs SSG
      📅 2024-06-19 19:00:00

   2. BSW025073026
      🏟️ LG vs KT
      📅 2024-06-19 19:00:00

🏆 축구 (2개)
------------------------------
   1. SOC025073025
      🏟️ 울산 vs 포항
      📅 2024-06-19 19:30:00

🏆 농구 (1개)
------------------------------
   1. BAS025073025
      🏟️ SK vs KT
      📅 2024-06-19 19:00:00
```

### 3. 스포츠별 경기 분석

#### ⚾ 야구 분석 (team_stats 활용)
```bash
python run_multi_sport.py analyze BSW025073025 --hashtags
```

출력 예시:
```
🏟️ 야구 전문 분석
================================================================================

🏟️ 키움 히어로즈 vs SSG 랜더스
📅 2024-06-19 19:00:00
🏆 종목: 야구

⚾ 투수 분석
----------------------------------------
키움 선발 박주성은 최근 3경기에서 14이닝 11실점으로 매우 불안한 모습
SSG 박시후는 시즌 ERA 3.25로 상대적으로 안정적

🏏 타격 분석
----------------------------------------
키움 팀 타율 .245, 홈런 32개로 장타력 부족
SSG 팀 타율 .268, 홈런 48개로 공격력 우세

📈 최근 폼 분석
----------------------------------------
키움 최근 5경기 2승 3패, 평균 득점 3.2점
SSG 최근 5경기 4승 1패, 평균 득점 5.8점

🔄 맞대결 분석
----------------------------------------
최근 10경기에서 SSG 6승 4패로 우세

🔑 주요 승부 요인
----------------------------------------
1. 투수력 격차 (박주성 vs 박시후)
2. SSG 타선의 안정적인 득점력
3. 키움 홈구장에서의 부진

💡 최종 예측 및 추천
----------------------------------------
🎯 예측: SSG 승리 예상
💰 추천: SSG 승 베팅 권장
📊 신뢰도: 🔴🔴🔴⚪ (3.2/4)

🏷️ 해시태그
----------------------------------------
#KBO #야구 #프로야구 #야구분석 #키움 #SSG #키움vsSSG
```

#### ⚽ 축구 분석 (WDL 데이터만)
```bash
python run_multi_sport.py analyze SOC025073025
```

출력 예시:
```
🏟️ 축구 전문 분석
================================================================================

🏟️ 울산 현대 vs 포항 스틸러스
📅 2024-06-19 19:30:00
🏆 종목: 축구

⚽ 전술 분석
----------------------------------------
울산은 4-3-3 포메이션으로 측면 공격 중심
포항은 5-4-1 수비적 전술로 역습 노리는 패턴

🏥 부상자 현황
----------------------------------------
울산 주전 미드필더 부상으로 전력 약화
포항은 주요 선수 모두 출전 가능

📈 최근 폼 분석
----------------------------------------
울산 최근 5경기 3승 1무 1패, 승점 10점
포항 최근 5경기 2승 2무 1패, 승점 8점

🔄 맞대결 분석
----------------------------------------
최근 10경기에서 울산 5승 3무 2패로 우세

💡 최종 예측 및 추천
----------------------------------------
🎯 예측: 울산 승리 또는 무승부
💰 추천: 울산 무승부 베팅
📊 신뢰도: 🔴🔴🔴⚪ (2.8/4)
```

### 4. 일괄 분석
```bash
# 모든 스포츠의 분석 대상 경기를 일괄 분석
python run_multi_sport.py batch
```

## 🔧 주요 특징

### 1. **실제 데이터 구조 반영**
- 가상의 테이블이나 필드 없이 실제 존재하는 데이터만 사용
- 야구는 `team_stats` 테이블의 풍부한 데이터 활용
- 다른 스포츠는 WDL 데이터 중심의 분석

### 2. **스포츠별 전문화**
- 각 스포츠의 특성에 맞는 분석 요소
- 스포츠별 맞춤 프롬프트 엔지니어링
- 스포츠별 해시태그 생성

### 3. **자동 스포츠 감지**
- 경기 데이터에서 자동으로 스포츠 종목 판별
- 적절한 전문 에이전트 자동 선택
- 스포츠별 데이터 가용성 확인

### 4. **확장 가능한 구조**
- 새로운 스포츠 추가 용이
- 새로운 데이터 소스 통합 가능
- SOLID 원칙 적용

## 📊 데이터 활용 현황

### 야구 (가장 풍부한 데이터)
- ✅ `team_stats`: 타율, ERA, 득점, 안타, 홈런, 실책 등
- ✅ WDL 데이터: 승무패 기록, 최근 폼
- ✅ H2H 데이터: 맞대결 기록
- ✅ 베팅 데이터: 배당률, 픽

### 축구/농구/배구 (WDL 중심)
- ✅ WDL 데이터: 승무패 기록, 최근 폼
- ✅ H2H 데이터: 맞대결 기록
- ✅ 베팅 데이터: 배당률, 픽
- ❌ 상세 통계: 별도 테이블 없음

## 🚨 중요 사항

### 데이터 제약 사항
1. **야구 외 스포츠**: 상세 통계 테이블이 없어 WDL 데이터만 활용
2. **실시간 데이터**: 경기 중 실시간 업데이트는 지원하지 않음
3. **선수 개별 통계**: 팀 단위 통계만 제공

### 분석 신뢰도
- **야구**: 풍부한 데이터로 높은 신뢰도 (3.0-4.0/4)
- **축구**: 중간 신뢰도 (2.5-3.5/4)
- **농구/배구**: 제한적 데이터로 중간 신뢰도 (2.0-3.0/4)

## 🔮 향후 확장 계획

### 1. 데이터 확장
- 축구/농구/배구용 상세 통계 테이블 추가
- 선수 개별 통계 데이터 통합
- 실시간 경기 데이터 연동

### 2. 분석 고도화
- 머신러닝 모델 통합
- 예측 정확도 향상
- 시각화 기능 추가

### 3. 새로운 스포츠
- 테니스, 골프 등 개별 스포츠 지원
- e스포츠 분석 기능
- 국제 대회 분석

## 📞 문의 및 지원

시스템 사용 중 문제가 발생하거나 새로운 기능이 필요한 경우:

1. **테스트 실행**: `python test_multi_sport_system.py`
2. **로그 확인**: `logs/multi_sport_analytics.log`
3. **데이터베이스 상태 확인**: Supabase 콘솔에서 테이블 상태 점검

---

**실제 데이터를 기반으로 한 정확하고 신뢰할 수 있는 멀티 스포츠 분석 시스템입니다! 🎯🏆**
