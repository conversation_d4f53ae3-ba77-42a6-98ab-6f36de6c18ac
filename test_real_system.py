#!/usr/bin/env python3
"""
Test script for the real baseball analysis system.

This script tests the integration with actual Supabase data
and verifies that all components work correctly.
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config.config as global_config
from baseball_analytics.infrastructure.supabase_mcp_repository import SupabaseMCPRepository
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import LLMSettings
from baseball_analytics.agents.real_baseball_agent import RealAnalysisService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_database_connection():
    """Test Supabase database connection."""
    print("🔍 Testing database connection...")
    
    try:
        repository = SupabaseMCPRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        # Test basic query
        target_matches = repository.get_sns_target_matches()
        print(f"✅ Database connection successful")
        print(f"📊 Found {len(target_matches)} target matches")
        
        if target_matches:
            # Test detailed data retrieval
            match_data = target_matches[0]
            match_id = match_data["match_id"]
            print(f"🎯 Testing data retrieval for match: {match_id}")
            
            # Test match data
            match = repository.get_match_by_id(match_id)
            if match:
                print(f"   ⚾ Match: {match.home_team.name} vs {match.away_team.name}")
            
            # Test WDL data
            h2h_summary = repository.get_wdl_data(match_id, 'h2h', 'summary')
            print(f"   📈 H2H summary: {'✅' if h2h_summary else '❌'}")
            
            # Test content data
            h2h_content = repository.get_h2h_content(match_id)
            print(f"   📝 H2H content: {'✅' if h2h_content else '❌'}")
            
            # Test betting data
            betting_picks = repository.get_betting_picks(match_id)
            print(f"   💰 Betting picks: {'✅' if betting_picks else '❌'}")
            
            return True, match_id
        else:
            print("⚠️  No target matches found")
            return True, None
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False, None


def test_llm_connection():
    """Test LLM API connection."""
    print("\n🤖 Testing LLM connection...")
    
    try:
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Test simple query
        test_response = llm.invoke("안녕하세요. 간단한 테스트입니다.")
        print(f"✅ LLM connection successful")
        print(f"🔤 Test response: {str(test_response)[:50]}...")
        
        return True, llm
        
    except Exception as e:
        print(f"❌ LLM connection failed: {e}")
        return False, None


def test_analysis_system(match_id: str):
    """Test the complete analysis system."""
    print(f"\n⚾ Testing analysis system with match: {match_id}")
    
    try:
        # Setup components
        repository = SupabaseMCPRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Create analysis service
        analysis_service = RealAnalysisService(llm, repository)
        
        # Perform analysis
        print("🔄 Running analysis...")
        analysis = analysis_service.analyze_specific_match(match_id)
        
        print("✅ Analysis completed successfully")
        print(f"📊 Match: {analysis.home_team} vs {analysis.away_team}")
        print(f"🎯 Prediction: {analysis.final_prediction}")
        print(f"💡 Recommendation: {analysis.recommendation}")
        print(f"📈 Confidence: {analysis.confidence:.2f}")
        print(f"🏷️  Hashtags: {len(analysis.hashtags)} generated")
        
        # Test social media formatting
        social_content = analysis_service.format_for_social_media(analysis)
        print(f"📱 Social media content: {len(social_content)} characters")
        
        return True, analysis
        
    except Exception as e:
        print(f"❌ Analysis system test failed: {e}")
        return False, None


def test_batch_analysis():
    """Test batch analysis of multiple matches."""
    print("\n📊 Testing batch analysis...")
    
    try:
        # Setup components
        repository = SupabaseMCPRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Create analysis service
        analysis_service = RealAnalysisService(llm, repository)
        
        # Perform batch analysis
        print("🔄 Running batch analysis...")
        analyses = analysis_service.analyze_upcoming_matches()
        
        print(f"✅ Batch analysis completed")
        print(f"📊 Analyzed {len(analyses)} matches")
        
        for i, analysis in enumerate(analyses[:3], 1):  # Show first 3
            print(f"   {i}. {analysis.home_team} vs {analysis.away_team}")
            print(f"      🎯 {analysis.final_prediction}")
            print(f"      📈 Confidence: {analysis.confidence:.2f}")
        
        return True, analyses
        
    except Exception as e:
        print(f"❌ Batch analysis test failed: {e}")
        return False, None


def main():
    """Run all tests."""
    print("🚀 Starting Real Baseball Analysis System Tests")
    print("=" * 60)
    
    # Test 1: Database connection
    db_ok, test_match_id = test_database_connection()
    if not db_ok:
        print("❌ Database tests failed. Please check your Supabase configuration.")
        return False
    
    # Test 2: LLM connection
    llm_ok, llm = test_llm_connection()
    if not llm_ok:
        print("❌ LLM tests failed. Please check your API keys.")
        return False
    
    # Test 3: Analysis system (if we have a test match)
    if test_match_id:
        analysis_ok, analysis = test_analysis_system(test_match_id)
        if not analysis_ok:
            print("❌ Analysis system tests failed.")
            return False
        
        # Test 4: Batch analysis
        batch_ok, batch_analyses = test_batch_analysis()
        if not batch_ok:
            print("❌ Batch analysis tests failed.")
            return False
    else:
        print("⚠️  Skipping analysis tests (no target matches available)")
    
    print("\n🎉 All tests completed successfully!")
    print("=" * 60)
    print("✅ Database connection: OK")
    print("✅ LLM connection: OK")
    if test_match_id:
        print("✅ Analysis system: OK")
        print("✅ Batch analysis: OK")
    print("\n🚀 System is ready for use!")
    
    # Show usage examples
    print("\n📖 Usage Examples:")
    print("=" * 60)
    print("# List available matches:")
    print("python run_real_analysis.py list")
    print()
    if test_match_id:
        print("# Analyze specific match:")
        print(f"python run_real_analysis.py analyze {test_match_id}")
        print()
        print("# Analyze with social media format:")
        print(f"python run_real_analysis.py analyze {test_match_id} --social-media --hashtags")
        print()
    print("# Batch analyze all matches:")
    print("python run_real_analysis.py batch")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
