"""
Supabase 데이터베이스 접근 모듈
"""
from datetime import datetime
from pathlib import Path

import pytz
from dotenv import load_dotenv
from supabase import create_client

from config.config import SUPABASE_KEY, SUPABASE_URL
from utils.logger import get_logger
from utils.match_scheduler import is_within_creation_window

# 로깅 설정
logger = get_logger('sportic_sns.database')

# 환경변수 로드
config_path = Path(__file__).resolve().parent.parent / 'config' / '.env'
load_dotenv(dotenv_path=config_path)

# Supabase 클라이언트 초기화 (Proto용)
proto_url, proto_key = SUPABASE_URL, SUPABASE_KEY

if not proto_url or not proto_key:
    logger.error("Supabase 설정이 없습니다. 환경변수를 확인하세요.")
    supabase = None
else:
    supabase = create_client(proto_url, proto_key)


def get_match_data(match_id, fields=None):
    """특정 match_id의 경기 데이터 조회"""
    if not match_id:
        logger.warning("매치 ID가 제공되지 않았습니다.")
        return None
    select_str = '*' if not fields else ','.join(fields)
    try:
        response = supabase.table('target_games').select(select_str).eq('match_id', match_id).execute()
        if response.data:
            return response.data[0]
        logger.warning(f"매치 ID {match_id}에 대한 데이터가 없습니다.")
        return None
    except Exception as e:
        logger.error(f"매치 데이터 조회 중 오류: {str(e)}")
        return None


def get_sportic_content(match_id, fields=None):
    """특정 match_id의 sportic_contents 데이터 조회"""
    if not match_id:
        logger.warning("매치 ID가 제공되지 않았습니다.")
        return None
    select_str = '*' if not fields else ','.join(fields)
    try:
        response = supabase.table('sportic_contents').select(select_str).eq('match_id', match_id).execute()
        if response.data:
            return response.data[0]
        logger.warning(f"매치 ID {match_id}에 대한 sportic_contents 데이터가 없습니다.")
        return None
    except Exception as e:
        logger.error(f"sportic_contents 조회 중 오류: {str(e)}")
        return None


def get_wdl_data(match_id, mode='h2h', data_type='summary'):
    """승무패 관련 데이터 조회"""
    if not match_id:
        logger.warning(f"{mode} {data_type} 조회 실패: 매치 ID 없음")
        return None
    field_name = f"{mode}_wdl_{data_type}"
    try:
        response = supabase.table('sportic_contents').select(field_name).eq('match_id', match_id).execute()
        if response.data and field_name in response.data[0]:
            return response.data[0][field_name]
        logger.warning(f"match_id={match_id}의 {field_name} 데이터가 없습니다.")
        return None
    except Exception as e:
        logger.error(f"{mode} WDL {data_type} 조회 중 오류: {str(e)}")
        return None


def get_content_for_match(match_id):
    """특정 match_id의 naver_text 또는 h2h_content 반환"""
    if not match_id:
        logger.warning("매치 ID가 제공되지 않았습니다.")
        return None
    try:
        response = supabase.table('sportic_sns').select('naver_text').eq('match_id', match_id).execute()
        if response.data and 'naver_text' in response.data[0]:
            naver_text = response.data[0]['naver_text']
            if isinstance(naver_text, dict) and 'ko' in naver_text:
                return naver_text['ko']
            return naver_text
        # naver_text가 없으면 h2h_content 조회
        content_response = supabase.table('sportic_contents').select('h2h_content').eq('match_id', match_id).execute()
        if content_response.data and 'h2h_content' in content_response.data[0]:
            h2h_content = content_response.data[0]['h2h_content']
            if isinstance(h2h_content, dict):
                return {k: v for k, v in h2h_content.items() if v}
            return h2h_content
        logger.warning(f"매치 ID {match_id}에 대한 콘텐츠를 찾을 수 없습니다.")
        return None
    except Exception as e:
        logger.error(f"매치 ID {match_id}의 콘텐츠 조회 중 오류: {str(e)}")
        return None


def get_sns_target_matches():
    """
    공통 타겟 경기 호출:
    - naver_posted 여부와 관계없이
    - 텍스트(콘텐츠) 존재
    - 경기 생성 가능 시간 윈도우(스포츠별) 내에 있는 경기만 반환
    """
    tz = pytz.timezone('Asia/Seoul')
    now = datetime.now(tz)
    response = supabase.rpc("get_sns_target_matches").execute()
    matches = []
    for row in response.data:
        if is_within_creation_window(row, now, tz):
            matches.append(row)
    return matches


def get_match_by_id(match_id):
    """ID로 경기 상세 정보 조회 (target_games + sportic_contents 병합)"""
    match_data = get_match_data(match_id)
    if not match_data:
        return None
    sportic_data = get_sportic_content(match_id, fields=[
        'h2h_content', 'h2h_wdl_summary', 'home_wdl_summary', 'away_wdl_summary',
        'h2h_wdl_matches', 'home_wdl_matches', 'away_wdl_matches'])
    if sportic_data:
        for key, value in sportic_data.items():
            if value is not None:
                match_data[key] = value
    return match_data


def get_tags_from_sportic_sns(match_id):
    """특정 match_id의 tags.ko 필드만 반환"""
    try:
        response = supabase.table('sportic_sns').select('tags').eq('match_id', match_id).execute()
        if response.data:
            tags_data = response.data[0].get('tags', {})
            if isinstance(tags_data, dict):
                return tags_data.get('ko', [])
        return []
    except Exception as e:
        logger.error(f"태그 가져오기 실패: {str(e)}")
        return []


def get_naver_posted_status(match_id: str) -> bool:
    """특정 match_id의 네이버 포스팅 상태 확인"""
    try:
        response = supabase.table('sportic_sns').select('naver_posted').eq('match_id', match_id).execute()
        if response.data:
            return response.data[0].get('naver_posted') == 'Y'
        return False
    except Exception as e:
        logger.error(f"네이버 포스팅 상태 확인 중 오류: {str(e)}")
        return False


def update_naver_posted_status(match_id):
    """네이버 포스팅 완료 시 naver_posted를 'Y'로 업데이트"""
    try:
        response = supabase.table('sportic_sns').update({'naver_posted': 'Y'}).eq('match_id', match_id).execute()
        return bool(response.data and len(response.data) > 0)
    except Exception as e:
        logger.error(f"네이버 포스팅 상태 업데이트 중 오류: {str(e)}")
        return False


def update_naver_post_content(match_id, naver_post_content):
    """네이버 포스트 콘텐츠를 sportic_sns 테이블에 저장"""
    try:
        response = supabase.table('sportic_sns').update({'naver_post': naver_post_content}).eq('match_id', match_id).execute()
        return bool(response.data and len(response.data) > 0)
    except Exception as e:
        logger.error(f"네이버 포스트 콘텐츠 업데이트 중 오류: {str(e)}")
        return False


def update_data_url(match_id: str, image_url: str) -> bool:
    try:
        # 먼저 기존 data_url이 존재하는지 확인
        existing = supabase.table("sportic_sns").select("data_url").eq("match_id", match_id).execute()
        if existing.data and existing.data[0].get("data_url"):
            logger.info(f"match_id {match_id} 이미 data_url 존재, 업데이트 건너뜀")
            return False
        response = supabase.table("sportic_sns") \
            .update({"data_url": image_url}) \
            .eq("match_id", match_id) \
            .execute()
        return bool(response.data and len(response.data) > 0)
    except Exception as e:
        logger.error(f"data_url 업데이트 중 오류: {e}")
        return False


# 이미지 파일을 Supabase 스토리지에 업로드하고 data_url을 업데이트하는 함수
def upload_image_to_storage(match_id: str, image_data: bytes, bucket_name: str = 'image') -> bool:
    """
    Uploads an image to Supabase storage and updates the data_url in the database.
    - match_id: the match identifier
    - image_data: raw bytes of the image
    - bucket_name: your Supabase storage bucket name
    Returns True if the database update succeeds, False otherwise.
    """
    try:
        # 1. Upload file to storage
        storage_bucket = supabase.storage.from_(bucket_name)
        file_path = f"{match_id}.png"
        storage_bucket.upload(file_path, image_data)

        # 2. Get public URL
        url_response = storage_bucket.get_public_url(file_path)
        public_url = url_response.get('publicURL')
        if not public_url:
            logger.error(f"Public URL not found for {file_path}")
            return False

        # 3. Update database record
        updated = update_data_url(match_id, public_url)
        if updated:
            logger.info(f"Updated data_url for match_id {match_id} to {public_url}")
        else:
            logger.info(f"Skipped or failed to update data_url for match_id {match_id}")
        return updated

    except Exception as e:
        logger.error(f"Error in upload_image_to_storage for {match_id}: {e}")
        return False


def get_unposted_matches():
    """
    naver_posted가 'Y'가 아닌 모든 경기 반환 (오늘의 경기용)
    """
    try:
        response = supabase.table('sportic_pick').select('*').or_('naver_posted.is.null,naver_posted.neq.Y').execute()
        return response.data if response.data else []
    except Exception as e:
        logger.error(f"미포스팅 경기 조회 오류: {str(e)}")
        return []


def get_all_unposted_sns():
    """
    sportic_sns 테이블에서 naver_posted가 'Y'가 아닌 모든 row 반환
    """
    response = supabase.table('sportic_sns').select('*').neq('naver_posted', 'Y').execute()
    return response.data if response.data else []


# ---- NEW: generator that yields one target match at a time ----

def iter_sns_target_matches():
    """조건에 맞는 타겟 경기를 하나씩 yield 합니다.

    Examples
    ---------
    >>> for match in iter_sns_target_matches():
    ...     process(match)
    """
    tz = pytz.timezone("Asia/Seoul")
    now = datetime.now(tz)

    try:
        response = supabase.rpc(
            "get_sns_target_matches"
        ).execute()
        if not response.data:
            return

        for row in response.data:
            if is_within_creation_window(row, now, tz):
                yield row
    except Exception as e:
        logger.error("iter_sns_target_matches 오류: %s", e)
        return

# ---------------------------------------------------------------

# ---- Baseball team stats helpers -------------------------------------------

BASEBALL_KEYS = {"baseball", "야구", "bseball"}

def get_team_stats(match_id):
    """team_stats 테이블에서 match_id 에 해당하는 모든 레코드 반환"""
    try:
        resp = (
            supabase.table("team_stats")
            .select("*")
            .eq("match_id", match_id)
            .execute()
        )
        return resp.data if resp.data else []
    except Exception as exc:
        logger.error("team_stats 조회 오류: %s", exc)
        return []


def iter_baseball_team_stats():
    """타겟 경기 중 스포츠가 야구인 경우 team_stats 를 한 경기씩 yield.

    Yields
    ------
    tuple(dict, list): (match_row, team_stats_rows)
    """
    for match in iter_sns_target_matches():
        sport = match.get("sports", "").lower()
        if sport in BASEBALL_KEYS:
            stats = get_team_stats(match["match_id"])
            yield match, stats

# -----------------------------------------------------------------------------
