"""
Supabase 데이터베이스 구조 탐색 스크립트
- 모든 테이블 목록
- 모든 함수/프로시저 목록  
- 테이블 스키마 정보
"""
import json

from db.database import supabase
from utils.logger import get_logger

logger = get_logger(__name__)


def get_all_tables():
    """모든 테이블 목록 조회"""
    print("\n" + "="*60)
    print("📊 데이터베이스 테이블 목록")
    print("="*60)
    
    try:
        # PostgreSQL 시스템 테이블에서 사용자 테이블 조회
        response = supabase.rpc('exec_sql', {
            'query': '''
                SELECT table_name, table_schema, table_type
                FROM information_schema.tables 
                WHERE table_schema IN ('public', 'auth', 'storage')
                ORDER BY table_schema, table_name;
            '''
        }).execute()
        
        if response.data:
            current_schema = None
            for row in response.data:
                schema = row.get('table_schema', 'unknown')
                table_name = row.get('table_name', 'unknown')
                table_type = row.get('table_type', 'unknown')
                
                if schema != current_schema:
                    print(f"\n🗂️  스키마: {schema}")
                    current_schema = schema
                
                print(f"   📋 {table_name} ({table_type})")
        else:
            print("❌ 테이블 정보를 가져올 수 없습니다.")
            
    except Exception as e:
        print(f"❌ 테이블 조회 오류: {e}")


def get_all_functions():
    """모든 함수/프로시저 목록 조회"""
    print("\n" + "="*60)
    print("⚙️  데이터베이스 함수/프로시저 목록")
    print("="*60)
    
    try:
        response = supabase.rpc('exec_sql', {
            'query': '''
                SELECT 
                    routine_name,
                    routine_schema,
                    routine_type,
                    data_type as return_type,
                    routine_definition
                FROM information_schema.routines 
                WHERE routine_schema IN ('public', 'auth', 'storage')
                ORDER BY routine_schema, routine_name;
            '''
        }).execute()
        
        if response.data:
            current_schema = None
            for row in response.data:
                schema = row.get('routine_schema', 'unknown')
                name = row.get('routine_name', 'unknown')
                routine_type = row.get('routine_type', 'unknown')
                return_type = row.get('return_type', 'unknown')
                
                if schema != current_schema:
                    print(f"\n🗂️  스키마: {schema}")
                    current_schema = schema
                
                print(f"   🔧 {name}() - {routine_type} (반환: {return_type})")
        else:
            print("❌ 함수 정보를 가져올 수 없습니다.")
            
    except Exception as e:
        print(f"❌ 함수 조회 오류: {e}")


def get_table_columns(table_name, schema='public'):
    """특정 테이블의 컬럼 정보 조회"""
    try:
        response = supabase.rpc('exec_sql', {
            'query': f'''
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    character_maximum_length
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' 
                AND table_schema = '{schema}'
                ORDER BY ordinal_position;
            '''
        }).execute()
        
        if response.data:
            print(f"\n📋 테이블: {schema}.{table_name}")
            print("-" * 50)
            for row in response.data:
                col_name = row.get('column_name', 'unknown')
                data_type = row.get('data_type', 'unknown')
                nullable = row.get('is_nullable', 'unknown')
                default = row.get('column_default', '')
                max_length = row.get('character_maximum_length', '')
                
                type_info = data_type
                if max_length:
                    type_info += f"({max_length})"
                
                nullable_info = "NULL" if nullable == "YES" else "NOT NULL"
                default_info = f" DEFAULT {default}" if default else ""
                
                print(f"   📌 {col_name:<20} {type_info:<15} {nullable_info}{default_info}")
                
        return response.data if response.data else []
        
    except Exception as e:
        print(f"❌ 테이블 {table_name} 컬럼 조회 오류: {e}")
        return []


def explore_main_tables():
    """주요 테이블들의 상세 정보 조회"""
    print("\n" + "="*60)
    print("🔍 주요 테이블 상세 정보")
    print("="*60)
    
    # 알려진 주요 테이블들
    main_tables = [
        'target_games',
        'team_stats', 
        'sportic_contents',
        'sportic_sns',
        'sportic_pick'
    ]
    
    for table_name in main_tables:
        get_table_columns(table_name)


def get_rpc_functions():
    """RPC로 호출 가능한 함수들 확인"""
    print("\n" + "="*60)
    print("🔌 RPC 호출 가능한 함수들")
    print("="*60)
    
    # 알려진 RPC 함수들 테스트
    known_functions = [
        'get_sns_target_matches',
        'exec_sql'
    ]
    
    for func_name in known_functions:
        try:
            print(f"\n🔧 함수: {func_name}")
            # 함수 존재 여부만 확인 (실제 실행하지 않음)
            print(f"   ✅ 호출 가능")
        except Exception as e:
            print(f"   ❌ 오류: {e}")


def main():
    """메인 탐색 함수"""
    print("🚀 Supabase 데이터베이스 구조 탐색 시작")
    print(f"📡 연결 상태: {'✅ 연결됨' if supabase else '❌ 연결 실패'}")
    
    if not supabase:
        print("❌ Supabase 연결이 필요합니다.")
        return
    
    # 1. 모든 테이블 목록
    get_all_tables()
    
    # 2. 모든 함수 목록  
    get_all_functions()
    
    # 3. 주요 테이블 상세 정보
    explore_main_tables()
    
    # 4. RPC 함수들
    get_rpc_functions()
    
    print("\n" + "="*60)
    print("✅ 데이터베이스 구조 탐색 완료")
    print("="*60)


if __name__ == "__main__":
    main() 