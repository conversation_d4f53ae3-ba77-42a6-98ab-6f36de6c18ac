#!/usr/bin/env python3
"""
Comprehensive Test Suite for Multi-Sport Analytics Platform.

This script tests all components of the unified sports analytics system:
- Configuration and database connectivity
- LLM providers and model initialization
- Sport-specific agents and analysis quality
- Integration testing with real data
"""

import sys
import logging
from typing import Dict, List, Any

from sports_analytics.config import get_settings
from sports_analytics.infrastructure.llm_providers import setup_llm_manager
from sports_analytics.infrastructure.database import (
    get_sns_target_matches, get_match_data, get_team_stats, 
    get_wdl_data, get_sport_type
)
from sports_analytics.agents.sports_agents import UnifiedAnalysisService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_configuration():
    """Test configuration and settings."""
    print("🔧 Testing Configuration...")
    
    try:
        settings = get_settings()
        
        # Test database settings
        if not settings.database.supabase_url:
            raise ValueError("Supabase URL not configured")
        if not settings.database.supabase_key:
            raise ValueError("Supabase key not configured")
        
        print(f"   ✅ Database URL: {settings.database.supabase_url[:30]}...")
        print(f"   ✅ Database configured")
        
        # Test LLM settings
        if not any([settings.llm.openai_api_key, settings.llm.google_api_key]):
            raise ValueError("No LLM API keys configured")
        
        print(f"   ✅ LLM Provider: {settings.llm.default_provider}")
        print(f"   ✅ LLM Model: {settings.llm.default_model}")
        
        return True, settings
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False, None


def test_database_connectivity():
    """Test database connectivity and data availability."""
    print("\n📊 Testing Database Connectivity...")
    
    try:
        # Test basic connectivity
        target_matches = get_sns_target_matches()
        print(f"   ✅ Database connection successful")
        print(f"   📈 Found {len(target_matches)} target matches")
        
        if not target_matches:
            print("   ⚠️  No target matches found")
            return True, {}
        
        # Group by sport and test data availability
        sport_data = {}
        test_matches = {}
        
        for match_data in target_matches:
            sport = get_sport_type(match_data)
            sport_name = {
                'baseball': '⚾ 야구',
                'soccer': '⚽ 축구',
                'basketball': '🏀 농구',
                'volleyball': '🏐 배구'
            }.get(sport, f'🏆 {sport}')
            
            if sport_name not in sport_data:
                sport_data[sport_name] = 0
                test_matches[sport_name] = match_data["match_id"]
            sport_data[sport_name] += 1
        
        print("\n   📊 Sport distribution:")
        for sport, count in sport_data.items():
            print(f"      {sport}: {count}개")
        
        # Test data availability for each sport
        print("\n   🔍 Testing data availability:")
        for sport_name, match_id in test_matches.items():
            print(f"\n      {sport_name} ({match_id}):")
            
            # Test basic match data
            match_data = get_match_data(match_id)
            if match_data:
                home_team = match_data.get('home_team', 'Unknown')
                away_team = match_data.get('away_team', 'Unknown')
                print(f"         ✅ Match data: {home_team} vs {away_team}")
            else:
                print(f"         ❌ Match data: Failed")
                continue
            
            # Test WDL data
            h2h_wdl = get_wdl_data(match_id, 'h2h', 'summary')
            home_wdl = get_wdl_data(match_id, 'home', 'summary')
            away_wdl = get_wdl_data(match_id, 'away', 'summary')
            print(f"         📈 H2H WDL: {'✅' if h2h_wdl else '❌'}")
            print(f"         🏠 Home WDL: {'✅' if home_wdl else '❌'}")
            print(f"         🚌 Away WDL: {'✅' if away_wdl else '❌'}")
            
            # Test team_stats for baseball
            sport = get_sport_type(match_data)
            if sport == "baseball":
                team_stats = get_team_stats(match_id)
                print(f"         ⚾ Team stats: {'✅' if team_stats else '❌'} ({len(team_stats)} records)")
        
        return True, test_matches
        
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        return False, {}


def test_llm_connectivity(settings):
    """Test LLM connectivity and model initialization."""
    print("\n🤖 Testing LLM Connectivity...")
    
    try:
        llm_manager = setup_llm_manager(settings.llm)
        llm = llm_manager.get_model()
        
        # Test simple query
        test_response = llm.invoke("안녕하세요. 간단한 테스트입니다.")
        response_text = str(test_response.content) if hasattr(test_response, 'content') else str(test_response)
        
        print(f"   ✅ LLM connection successful")
        print(f"   🔤 Test response: {response_text[:50]}...")
        
        return True, llm
        
    except Exception as e:
        print(f"   ❌ LLM test failed: {e}")
        return False, None


def test_sport_agents(llm, test_matches):
    """Test sport-specific agents."""
    print("\n⚽ Testing Sport-Specific Agents...")
    
    try:
        analysis_service = UnifiedAnalysisService(llm)
        
        # Test each sport
        for sport_name, match_id in test_matches.items():
            print(f"\n   🔄 Testing {sport_name} analysis ({match_id})...")
            
            try:
                analysis = analysis_service.analyze_match(match_id)
                
                if "error" in analysis:
                    print(f"      ❌ Analysis failed: {analysis['error']}")
                    continue
                
                print(f"      ✅ Analysis completed")
                print(f"      📊 Match: {analysis.get('home_team', 'Unknown')} vs {analysis.get('away_team', 'Unknown')}")
                print(f"      🏆 Sport: {analysis.get('sport', 'Unknown')}")
                print(f"      🔍 Analysis type: {analysis.get('analysis_type', 'Unknown')}")
                
                # Check sport-specific analysis
                if analysis.get('analysis_type') == 'baseball_rich':
                    print(f"      ⚾ Pitcher analysis: {'✅' if analysis.get('pitcher_analysis') else '❌'}")
                    print(f"      🏏 Team analysis: {'✅' if analysis.get('team_analysis') else '❌'}")
                else:
                    print(f"      🏆 Sport analysis: {'✅' if analysis.get('sport_analysis') else '❌'}")
                
                print(f"      🔄 H2H analysis: {'✅' if analysis.get('h2h_analysis') else '❌'}")
                print(f"      🏠 Home/Away analysis: {'✅' if analysis.get('home_away_analysis') else '❌'}")
                
            except Exception as e:
                print(f"      ❌ Analysis failed: {e}")
                continue
        
        return True
        
    except Exception as e:
        print(f"   ❌ Agent test failed: {e}")
        return False


def test_batch_analysis(llm):
    """Test batch analysis functionality."""
    print("\n📊 Testing Batch Analysis...")
    
    try:
        analysis_service = UnifiedAnalysisService(llm)
        
        # Get target matches (limit to first 3 for testing)
        target_matches = analysis_service.get_target_matches()[:3]
        
        if not target_matches:
            print("   ⚠️  No target matches for batch testing")
            return True
        
        print(f"   🔄 Running batch analysis on {len(target_matches)} matches...")
        
        results = []
        for match_data in target_matches:
            try:
                match_id = match_data.get("match_id")
                if match_id:
                    analysis = analysis_service.analyze_match(match_id)
                    if "error" not in analysis:
                        results.append(analysis)
            except Exception as e:
                logger.error(f"Error in batch analysis: {e}")
                continue
        
        print(f"   ✅ Batch analysis completed")
        print(f"   📊 Analyzed {len(results)} matches successfully")
        
        # Group by sport
        sport_counts = {}
        for analysis in results:
            sport = analysis.get('sport', 'unknown')
            sport_counts[sport] = sport_counts.get(sport, 0) + 1
        
        print("\n   📈 Analysis results by sport:")
        for sport, count in sport_counts.items():
            sport_name = {
                'baseball': '⚾ 야구',
                'soccer': '⚽ 축구',
                'basketball': '🏀 농구',
                'volleyball': '🏐 배구'
            }.get(sport, f'🏆 {sport}')
            print(f"      {sport_name}: {count}개")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Batch analysis test failed: {e}")
        return False


def test_integration():
    """Run comprehensive integration test."""
    print("\n🔗 Testing System Integration...")
    
    try:
        # Test full workflow
        settings = get_settings()
        llm_manager = setup_llm_manager(settings.llm)
        llm = llm_manager.get_model()
        analysis_service = UnifiedAnalysisService(llm)
        
        # Get one match from each sport if available
        target_matches = analysis_service.get_target_matches()
        sport_matches = {}
        
        for match_data in target_matches:
            sport = get_sport_type(match_data)
            if sport not in sport_matches:
                sport_matches[sport] = match_data["match_id"]
        
        print(f"   🔄 Testing integration with {len(sport_matches)} sports...")
        
        success_count = 0
        for sport, match_id in sport_matches.items():
            try:
                analysis = analysis_service.analyze_match(match_id)
                if "error" not in analysis:
                    success_count += 1
                    print(f"      ✅ {sport}: Integration successful")
                else:
                    print(f"      ❌ {sport}: {analysis['error']}")
            except Exception as e:
                print(f"      ❌ {sport}: {e}")
        
        print(f"   📊 Integration success rate: {success_count}/{len(sport_matches)}")
        
        return success_count == len(sport_matches)
        
    except Exception as e:
        print(f"   ❌ Integration test failed: {e}")
        return False


def main():
    """Run comprehensive test suite."""
    print("🚀 Multi-Sport Analytics Platform - Comprehensive Test Suite")
    print("=" * 80)
    
    test_results = []
    
    # Test 1: Configuration
    config_ok, settings = test_configuration()
    test_results.append(("Configuration", config_ok))
    
    if not config_ok:
        print("\n❌ Configuration tests failed. Cannot proceed.")
        return False
    
    # Test 2: Database connectivity
    db_ok, test_matches = test_database_connectivity()
    test_results.append(("Database", db_ok))
    
    if not db_ok:
        print("\n❌ Database tests failed. Cannot proceed.")
        return False
    
    # Test 3: LLM connectivity
    llm_ok, llm = test_llm_connectivity(settings)
    test_results.append(("LLM", llm_ok))
    
    if not llm_ok:
        print("\n❌ LLM tests failed. Cannot proceed.")
        return False
    
    # Test 4: Sport agents (if we have test matches)
    if test_matches:
        agents_ok = test_sport_agents(llm, test_matches)
        test_results.append(("Sport Agents", agents_ok))
        
        # Test 5: Batch analysis
        batch_ok = test_batch_analysis(llm)
        test_results.append(("Batch Analysis", batch_ok))
        
        # Test 6: Integration
        integration_ok = test_integration()
        test_results.append(("Integration", integration_ok))
    else:
        print("\n⚠️  Skipping agent tests (no target matches available)")
    
    # Summary
    print("\n🎯 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! System is ready for use.")
        print("\n📖 Usage Examples:")
        print("=" * 80)
        print("# List available matches:")
        print("python -m sports_analytics.main list")
        print()
        print("# Analyze specific match:")
        print("python -m sports_analytics.main analyze <match_id>")
        print()
        print("# Batch analyze all matches:")
        print("python -m sports_analytics.main batch")
        
        return True
    else:
        print(f"\n❌ {total - passed} tests failed. Please check the configuration and try again.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
