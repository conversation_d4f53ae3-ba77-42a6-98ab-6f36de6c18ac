"""Baseball stats analysis agent using LangChain.

Usage example
-------------
python -m src.baseball_analyzer --model openai --match_id BSW025073025
"""
from __future__ import annotations

import argparse
from typing import List

from langchain.prompts import ChatPromptTemplate
from langchain_core.language_models import BaseChatModel  # noqa: F401

from db.database import (get_match_by_id, get_team_stats,
                         iter_baseball_team_stats)

# Conditional imports to avoid heavy deps when not used
try:
    from langchain_openai import ChatOpenAI  # type: ignore
except ImportError:  # pragma: no cover
    ChatOpenAI = None  # type: ignore

try:
    from langchain_google_genai import ChatGoogleGenerativeAI  # type: ignore
except ImportError:  # pragma: no cover
    ChatGoogleGenerativeAI = None  # type: ignore


def build_llm(provider: str) -> BaseChatModel:
    """Return a LangChain chat model for the given provider.

    provider: "openai" or "gemini"
    """
    if provider == "openai":
        if ChatOpenAI is None:
            raise ImportError("langchain_openai not installed")
        # Assume env var OPENAI_API_KEY is set.
        return ChatOpenAI(
            model="gpt-4.1-mini",
            temperature=0.3,
        )
    if provider == "gemini":
        if ChatGoogleGenerativeAI is None:
            raise ImportError("langchain_google_genai not installed")
        # Assume env var GOOGLE_API_KEY is set.
        return ChatGoogleGenerativeAI(
            model="gemini-2.5-pro",
            temperature=0.3,
        )
    raise ValueError(f"Unknown provider: {provider}")


def format_prompt(match: dict, stats: List[dict]) -> str:
    """Compose a prompt for the LLM with match info and stats."""
    prompt_template = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                (
                    "You are a sports analytics assistant specialising in "
                    "Korean baseball. Analyse the following match context "
                    "and team statistics, and produce key insights and "
                    "predictions. 또한 소셜 미디어 하이라이트 포인트를 "
                    "한국어로 제안하십시오."
                ),
            ),
            (
                "human",
                (
                    "경기 정보:\n{match}\n\n팀 통계(최근 경기 포함):\n{stats}\n\n요청: "
                    "1) 핵심 분석 3줄 2) 승부 예측(예: 홈 60% 원정 40%) "
                    "3) 해시태그 5개를 # 형식으로 제시하십시오."
                ),
            ),
        ]
    )
    return prompt_template.format(match=match, stats=stats)


def analyze_match(match_id: str, provider: str = "openai") -> str:
    """Fetch data and run LLM analysis, returning the result string."""
    # 1. 매치 데이터 조회
    match = get_match_by_id(match_id)
    allowed = {"baseball", "야구", "bseball"}
    if not match or match.get("sports", "").lower() not in allowed:
        raise ValueError(
            "해당 match_id 가 야구 경기가 아니거나 존재하지 않습니다."
        )

    stats = get_team_stats(match_id)

    llm = build_llm(provider)
    prompt = format_prompt(match, stats)

    response = llm.invoke(prompt)
    return str(response)


# ----------------------- CLI ------------------------

def _cli():
    parser = argparse.ArgumentParser(description="Baseball match analyzer")
    parser.add_argument(
        "--match_id",
        required=False,
        help=(
            "Supabase match_id (생략 시 첫 번째 야구 경기 자동 선택)"
        ),
    )
    parser.add_argument(
        "--model",
        choices=["openai", "gemini"],
        default="openai",
        help="LLM provider to use",
    )
    args = parser.parse_args()

    match_id = args.match_id or pick_first_baseball_match_id()
    result = analyze_match(match_id, provider=args.model)
    print(result)


def pick_first_baseball_match_id() -> str:
    """iter_baseball_team_stats 제너레이터에서 가장 첫 번째 match_id 반환.

    Raises
    ------
    ValueError
        조건에 맞는 야구 경기가 없을 때
    """
    try:
        match, _stats = next(iter_baseball_team_stats())
    except StopIteration as exc:  # pragma: no cover
        raise ValueError("현재 분석 가능한 야구 경기가 없습니다.") from exc
    return match["match_id"]


if __name__ == "__main__":
    _cli() 