"""Baseball stats analysis agent using direct OpenAI API.

Usage examples
--------------
# 특정 경기 분석
python -m src.baseball_analyzer --match_id BSS025073180

# 자동으로 야구 경기 선택하여 분석
python -m src.baseball_analyzer --auto

# 여러 경기 분석
python -m src.baseball_analyzer --auto --count 3
"""
from __future__ import annotations

import argparse
import json
from typing import Any, Dict, List

from openai import OpenAI

from config import OPENAI_API_KEY
from db.database import (get_content_for_match, get_match_data,
                         get_sportic_content, get_team_stats, get_wdl_data,
                         iter_baseball_team_stats, supabase)
from utils.logger import get_logger

logger = get_logger(__name__)

# OpenAI 클라이언트 초기화
client = OpenAI(api_key=OPENAI_API_KEY) if OPENAI_API_KEY else None


def get_available_baseball_matches() -> List[Dict[str, Any]]:
    """사용 가능한 야구 경기 목록을 가져옵니다."""
    logger.info("사용 가능한 야구 경기 검색 중...")
    
    matches = []
    try:
        # iter_baseball_team_stats를 사용하여 야구 경기 찾기
        for match, stats in iter_baseball_team_stats():
            if stats:  # 통계 데이터가 있는 경기만
                matches.append({
                    'match_id': match['match_id'],
                    'home_team': match.get('home_team', 'Unknown'),
                    'away_team': match.get('away_team', 'Unknown'),
                    'match_date': match.get('match_date', 'Unknown'),
                    'stats_count': len(stats)
                })
                
        if not matches:
            # 직접 target_games에서 야구 경기 검색
            logger.info("RPC에서 야구 경기를 찾지 못했습니다. 직접 검색합니다...")
            response = (
                supabase.table('target_games')
                .select('match_id, home_team, away_team, match_date, sports')
                .ilike('sports', '%야구%')
                .limit(20)
                .execute()
            )
            
            for match in response.data:
                # team_stats가 있는지 확인
                stats = get_team_stats(match['match_id'])
                if stats:
                    matches.append({
                        'match_id': match['match_id'],
                        'home_team': match.get('home_team', 'Unknown'),
                        'away_team': match.get('away_team', 'Unknown'),
                        'match_date': match.get('match_date', 'Unknown'),
                        'stats_count': len(stats)
                    })
                    
        logger.info(f"분석 가능한 야구 경기 {len(matches)}개 발견")
        return matches
        
    except Exception as e:
        logger.error(f"야구 경기 검색 중 오류: {e}")
        return []


def get_comprehensive_match_data(match_id: str) -> Dict[str, Any]:
    """경기에 대한 종합적인 데이터를 수집합니다."""
    logger.info(f"경기 {match_id}에 대한 종합 데이터 수집 시작")
    
    data = {}
    
    # 1. 기본 경기 정보
    match_info = get_match_data(match_id)
    if match_info:
        data['match_info'] = match_info
        home_team = match_info.get('home_team')
        away_team = match_info.get('away_team')
        logger.info(f"경기 정보 수집 완료: {home_team} vs {away_team}")
    else:
        logger.warning(f"경기 ID {match_id}에 대한 기본 정보를 찾을 수 없습니다.")
    
    # 2. 팀 통계 데이터
    team_stats = get_team_stats(match_id)
    if team_stats:
        data['team_stats'] = team_stats
        logger.info(f"팀 통계 데이터 수집 완료: {len(team_stats)}개 레코드")
    else:
        logger.warning(f"경기 ID {match_id}에 대한 팀 통계를 찾을 수 없습니다.")
    
    # 3. Sportic 콘텐츠 (H2H, WDL 데이터 등)
    sportic_content = get_sportic_content(match_id)
    if sportic_content:
        data['sportic_content'] = sportic_content
        logger.info("Sportic 콘텐츠 데이터 수집 완료")
    
    # 4. H2H 승무패 데이터
    h2h_summary = get_wdl_data(match_id, mode='h2h', data_type='summary')
    if h2h_summary:
        data['h2h_wdl_summary'] = h2h_summary
        logger.info("H2H 승무패 요약 데이터 수집 완료")
    
    # 5. 홈팀 승무패 데이터
    home_wdl = get_wdl_data(match_id, mode='home', data_type='summary')
    if home_wdl:
        data['home_wdl_summary'] = home_wdl
        logger.info("홈팀 승무패 데이터 수집 완료")
    
    # 6. 어웨이팀 승무패 데이터
    away_wdl = get_wdl_data(match_id, mode='away', data_type='summary')
    if away_wdl:
        data['away_wdl_summary'] = away_wdl
        logger.info("어웨이팀 승무패 데이터 수집 완료")
    
    # 7. 기존 콘텐츠 (네이버 텍스트 등)
    existing_content = get_content_for_match(match_id)
    if existing_content:
        data['existing_content'] = existing_content
        logger.info("기존 콘텐츠 수집 완료")
    
    return data


def format_data_for_analysis(data: Dict[str, Any]) -> str:
    """분석을 위해 데이터를 포맷팅합니다."""
    formatted_parts = []
    
    # 경기 기본 정보
    if 'match_info' in data:
        match_info = data['match_info']
        formatted_parts.append("=== 경기 정보 ===")
        formatted_parts.append(f"홈팀: {match_info.get('home_team', 'N/A')}")
        formatted_parts.append(f"어웨이팀: {match_info.get('away_team', 'N/A')}")
        formatted_parts.append(f"경기 날짜: {match_info.get('match_date', 'N/A')}")
        formatted_parts.append(f"경기장: {match_info.get('stadium', 'N/A')}")
        formatted_parts.append(f"스포츠: {match_info.get('sports', 'N/A')}")
        formatted_parts.append("")
    
    # 팀 통계
    if 'team_stats' in data and data['team_stats']:
        formatted_parts.append("=== 팀 통계 ===")
        for i, stat in enumerate(data['team_stats']):
            formatted_parts.append(f"통계 {i+1}:")
            formatted_parts.append(f"  팀: {stat.get('team_name', 'N/A')}")
            batting_avg = stat.get('batting_average', 'N/A')
            formatted_parts.append(f"  타율: {batting_avg}")
            formatted_parts.append(f"  홈런: {stat.get('home_runs', 'N/A')}")
            formatted_parts.append(f"  타점: {stat.get('rbi', 'N/A')}")
            formatted_parts.append(f"  득점: {stat.get('runs', 'N/A')}")
            formatted_parts.append(f"  ERA: {stat.get('era', 'N/A')}")
            formatted_parts.append("")
    
    # H2H 승무패 요약
    if 'h2h_wdl_summary' in data:
        formatted_parts.append("=== H2H 승무패 요약 ===")
        h2h_json = json.dumps(
            data['h2h_wdl_summary'], ensure_ascii=False, indent=2
        )
        formatted_parts.append(h2h_json)
        formatted_parts.append("")
    
    # 홈팀 승무패
    if 'home_wdl_summary' in data:
        formatted_parts.append("=== 홈팀 승무패 ===")
        home_json = json.dumps(
            data['home_wdl_summary'], ensure_ascii=False, indent=2
        )
        formatted_parts.append(home_json)
        formatted_parts.append("")
    
    # 어웨이팀 승무패
    if 'away_wdl_summary' in data:
        formatted_parts.append("=== 어웨이팀 승무패 ===")
        away_json = json.dumps(
            data['away_wdl_summary'], ensure_ascii=False, indent=2
        )
        formatted_parts.append(away_json)
        formatted_parts.append("")
    
    # 기존 콘텐츠
    if 'existing_content' in data:
        formatted_parts.append("=== 기존 분석 콘텐츠 ===")
        if isinstance(data['existing_content'], dict):
            content_json = json.dumps(
                data['existing_content'], ensure_ascii=False, indent=2
            )
            formatted_parts.append(content_json)
        else:
            formatted_parts.append(str(data['existing_content']))
        formatted_parts.append("")
    
    return "\n".join(formatted_parts)


def analyze_match_with_openai(match_id: str, formatted_data: str) -> str:
    """OpenAI API를 직접 사용하여 경기를 분석합니다."""
    if not client:
        raise ValueError("OpenAI API 키가 설정되지 않았습니다.")
    
    prompt = f"""
다음은 야구 경기 {match_id}에 대한 실제 데이터베이스에서 가져온 종합적인 데이터입니다.

{formatted_data}

위 데이터를 바탕으로 다음 항목들을 포함한 전문적인 야구 경기 분석을 제공해주세요:

1. **경기 개요**: 양 팀의 기본 정보와 경기 배경
2. **팀별 주요 통계 분석**: 
   - 타격 성적 (타율, 홈런, 타점 등)
   - 투수 성적 (ERA 등)
   - 최근 경기력 분석
3. **H2H (Head-to-Head) 분석**: 양 팀 간 과거 대전 기록
4. **홈/어웨이 성적 분석**: 각 팀의 홈/어웨이 경기 성적
5. **핵심 포인트**: 경기 결과에 영향을 줄 수 있는 주요 요소들
6. **예상 시나리오**: 가능한 경기 전개와 결과 예측
7. **관전 포인트**: 팬들이 주목해야 할 선수나 상황

분석은 구체적인 수치와 데이터를 근거로 하되, 일반 야구 팬들도 이해하기 쉽게 작성해주세요.
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": ("당신은 전문적인 야구 분석가입니다. "
                                "데이터를 바탕으로 정확하고 이해하기 쉬운 분석을 제공합니다.")
                },
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=2000
        )
        
        content = response.choices[0].message.content
        return content if content else "분석 결과를 생성할 수 없습니다."
        
    except Exception as e:
        logger.error(f"OpenAI API 호출 중 오류: {e}")
        raise


def analyze_match(match_id: str) -> str:
    """실제 데이터베이스에서 데이터를 가져와서 분석합니다."""
    logger.info(f"경기 {match_id} 분석 시작")
    
    # 종합적인 데이터 수집
    comprehensive_data = get_comprehensive_match_data(match_id)
    
    if not comprehensive_data:
        raise ValueError(f"match_id {match_id}에 대한 데이터를 찾을 수 없습니다.")
    
    # 데이터 포맷팅
    formatted_data = format_data_for_analysis(comprehensive_data)
    
    # OpenAI로 분석
    result = analyze_match_with_openai(match_id, formatted_data)
    
    logger.info(f"경기 {match_id} 분석 완료")
    return result


def auto_analyze_matches(count: int = 1) -> None:
    """자동으로 야구 경기를 선택하여 분석합니다."""
    print(f"\n🔍 분석 가능한 야구 경기를 검색 중...")
    
    available_matches = get_available_baseball_matches()
    
    if not available_matches:
        print("❌ 분석 가능한 야구 경기를 찾을 수 없습니다.")
        return
    
    print(f"✅ {len(available_matches)}개의 야구 경기를 발견했습니다.")
    
    # 요청된 수만큼 분석
    analyze_count = min(count, len(available_matches))
    
    for i in range(analyze_count):
        match = available_matches[i]
        match_id = match['match_id']
        
        print(f"\n{'='*80}")
        print(f"📊 경기 {i+1}/{analyze_count}: {match['home_team']} vs {match['away_team']}")
        print(f"경기 ID: {match_id}")
        print(f"경기 날짜: {match['match_date']}")
        print(f"통계 데이터: {match['stats_count']}개")
        print(f"{'='*80}")
        
        try:
            analysis = analyze_match(match_id)
            print(analysis)
            print(f"{'='*80}")
            
        except Exception as e:
            print(f"❌ 경기 {match_id} 분석 중 오류: {e}")
            continue


def main():
    """Main function to run the analyzer."""
    parser = argparse.ArgumentParser(
        description=(
            "실제 데이터베이스에서 야구 경기 데이터를 가져와 "
            "분석하고 인사이트를 생성합니다."
        )
    )
    parser.add_argument(
        "--match_id",
        type=str,
        help="분석할 경기의 match_id",
    )
    parser.add_argument(
        "--auto",
        action="store_true",
        help="자동으로 야구 경기를 선택하여 분석",
    )
    parser.add_argument(
        "--count",
        type=int,
        default=1,
        help="자동 분석 시 분석할 경기 수 (기본값: 1)",
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="분석 가능한 야구 경기 목록만 표시",
    )
    
    args = parser.parse_args()

    # 경기 목록만 표시
    if args.list:
        print("\n🔍 분석 가능한 야구 경기 목록:")
        matches = get_available_baseball_matches()
        for i, match in enumerate(matches[:10], 1):  # 최대 10개만 표시
            print(f"{i:2d}. {match['match_id']} | "
                  f"{match['home_team']} vs {match['away_team']} | "
                  f"{match['match_date']} | "
                  f"통계: {match['stats_count']}개")
        if len(matches) > 10:
            print(f"... 외 {len(matches) - 10}개 경기")
        return

    # 자동 분석
    if args.auto:
        auto_analyze_matches(args.count)
        return
    
    # 특정 경기 분석
    if not args.match_id:
        print("❌ --match_id 또는 --auto 옵션을 지정해주세요.")
        print("사용법: python -m src.baseball_analyzer --help")
        return

    try:
        print(f"\n⚾ 경기 {args.match_id} 분석을 시작합니다...\n")
        analysis = analyze_match(args.match_id)
        print("=" * 80)
        print(f"경기 {args.match_id} 분석 결과")
        print("=" * 80)
        print(analysis)
        print("=" * 80)
    except ValueError as e:
        print(f"❌ 데이터 오류: {e}")
    except Exception as e:
        print(f"❌ 예상치 못한 오류 발생: {e}")
        logger.error(f"메인 함수에서 오류 발생: {e}")


if __name__ == "__main__":
    main() 