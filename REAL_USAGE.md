# 실제 데이터 기반 야구 분석 시스템 사용법

## 개요

이 시스템은 실제 Supabase 데이터베이스의 데이터를 사용하여 SporTic365 스타일의 야구 분석을 제공합니다.

## 주요 특징

- **실제 데이터 사용**: Supabase MCP를 통한 실제 데이터베이스 연동
- **전문 에이전트**: 투수, 팀 성적, 배당률 분석 전문 AI 에이전트
- **SporTic365 스타일**: 기존 분석 형식과 동일한 출력
- **최신 LangChain**: Context7 기반 최신 라이브러리 활용
- **SOLID 원칙**: 확장 가능하고 유지보수가 쉬운 구조

## 설치 및 설정

### 1. 환경 설정

```bash
# 의존성 설치
pip install -r requirements.txt

# 환경 변수 설정 (config/config.py에서 확인)
export SUPABASE_URL="your_supabase_url"
export SUPABASE_KEY="your_supabase_key"
export OPENAI_API_KEY="your_openai_key"
export GEMINI_API_KEY="your_gemini_key"
```

### 2. 데이터베이스 구조

시스템은 다음 Supabase 테이블을 사용합니다:

- `target_games`: 경기 기본 정보
- `sportic_contents`: 분석 콘텐츠 및 WDL 데이터
- `sportic_sns`: 소셜 미디어 콘텐츠
- `sportic_pick`: 베팅 픽
- `team_stats`: 팀 통계 (선택사항)

## 사용법

### 1. 분석 대상 경기 확인

```bash
# 현재 분석 가능한 경기 목록 조회
python run_real_analysis.py list
```

출력 예시:
```
📋 분석 대상 경기 (2개)
============================================================
 1. BSW025073025
    🏟️ 키움 vs SSG
    📅 2024-06-19 19:00:00

 2. BSW025073026
    🏟️ LG vs KT
    📅 2024-06-19 19:00:00
```

### 2. 특정 경기 분석

```bash
# 기본 분석
python run_real_analysis.py analyze BSW025073025

# 소셜 미디어 포맷 포함
python run_real_analysis.py analyze BSW025073025 --social-media

# 해시태그 포함
python run_real_analysis.py analyze BSW025073025 --hashtags

# 모든 옵션 포함
python run_real_analysis.py analyze BSW025073025 --social-media --hashtags
```

### 3. 일괄 분석

```bash
# 모든 분석 대상 경기를 일괄 분석
python run_real_analysis.py batch
```

## 분석 결과 예시

```
================================================================================
🏟️ SporTic365 스타일 야구 분석
================================================================================

🏟️ 키움 히어로즈 vs SSG 랜더스
📅 2024-06-19 19:00:00
🏟️ 고척스카이돔

📊 SporTic365 요약
----------------------------------------
최근 5경기 맞대결에서 키움 3승 2패, 경기당 평균 득점은 3.2점에 불과
키움, 홈 5경기에서 평균 2.6득점·5.8실점으로 흐름 좋지 않음
SSG는 원정서 평균 5.8득점, 시즌 타율 .246·홈런 44개로 장타력 우세

✅ 경기 요약
----------------------------------------
최근 맞대결에서 키움이 3승 2패로 근소한 우위를 점하고 있지만, 
양 팀 모두 경기당 평균 득점이 낮아 투수력에 따라 승패가 갈리고 있다.

🎯 배당 인사이트
----------------------------------------
배당은 키움 2.0, SSG 1.8 수준으로 형성되어 있으며, 
시장은 SSG의 승리에 약 56% 확률을 부여하고 있다.

🔍 분석 포인트
----------------------------------------
1. 선발 투수 비교: 박주성 vs 박시후
키움 선발 박주성은 최근 3경기에서 14이닝 11실점으로 매우 불안한 모습
SSG 박시후는 시즌 ERA 3.25로 상대적으로 안정적

🔑 주요 승부 요인
----------------------------------------
1. 키움 박주성의 불안한 제구력
2. SSG 타선의 꾸준한 원정 득점력
3. 시장 배당이 반영한 승리 기대값

💡 종합 판단 및 베팅 추천
----------------------------------------
SSG의 원정 승리 가능성이 높다고 판단됩니다.

🎯 최종 추천: SSG 승
추천도: 🔴🔴🔴⚪ (3.5/4)
```

## 시스템 구조

### CAG (Context-Aware Generation) 아키텍처

1. **Context Layer** (`supabase_mcp_repository.py`)
   - 실제 Supabase 데이터 수집
   - WDL 데이터, H2H 기록, 베팅 정보 등

2. **Analysis Layer** (`real_baseball_agent.py`)
   - 전문 AI 에이전트를 통한 분석
   - 투수 분석, 팀 성적 분석, 배당률 분석

3. **Generation Layer**
   - SporTic365 스타일 콘텐츠 생성
   - 소셜 미디어 포맷 지원

### 주요 클래스

- `SupabaseMCPRepository`: 실제 데이터베이스 연동
- `RealBaseballAnalysisAgent`: 야구 분석 전문 에이전트
- `RealAnalysisService`: 분석 서비스 관리

## 확장 가능성

### 새로운 분석 요소 추가

```python
# 새로운 컨텍스트 수집기 추가
class WeatherContextCollector:
    def collect_weather_data(self, match_id: str):
        # 날씨 데이터 수집 로직
        pass

# 새로운 분석 에이전트 추가
class InjuryAnalysisAgent:
    def analyze_injury_impact(self, team_data):
        # 부상자 영향 분석 로직
        pass
```

### 다른 스포츠 지원

```python
# 축구 분석 에이전트
class SoccerAnalysisAgent(RealBaseballAnalysisAgent):
    def _create_analysis_prompt(self):
        # 축구 전용 프롬프트
        pass
```

## 트러블슈팅

### 1. 데이터베이스 연결 오류

```bash
# 연결 테스트
python -c "
from baseball_analytics.infrastructure.supabase_mcp_repository import SupabaseMCPRepository
import config.config as config
repo = SupabaseMCPRepository(config.SUPABASE_URL, config.SUPABASE_KEY)
print('Connection OK')
"
```

### 2. LLM API 오류

```bash
# API 키 확인
echo $OPENAI_API_KEY
echo $GEMINI_API_KEY
```

### 3. 분석 대상 경기가 없는 경우

- 경기 시간 창 확인 (야구: 경기 34시간 전 ~ 10분 전)
- `target_games` 테이블의 데이터 확인
- `get_sns_target_matches()` 함수 결과 확인

## 개발자 가이드

### 새로운 분석 기능 추가

1. **도메인 모델 정의** (`domain/models.py`)
2. **데이터 수집 로직** (`infrastructure/supabase_mcp_repository.py`)
3. **분석 에이전트** (`agents/real_baseball_agent.py`)
4. **프롬프트 엔지니어링** (분석 품질 향상)

### 테스트

```bash
# 단위 테스트
pytest tests/test_real_analysis.py

# 통합 테스트
pytest tests/integration/test_supabase_integration.py
```

## 성능 최적화

- **캐싱**: 분석 결과 캐싱으로 응답 속도 향상
- **배치 처리**: 여러 경기 동시 분석
- **비동기 처리**: 대용량 데이터 처리 시 활용

## 라이선스

MIT License - 자유롭게 사용, 수정, 배포 가능
