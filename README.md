# Baseball Analytics Platform

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

A modern, Context-Aware Generation (CAG) based platform for baseball analytics and content generation. This platform leverages cutting-edge AI technologies including LangChain, Pydantic v2, and multiple LLM providers to deliver comprehensive baseball analysis and automated content creation.

## 🏗️ Architecture

The platform follows a **CAG (Context-Aware Generation)** architecture with **SOLID principles**:

### CAG Layers

1. **Context Layer** (`baseball_analytics/cag/context.py`)
   - Data collection and aggregation
   - Match information, team statistics, historical data
   - Weather conditions and additional context

2. **Analysis Layer** (`baseball_analytics/cag/analysis.py`)
   - AI-powered analysis using Lang<PERSON>hain
   - Insight extraction and match predictions
   - Structured output with confidence scoring

3. **Generation Layer** (`baseball_analytics/cag/generation.py`)
   - Content generation for multiple platforms
   - Social media posts, blog articles, reports
   - Customizable tone and audience targeting

### Domain-Driven Design

```
baseball_analytics/
├── domain/           # Business entities and value objects
├── application/      # Use cases and application services
├── infrastructure/   # External dependencies and data access
├── cag/             # Context-Aware Generation framework
└── main.py          # Application entry point
```

## ✨ Features

- **🤖 AI-Powered Analysis**: Advanced baseball analysis using multiple LLM providers
- **📊 Comprehensive Statistics**: Team performance, player stats, historical trends
- **🎯 Smart Predictions**: Match outcome predictions with confidence scoring
- **📝 Content Generation**: Automated content for social media, blogs, and reports
- **🔧 Modular Architecture**: SOLID principles with dependency injection
- **⚡ High Performance**: Caching, async operations, and optimized queries
- **🧪 Well Tested**: Comprehensive unit and integration tests
- **🐳 Docker Ready**: Containerized deployment with Docker Compose
- **📈 Monitoring**: Built-in analytics and performance tracking

## 🚀 Quick Start

### Prerequisites

- Python 3.9 or higher
- Supabase account and project
- At least one LLM API key (OpenAI, Google, or Anthropic)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/baseball-analytics.git
   cd baseball-analytics
   ```

2. **Install dependencies**
   ```bash
   # Using make (recommended)
   make install-dev

   # Or using pip directly
   pip install -e ".[dev]"
   ```

3. **Configure environment**
   ```bash
   cp .env.template .env
   # Edit .env with your configuration
   ```

4. **Run tests**
   ```bash
   make test
   ```

### Configuration

Create a `.env` file with your settings:

```env
# Database Configuration
DB_SUPABASE_URL=your_supabase_url_here
DB_SUPABASE_KEY=your_supabase_key_here

# LLM Configuration
LLM_OPENAI_API_KEY=your_openai_api_key_here
LLM_DEFAULT_PROVIDER=openai
LLM_DEFAULT_MODEL=gpt-4o-mini

# Application Configuration
APP_ENVIRONMENT=development
LOG_LEVEL=INFO
```

## 📖 Usage

### Command Line Interface

The platform provides a comprehensive CLI for all operations:

```bash
# Analyze a specific match
baseball-analytics analyze BSW025073025

# Analyze and generate social media content
baseball-analytics analyze BSW025073025 --content-type social_media_post --tone enthusiastic

# Generate blog article
baseball-analytics analyze BSW025073025 --content-type blog_article --audience fans

# List upcoming matches
baseball-analytics list --days 7
```

### Python API

```python
from baseball_analytics.application.services import ApplicationServiceFactory
from baseball_analytics.infrastructure.database import SupabaseConnection, SupabaseRepository
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import get_settings

# Setup
settings = get_settings()
db_connection = SupabaseConnection(settings.database.supabase_url, settings.database.supabase_key)
repository = SupabaseRepository(db_connection)
llm_manager = setup_llm_manager(settings.llm)

# Create service
analysis_service = ApplicationServiceFactory.create_analysis_service(repository, llm_manager)

# Perform analysis
result = analysis_service.analyze_match("BSW025073025")
print(f"Confidence: {result.confidence_score:.2f}")

# Generate content
content = analysis_service.generate_content(
    result,
    "social_media_post",
    tone="enthusiastic"
)
print(content.body)
```

## 🏛️ Architecture Details

### SOLID Principles Implementation

1. **Single Responsibility Principle (SRP)**
   - Each class has one reason to change
   - `ContextManager` only handles context aggregation
   - `AnalysisEngine` only performs analysis
   - `ContentGenerator` only generates content

2. **Open/Closed Principle (OCP)**
   - New LLM providers can be added without modifying existing code
   - New content generators can be registered dynamically
   - New context collectors can be added to the aggregator

3. **Liskov Substitution Principle (LSP)**
   - All repository implementations are interchangeable
   - All LLM providers follow the same interface
   - All content generators can be substituted

4. **Interface Segregation Principle (ISP)**
   - Separate protocols for different responsibilities
   - `DataRepository` protocol for data access
   - `LLMProvider` protocol for language models
   - `ContentGenerator` protocol for content creation

5. **Dependency Inversion Principle (DIP)**
   - High-level modules depend on abstractions
   - Dependency injection throughout the application
   - Configuration-driven component selection

### CAG Framework

The Context-Aware Generation framework consists of three interconnected layers:

#### Context Layer
- **Purpose**: Collect and aggregate relevant data
- **Components**:
  - `MatchContextCollector`: Basic match information
  - `StatisticsContextCollector`: Team and player statistics
  - `HistoricalContextCollector`: Historical match data
  - `WeatherContextCollector`: Environmental conditions
- **Extensibility**: New collectors can be added via the `ContextCollector` interface

#### Analysis Layer
- **Purpose**: Process context data and generate insights
- **Components**:
  - `LangChainInsightExtractor`: Extract insights using LLM
  - `LangChainPredictionEngine`: Generate match predictions
  - `BaseballAnalysisEngine`: Orchestrate analysis workflow
- **Features**: Structured output, confidence scoring, fallback mechanisms

#### Generation Layer
- **Purpose**: Create content based on analysis results
- **Components**:
  - `SocialMediaGenerator`: Short-form content for social platforms
  - `BlogArticleGenerator`: Long-form articles and reports
  - `ContentGenerationEngine`: Content type routing and management
- **Customization**: Tone, audience, length, and format options

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DB_SUPABASE_URL` | Supabase project URL | - | ✅ |
| `DB_SUPABASE_KEY` | Supabase API key | - | ✅ |
| `LLM_OPENAI_API_KEY` | OpenAI API key | - | * |
| `LLM_GOOGLE_API_KEY` | Google API key | - | * |
| `LLM_ANTHROPIC_API_KEY` | Anthropic API key | - | * |
| `LLM_DEFAULT_PROVIDER` | Default LLM provider | `openai` | ❌ |
| `LLM_DEFAULT_MODEL` | Default model name | `gpt-4o-mini` | ❌ |
| `LOG_LEVEL` | Logging level | `INFO` | ❌ |
| `ANALYSIS_ENABLE_CACHING` | Enable result caching | `true` | ❌ |

*At least one LLM API key is required

### Advanced Configuration

```python
from baseball_analytics.infrastructure.config import ApplicationSettings

settings = ApplicationSettings(
    database=DatabaseSettings(
        supabase_url="your_url",
        supabase_key="your_key",
        cache_ttl=600  # 10 minutes
    ),
    llm=LLMSettings(
        default_provider="openai",
        default_temperature=0.2,
        default_max_tokens=2000
    ),
    analysis=AnalysisSettings(
        max_historical_matches=15,
        confidence_threshold=0.7
    )
)
```

## 📊 Data Models

### Core Domain Models

```python
# Team representation
class Team(BaseModel):
    id: str
    name: str
    short_name: str
    logo_url: Optional[str] = None

# Match information
class Match(BaseModel):
    match_id: str
    sport: SportType
    home_team: Team
    away_team: Team
    match_date: datetime
    status: MatchStatus

# Team statistics
class TeamStats(BaseModel):
    team_id: str
    match_id: str
    runs: Optional[int] = None
    hits: Optional[int] = None
    batting_average: Optional[float] = None
    era: Optional[float] = None
```

### Analysis Results

```python
# Analysis insight
class AnalysisInsight(BaseModel):
    category: str
    title: str
    content: str
    confidence: float  # 0.0 to 1.0

# Match prediction
class PredictionResult(BaseModel):
    home_win_probability: float
    away_win_probability: float
    draw_probability: float = 0.0

# Complete analysis
class AnalysisResult(BaseModel):
    match_id: str
    insights: List[AnalysisInsight]
    prediction: Optional[PredictionResult]
    key_points: List[str]
    hashtags: List[str]
    confidence_score: float
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
make test

# Run with coverage
pytest tests/ --cov=baseball_analytics --cov-report=html

# Run specific test file
pytest tests/test_domain_models.py -v

# Run tests with specific marker
pytest -m "not integration" -v
```

### Test Structure

```
tests/
├── conftest.py              # Pytest fixtures and configuration
├── test_domain_models.py    # Domain model tests
├── test_cag_context.py      # Context layer tests
├── test_cag_analysis.py     # Analysis layer tests
├── test_cag_generation.py   # Generation layer tests
├── test_infrastructure.py   # Infrastructure tests
└── integration/             # Integration tests
    ├── test_end_to_end.py
    └── test_api_integration.py
```

### Writing Tests

```python
import pytest
from baseball_analytics.domain.models import Team, Match

def test_team_creation():
    """Test creating a valid team."""
    team = Team(
        id="test_team",
        name="Test Team",
        short_name="TEST"
    )
    assert team.name == "Test Team"

@pytest.fixture
def sample_match(sample_team_home, sample_team_away):
    """Sample match fixture."""
    return Match(
        match_id="test_match",
        home_team=sample_team_home,
        away_team=sample_team_away,
        match_date=datetime(2024, 6, 18, 19, 0)
    )
```

## 🐳 Docker Deployment

### Using Docker Compose

```bash
# Build and run
docker-compose up --build

# Run specific analysis
docker-compose run baseball-analytics python -m baseball_analytics.main analyze BSW025073025

# Run in background
docker-compose up -d
```

### Custom Docker Build

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN pip install -e .

CMD ["python", "-m", "baseball_analytics.main", "--help"]
```

## 🔍 Monitoring and Analytics

### Performance Tracking

```python
from baseball_analytics.application.services import AnalyticsService

analytics = AnalyticsService()

# Track analysis performance
analytics.track_analysis_performance("match_123", duration=2.5, success=True)

# Get performance summary
summary = analytics.get_performance_summary()
print(f"Success rate: {summary['analysis']['success_rate']:.2%}")
```

### Logging Configuration

```python
# Configure structured logging
LOG_LEVEL=DEBUG
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/baseball_analytics.log
LOG_CONSOLE_ENABLED=true
```

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
   ```bash
   # Set production environment
   export APP_ENVIRONMENT=production
   export LOG_LEVEL=WARNING

   # Configure database
   export DB_SUPABASE_URL=your_production_url
   export DB_SUPABASE_KEY=your_production_key
   ```

2. **Security Configuration**
   ```bash
   # API security
   export SECURITY_RATE_LIMIT_REQUESTS=1000
   export SECURITY_CORS_ORIGINS=["https://yourdomain.com"]
   ```

3. **Performance Optimization**
   ```bash
   # Enable caching
   export ANALYSIS_ENABLE_CACHING=true
   export DB_CACHE_TTL=600

   # Optimize LLM settings
   export LLM_DEFAULT_TEMPERATURE=0.1
   export LLM_DEFAULT_MAX_TOKENS=1500
   ```

### Scaling Considerations

- **Horizontal Scaling**: Stateless design allows multiple instances
- **Caching**: Redis integration for distributed caching
- **Database**: Supabase handles scaling automatically
- **LLM Rate Limits**: Built-in retry mechanisms and fallbacks

## 🤝 Contributing

### Development Setup

1. **Fork and clone the repository**
2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install development dependencies**
   ```bash
   make install-dev
   ```

4. **Set up pre-commit hooks**
   ```bash
   pre-commit install
   ```

### Code Style

We use several tools to maintain code quality:

```bash
# Format code
make format

# Run linting
make lint

# Type checking
mypy baseball_analytics/
```

### Pull Request Process

1. Create a feature branch from `main`
2. Make your changes with tests
3. Ensure all tests pass: `make test`
4. Format code: `make format`
5. Submit a pull request with a clear description

### Adding New Features

#### Adding a New LLM Provider

```python
from baseball_analytics.infrastructure.llm_providers import LLMProvider

class CustomProvider(LLMProvider):
    def get_model(self, model_name: str, **kwargs) -> BaseChatModel:
        # Implementation here
        pass

# Register the provider
LLMProviderFactory.register_provider("custom", CustomProvider)
```

#### Adding a New Content Generator

```python
from baseball_analytics.cag.generation import ContentGenerator

class CustomGenerator(ContentGenerator):
    def generate(self, request: ContentGenerationRequest) -> GeneratedContent:
        # Implementation here
        pass

    def supports_content_type(self, content_type: str) -> bool:
        return content_type == "custom_type"
```

## 📚 API Reference

### Core Services

#### BaseballAnalysisService

```python
class BaseballAnalysisService:
    def analyze_match(self, match_id: str) -> AnalysisResult:
        """Perform complete analysis for a baseball match."""

    def generate_content(
        self,
        analysis_result: AnalysisResult,
        content_type: str,
        **kwargs
    ) -> GeneratedContent:
        """Generate content based on analysis results."""

    def analyze_and_generate_content(
        self,
        match_id: str,
        content_type: str = "social_media_post",
        **content_kwargs
    ) -> tuple[AnalysisResult, GeneratedContent]:
        """Perform analysis and generate content in one operation."""
```

#### ContextManager

```python
class ContextManager:
    def get_analysis_context(self, match_id: str) -> AnalysisContext:
        """Get complete analysis context for a match."""

    def validate_context(self, context: AnalysisContext) -> bool:
        """Validate that context contains sufficient data for analysis."""

    def add_custom_collector(self, collector: ContextCollector) -> None:
        """Add a custom context collector."""
```

#### LLMManager

```python
class LLMManager:
    def get_model(
        self,
        provider_name: Optional[str] = None,
        model_name: Optional[str] = None,
        **kwargs
    ) -> BaseChatModel:
        """Get a language model instance."""

    def register_provider(self, name: str, provider: LLMProvider) -> None:
        """Register an LLM provider."""
```

### Content Types

| Type | Description | Use Case |
|------|-------------|----------|
| `social_media_post` | Short, engaging posts | Twitter, Instagram, Facebook |
| `blog_article` | Long-form articles | Blogs, news sites |
| `news_summary` | Brief news summaries | News feeds, notifications |
| `match_preview` | Pre-game analysis | Preview articles |
| `analysis_report` | Detailed reports | Professional analysis |

### Content Tones

| Tone | Description | Example Use |
|------|-------------|-------------|
| `professional` | Formal, analytical | Business reports |
| `casual` | Relaxed, conversational | Social media |
| `enthusiastic` | Excited, energetic | Fan content |
| `analytical` | Data-focused, technical | Statistical analysis |

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Errors

```bash
# Check Supabase configuration
python -c "from baseball_analytics.infrastructure.config import get_settings; print(get_settings().database)"

# Test connection
python -c "
from baseball_analytics.infrastructure.database import SupabaseConnection
conn = SupabaseConnection('your_url', 'your_key')
print('Connected:', conn.is_connected())
"
```

#### 2. LLM API Errors

```bash
# Check API key configuration
python -c "from baseball_analytics.infrastructure.config import get_settings; print(bool(get_settings().llm.openai_api_key))"

# Test LLM connection
python -c "
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
manager = setup_llm_manager()
model = manager.get_model()
print('Model ready:', model is not None)
"
```

#### 3. Import Errors

```bash
# Reinstall package
pip uninstall baseball-analytics
pip install -e .

# Check Python path
python -c "import sys; print('\n'.join(sys.path))"
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
export APP_DEBUG=true
export LOG_LEVEL=DEBUG
python -m baseball_analytics.main analyze BSW025073025
```

### Performance Issues

1. **Enable Caching**
   ```bash
   export ANALYSIS_ENABLE_CACHING=true
   export DB_CACHE_TTL=600
   ```

2. **Optimize LLM Settings**
   ```bash
   export LLM_DEFAULT_TEMPERATURE=0.1
   export LLM_DEFAULT_MAX_TOKENS=1000
   ```

3. **Monitor Performance**
   ```python
   from baseball_analytics.application.services import AnalyticsService
   analytics = AnalyticsService()
   summary = analytics.get_performance_summary()
   print(summary)
   ```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain** for the powerful LLM framework
- **Pydantic** for data validation and settings management
- **Supabase** for the backend infrastructure
- **OpenAI, Google, Anthropic** for LLM services
- **Korean Baseball Organization (KBO)** for inspiration

## 📞 Support

- **Documentation**: [docs.baseballanalytics.com](https://docs.baseballanalytics.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/baseball-analytics/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/baseball-analytics/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

### Version 2.1 (Q3 2024)
- [ ] Real-time analysis during live games
- [ ] Player-level statistics and analysis
- [ ] Advanced visualization components
- [ ] REST API with FastAPI

### Version 2.2 (Q4 2024)
- [ ] Multi-language support (English, Korean, Japanese)
- [ ] Advanced ML models for predictions
- [ ] Integration with more data sources
- [ ] Mobile app companion

### Version 3.0 (Q1 2025)
- [ ] Multi-sport support (soccer, basketball)
- [ ] Real-time streaming analytics
- [ ] Advanced AI agents with memory
- [ ] Enterprise features and SLA

---

**Built with ❤️ for baseball analytics and modern AI technologies**
