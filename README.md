# 통합 멀티 스포츠 분석 플랫폼

## 🎯 개요

실제 Supabase 데이터베이스 구조를 기반으로 한 통합 멀티 스포츠 분석 플랫폼입니다.

### 📊 지원 스포츠 및 데이터 구조

#### ⚾ 야구 (풍부한 데이터)
- **데이터 소스**: `team_stats` 테이블 + WDL 데이터
- **분석 에이전트**: 
  - `BaseballPitcherAgent`: 투수 분석 (pitcher_profile, pitcher_stats)
  - `BaseballTeamAgent`: 팀 분석 (season_stats, recent_matches)
- **분석 깊이**: 매우 상세 (투수 매치업, 팀 통계, 최근 폼)

#### ⚽ 축구 / 🏀 농구 / 🏐 배구 (WDL 데이터)
- **데이터 소스**: WDL 데이터만 (`sportic_contents`)
- **분석 에이전트**: 스포츠별 특화 에이전트 (SoccerAgent, BasketballAgent, VolleyballAgent)
- **분석 깊이**: 중간 (승무패 기록, 최근 폼, 맞대결)

#### 🔄 공통 분석
- **H2HAgent**: 모든 스포츠의 맞대결 분석
- **HomeAwayAgent**: 홈/원정 폼 분석

## 🏗️ 아키텍처

### 통합 구조
```
sports_analytics/
├── main.py                           # 통합 메인 애플리케이션
├── config.py                         # 통합 설정 관리
├── agents/
│   └── sports_agents.py              # 모든 스포츠 에이전트
└── infrastructure/
    ├── database.py                   # 통합 데이터베이스 접근
    └── llm_providers.py              # LLM 제공자 관리
```

### 도구 기반 모듈화
- **@tool 데코레이터**: 각 데이터 수집 기능을 독립적인 도구로 구현
- **에이전트 + 도구**: 에이전트가 필요한 도구를 조합하여 사용
- **자동 스포츠 감지**: 경기 데이터에서 스포츠 종목을 자동 판별

## 🚀 사용법

### 1. 시스템 테스트
```bash
# 전체 시스템 테스트 (데이터베이스, LLM, 에이전트)
python test_sports_analytics.py
```

### 2. 분석 대상 경기 확인
```bash
# 스포츠별로 분류된 분석 대상 경기 목록
python -m sports_analytics.main list
```

### 3. 개별 경기 분석

#### ⚾ 야구 분석 (풍부한 데이터)
```bash
python -m sports_analytics.main analyze BSW025073025
```

#### ⚽ 축구 분석 (WDL 데이터)
```bash
python -m sports_analytics.main analyze SOC025073025
```

### 4. 일괄 분석
```bash
# 모든 분석 대상 경기를 스포츠별로 일괄 분석
python -m sports_analytics.main batch
```

### 5. 시스템 상태 확인
```bash
# 시스템 구성 요소 테스트
python -m sports_analytics.main test
```

## 🔧 주요 특징

### 1. **실제 데이터베이스 구조 완벽 반영**
- 가상의 테이블이나 필드 없이 실제 존재하는 데이터만 사용
- 야구: `team_stats` 테이블의 JSONB 필드 활용
- 다른 스포츠: `sportic_contents`의 WDL 데이터 활용

### 2. **자동 스포츠 감지 및 적절한 에이전트 선택**
- 경기 데이터의 `sports` 필드 기반 자동 감지
- 스포츠별 데이터 가용성에 맞는 에이전트 자동 선택
- 야구: 풍부한 통계 분석, 다른 스포츠: WDL 중심 분석

### 3. **SOLID 원칙 적용**
- **Single Responsibility**: 각 에이전트는 하나의 스포츠/분석 영역 담당
- **Open/Closed**: 새로운 스포츠 추가 시 기존 코드 수정 없이 확장
- **Liskov Substitution**: 모든 에이전트는 동일한 인터페이스 구현
- **Interface Segregation**: 도구 기반으로 기능 분리
- **Dependency Inversion**: 추상화에 의존, 구체적 구현에 의존하지 않음

### 4. **통합된 구조**
- 기존 중복 파일들 제거 (`real_main.py`, `run_*` 파일들)
- 일관된 import 경로와 명명 규칙
- 단일 메인 애플리케이션으로 모든 기능 접근

## 📊 분석 품질

- **⚾ 야구**: 4.0/4 (team_stats의 풍부한 데이터)
- **⚽ 축구**: 3.0/4 (WDL 데이터 + 전술 분석)
- **🏀 농구**: 3.0/4 (WDL 데이터 + 스타일 분석)
- **🏐 배구**: 3.0/4 (WDL 데이터 + 세트 분석)

## 🔧 설정

### 환경 변수 (config/config.py)
```python
SUPABASE_URL = "your_supabase_url"
SUPABASE_KEY = "your_supabase_key"
OPENAI_API_KEY = "your_openai_key"
GEMINI_API_KEY = "your_gemini_key"
```

### 의존성
```bash
pip install -r requirements.txt
```

## 📁 프로젝트 구조

```
sportic365/
├── sports_analytics/                          # 메인 패키지
│   ├── main.py                               # 통합 메인 애플리케이션
│   ├── config.py                             # 통합 설정
│   ├── agents/
│   │   └── sports_agents.py                  # 모든 스포츠 에이전트
│   └── infrastructure/
│       ├── database.py                       # 통합 데이터베이스
│       └── llm_providers.py                  # LLM 제공자
├── config/
│   └── config.py                             # 환경 변수 설정
├── test_sports_analytics.py                  # 통합 테스트
└── README.md                                 # 이 파일
```

## 🚨 중요 사항

### 데이터 제약 사항
1. **야구 외 스포츠**: 상세 통계 테이블이 없어 WDL 데이터만 활용
2. **실시간 데이터**: 경기 중 실시간 업데이트는 지원하지 않음
3. **선수 개별 통계**: 팀 단위 통계만 제공

### 분석 신뢰도
- **야구**: 풍부한 데이터로 높은 신뢰도
- **축구**: 중간 신뢰도 (WDL + 전술 분석)
- **농구/배구**: 중간 신뢰도 (WDL 중심)

## 🧪 테스트 결과

```bash
$ python test_sports_analytics.py

🚀 Multi-Sport Analytics Platform - Comprehensive Test Suite
================================================================================
🔧 Testing Configuration...
   ✅ Database URL: https://ozdeoipuyiad...
   ✅ Database configured
   ✅ LLM Provider: LLMProvider.OPENAI
   ✅ LLM Model: gpt-4o-mini

📊 Testing Database Connectivity...
   ✅ Database connection successful
   📈 Found 0 target matches

🤖 Testing LLM Connectivity...
   ✅ LLM connection successful
   🔤 Test response: 안녕하세요! 어떻게 도와드릴까요?...

🎯 Test Results Summary
================================================================================
✅ PASS Configuration
✅ PASS Database
✅ PASS LLM

📊 Overall Result: 3/3 tests passed

🎉 All tests passed! System is ready for use.
```

## 🔮 확장 계획

### 1. 데이터 확장
- 축구/농구/배구용 상세 통계 테이블 추가
- 선수 개별 통계 데이터 통합
- 실시간 경기 데이터 연동

### 2. 분석 고도화
- 머신러닝 모델 통합
- 예측 정확도 향상
- 시각화 기능 추가

### 3. 새로운 스포츠
- 테니스, 골프 등 개별 스포츠 지원
- e스포츠 분석 기능
- 국제 대회 분석

---

**실제 데이터베이스 구조를 완벽히 반영하고 SOLID 원칙을 적용한 통합 멀티 스포츠 분석 플랫폼입니다! 🎯🏆**
