# Sportic365 Baseball Analytics Platform

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

Sportic365는 AI 기반의 스포츠 분석 플랫폼으로, 야구를 중심으로 한 다양한 스포츠 경기 분석과 자동 콘텐츠 생성을 제공합니다. 실제 Supabase 데이터베이스와 연동하여 정확하고 신뢰할 수 있는 분석 결과를 제공합니다.

## 📖 문서

### 핵심 문서
- **[사용자 가이드](docs/USAGE_GUIDE.md)** - 플랫폼 사용법 단계별 가이드
- **[API 레퍼런스](docs/API_REFERENCE.md)** - 상세한 API 문서와 사용 예시
- **[데이터베이스 스키마](docs/DATABASE_SCHEMA.md)** - Supabase 데이터베이스 구조 설명
- **[시스템 아키텍처](docs/ARCHITECTURE.md)** - 전체 시스템 구조와 설계 원칙

### 실사용 가이드
- **[실제 사용법](REAL_USAGE.md)** - 실제 데이터를 사용한 분석 방법
- **[멀티 스포츠 사용법](REAL_MULTI_SPORT_USAGE.md)** - 여러 스포츠 동시 분석

## ✨ 주요 기능

- **🤖 AI 기반 경기 분석**: 다양한 LLM을 활용한 스포츠 경기 분석
- **📊 실시간 데이터 연동**: Supabase를 통한 실시간 경기 데이터 접근
- **🎯 멀티 스포츠 지원**: 야구, 축구, 농구, 배구 등 다양한 스포츠
- **📝 자동 콘텐츠 생성**: SNS 포스팅, 블로그 글 등 자동 생성
- **🔧 모듈러 아키텍처**: SOLID 원칙 기반의 확장 가능한 구조
- **⚡ 고성능**: 캐싱, 비동기 처리를 통한 최적화
- **🧪 테스트 커버리지**: 포괄적인 단위 및 통합 테스트

## 🚀 빠른 시작

### 1. 설치

```bash
# 저장소 클론
git clone https://github.com/your-org/sportic365.git
cd sportic365

# 의존성 설치
pip install -r requirements.txt
```

### 2. 환경 설정

`.env` 파일을 생성하고 다음 내용을 추가하세요:

```env
# 데이터베이스 설정
DB_SUPABASE_URL=your_supabase_url_here
DB_SUPABASE_KEY=your_supabase_key_here

# LLM 설정
LLM_OPENAI_API_KEY=your_openai_api_key_here
LLM_DEFAULT_PROVIDER=openai
LLM_DEFAULT_MODEL=gpt-4o-mini

# 애플리케이션 설정
APP_ENVIRONMENT=development
LOG_LEVEL=INFO
```

### 3. 기본 사용법

```python
from baseball_analytics.real_main import main

# 실제 데이터 분석 실행
if __name__ == "__main__":
    main()
```

더 자세한 사용법은 **[사용자 가이드](docs/USAGE_GUIDE.md)**를 참고하세요.

## 🏗️ 시스템 아키텍처

### 레이어 구조

```
┌─────────────────────┐
│   사용자 인터페이스    │
├─────────────────────┤
│   애플리케이션 계층    │  ← 비즈니스 로직 조율
├─────────────────────┤
│   도메인 계층        │  ← 핵심 비즈니스 로직
├─────────────────────┤
│   인프라스트럭처 계층  │  ← 외부 시스템 연동
└─────────────────────┘
```

### 주요 컴포넌트

- **도메인 계층**: 경기, 팀, 통계 등 핵심 엔티티
- **애플리케이션 계층**: 분석 서비스, 콘텐츠 생성 서비스
- **인프라스트럭처 계층**: Supabase Repository, LLM 제공자
- **AI 에이전트**: 스포츠별 특화된 분석 에이전트

자세한 내용은 **[시스템 아키텍처](docs/ARCHITECTURE.md)** 문서를 참고하세요.

## 📊 데이터베이스 구조

### 주요 테이블

- **target_games**: 경기 기본 정보
- **team_stats**: 야구 팀 통계 (야구 전용)
- **sportic_contents**: 분석 콘텐츠 및 WDL 데이터
- **sportic_sns**: SNS 포스팅 관련 데이터
- **sportic_pick**: 베팅 픽 및 예측 데이터

### RPC 함수

- `get_sns_target_matches()`: SNS 포스팅 대상 경기 조회
- `get_target_matches()`: 분석 대상 경기 조회

자세한 스키마 정보는 **[데이터베이스 스키마](docs/DATABASE_SCHEMA.md)** 문서를 참고하세요.

## 🔧 사용 예시

### 기본 경기 분석

```python
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository
from baseball_analytics.agents.real_baseball_agent import RealBaseballAgent
from baseball_analytics.infrastructure.config import get_settings
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager

# 설정 및 초기화
settings = get_settings()
repository = RealSupabaseRepository(settings.database.supabase_url, settings.database.supabase_key)
llm_manager = setup_llm_manager(settings.llm)
agent = RealBaseballAgent(repository, llm_manager)

# 경기 분석
analysis = agent.analyze_match_comprehensive("BSW025073025")
print(analysis["summary"])
```

### 멀티 스포츠 분석

```python
from baseball_analytics.real_sport_main import main as sport_main

# 여러 스포츠 동시 분석
sport_main()
```

### 배치 분석

```python
from concurrent.futures import ThreadPoolExecutor

def analyze_matches_batch(match_ids):
    with ThreadPoolExecutor(max_workers=3) as executor:
        results = list(executor.map(agent.analyze_match_comprehensive, match_ids))
    return results

# 여러 경기 동시 분석
match_ids = ["BSW025073025", "BSW025073026", "BSW025073027"]
results = analyze_matches_batch(match_ids)
```

## 🧪 테스트

```bash
# 전체 테스트 실행
python -m pytest tests/

# 실제 시스템 테스트
python test_real_system.py

# 멀티 스포츠 테스트
python test_multi_sport_system.py
```

## 🐳 Docker 배포

```bash
# Docker 이미지 빌드
docker build -t sportic365 .

# Docker Compose로 실행
docker-compose up -d
```

## 📈 성능 최적화

### 캐싱

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def get_cached_match_data(match_id):
    return repository.get_match_data(match_id)
```

### 비동기 처리

```python
import asyncio

async def async_analyze_matches(match_ids):
    tasks = [analyze_match_async(match_id) for match_id in match_ids]
    results = await asyncio.gather(*tasks)
    return results
```

## 🔒 보안

- 환경 변수를 통한 API 키 관리
- Pydantic을 통한 데이터 검증
- 구조화된 에러 처리
- 로깅 및 모니터링

## 📝 API 문서

상세한 API 문서는 **[API 레퍼런스](docs/API_REFERENCE.md)**를 참고하세요.

### 주요 클래스

- `SupabaseRepository`: 데이터베이스 접근
- `RealBaseballAgent`: 야구 분석 에이전트
- `AnalysisService`: 분석 서비스
- `ContentGenerator`: 콘텐츠 생성

### 사용 가능한 스크립트

- `run_real_analysis.py`: 실제 데이터 분석
- `run_multi_sport.py`: 멀티 스포츠 분석
- `explore_supabase.py`: 데이터베이스 탐색
- `quick_explore.py`: 빠른 데이터 확인

## 🤝 기여하기

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다. 자세한 내용은 `LICENSE` 파일을 참고하세요.

## 📞 지원

문제가 발생하거나 질문이 있으시면:

1. **[GitHub Issues](https://github.com/your-org/sportic365/issues)**에 문제를 보고해주세요
2. **[사용자 가이드](docs/USAGE_GUIDE.md)**의 문제 해결 섹션을 확인해주세요
3. 로그 파일과 함께 상세한 오류 내용을 포함해주세요

## 🚀 로드맵

- [ ] 실시간 경기 분석
- [ ] 웹 대시보드 구축
- [ ] 모바일 앱 개발
- [ ] 마이크로서비스 아키텍처 전환
- [ ] 머신러닝 모델 통합
- [ ] 다국어 지원 확대

---

**Sportic365**로 스포츠 분석의 새로운 차원을 경험해보세요! 🏆
