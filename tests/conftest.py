"""Pytest configuration and fixtures."""

import pytest
from datetime import datetime
from unittest.mock import Mo<PERSON>, MagicMock

from baseball_analytics.domain.models import (
    Team, Match, TeamStats, AnalysisContext, SportType, MatchStatus
)
from baseball_analytics.infrastructure.database import SupabaseConnection
from baseball_analytics.infrastructure.llm_providers import LL<PERSON>rovider
from baseball_analytics.cag.context import DataRepository


@pytest.fixture
def sample_team_home():
    """Sample home team fixture."""
    return Team(
        id="team_home",
        name="Home Team",
        short_name="HOME"
    )


@pytest.fixture
def sample_team_away():
    """Sample away team fixture."""
    return Team(
        id="team_away",
        name="Away Team", 
        short_name="AWAY"
    )


@pytest.fixture
def sample_match(sample_team_home, sample_team_away):
    """Sample match fixture."""
    return Match(
        match_id="test_match_001",
        sport=SportType.BASEBALL,
        home_team=sample_team_home,
        away_team=sample_team_away,
        match_date=datetime(2024, 6, 18, 19, 0),
        status=MatchStatus.SCHEDULED,
        venue="Test Stadium"
    )


@pytest.fixture
def sample_team_stats():
    """Sample team statistics fixture."""
    return [
        TeamStats(
            team_id="team_home",
            match_id="test_match_001",
            runs=5,
            hits=8,
            errors=1,
            batting_average=0.275,
            era=3.45,
            wins=10,
            losses=5
        ),
        TeamStats(
            team_id="team_away",
            match_id="test_match_001",
            runs=3,
            hits=6,
            errors=2,
            batting_average=0.250,
            era=4.20,
            wins=8,
            losses=7
        )
    ]


@pytest.fixture
def sample_analysis_context(sample_match, sample_team_stats):
    """Sample analysis context fixture."""
    home_stats = [s for s in sample_team_stats if s.team_id == "team_home"]
    away_stats = [s for s in sample_team_stats if s.team_id == "team_away"]
    
    return AnalysisContext(
        match=sample_match,
        home_team_stats=home_stats,
        away_team_stats=away_stats,
        historical_h2h=[],
        weather_conditions={"temperature": 25, "conditions": "clear"}
    )


@pytest.fixture
def mock_repository():
    """Mock data repository fixture."""
    repository = Mock(spec=DataRepository)
    return repository


@pytest.fixture
def mock_llm_provider():
    """Mock LLM provider fixture."""
    provider = Mock(spec=LLMProvider)
    mock_model = MagicMock()
    provider.get_model.return_value = mock_model
    return provider


@pytest.fixture
def mock_supabase_connection():
    """Mock Supabase connection fixture."""
    connection = Mock(spec=SupabaseConnection)
    mock_client = MagicMock()
    connection.client = mock_client
    connection.connect.return_value = mock_client
    connection.is_connected.return_value = True
    return connection


@pytest.fixture
def sample_supabase_match_data():
    """Sample Supabase match data fixture."""
    return {
        "match_id": "test_match_001",
        "sports": "baseball",
        "home_team": "team_home",
        "away_team": "team_away",
        "home_team_name": "Home Team",
        "away_team_name": "Away Team",
        "match_date": "2024-06-18",
        "match_time": "19:00:00",
        "venue": "Test Stadium",
        "home_score": None,
        "away_score": None
    }


@pytest.fixture
def sample_supabase_stats_data():
    """Sample Supabase team stats data fixture."""
    return [
        {
            "team_id": "team_home",
            "match_id": "test_match_001",
            "runs": 5,
            "hits": 8,
            "errors": 1,
            "batting_average": 0.275,
            "era": 3.45,
            "wins": 10,
            "losses": 5
        },
        {
            "team_id": "team_away",
            "match_id": "test_match_001",
            "runs": 3,
            "hits": 6,
            "errors": 2,
            "batting_average": 0.250,
            "era": 4.20,
            "wins": 8,
            "losses": 7
        }
    ]


@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests."""
    # Reset LLMManager singleton
    from baseball_analytics.infrastructure.llm_providers import LLMManager
    LLMManager._instance = None
    
    # Reset settings singleton
    from baseball_analytics.infrastructure.config import _settings
    import baseball_analytics.infrastructure.config as config_module
    config_module._settings = None
    
    yield
    
    # Clean up after test
    LLMManager._instance = None
    config_module._settings = None
