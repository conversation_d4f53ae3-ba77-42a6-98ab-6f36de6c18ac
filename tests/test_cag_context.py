"""Tests for CAG context layer."""

import pytest
from unittest.mock import <PERSON><PERSON>, Magic<PERSON>ock
from datetime import datetime

from baseball_analytics.cag.context import (
    ContextManager, MatchContextCollector, StatisticsContextCollector,
    HistoricalContextCollector, ContextAggregator
)
from baseball_analytics.domain.models import Match, Team, TeamStats, SportType


class TestMatchContextCollector:
    """Tests for MatchContextCollector."""
    
    def test_collect_valid_match(self, mock_repository, sample_match):
        """Test collecting context for a valid match."""
        mock_repository.get_match_by_id.return_value = sample_match
        
        collector = MatchContextCollector(mock_repository)
        context = collector.collect("test_match_001")
        
        assert "match" in context
        assert "home_team" in context
        assert "away_team" in context
        assert context["match"] == sample_match
        mock_repository.get_match_by_id.assert_called_once_with("test_match_001")
    
    def test_collect_nonexistent_match(self, mock_repository):
        """Test collecting context for nonexistent match."""
        mock_repository.get_match_by_id.return_value = None
        
        collector = MatchContextCollector(mock_repository)
        
        with pytest.raises(ValueError, match="Match not found"):
            collector.collect("nonexistent_match")


class TestStatisticsContextCollector:
    """Tests for StatisticsContextCollector."""
    
    def test_collect_team_stats(self, mock_repository, sample_match, sample_team_stats):
        """Test collecting team statistics."""
        mock_repository.get_match_by_id.return_value = sample_match
        mock_repository.get_team_stats.return_value = sample_team_stats
        
        collector = StatisticsContextCollector(mock_repository)
        context = collector.collect("test_match_001")
        
        assert "home_team_stats" in context
        assert "away_team_stats" in context
        
        # Check that stats are properly separated
        home_stats = context["home_team_stats"]
        away_stats = context["away_team_stats"]
        
        assert len(home_stats) == 1
        assert len(away_stats) == 1
        assert home_stats[0].team_id == "team_home"
        assert away_stats[0].team_id == "team_away"
    
    def test_collect_no_match(self, mock_repository):
        """Test collecting stats when match doesn't exist."""
        mock_repository.get_match_by_id.return_value = None
        
        collector = StatisticsContextCollector(mock_repository)
        context = collector.collect("nonexistent_match")
        
        assert context["home_team_stats"] == []
        assert context["away_team_stats"] == []


class TestHistoricalContextCollector:
    """Tests for HistoricalContextCollector."""
    
    def test_collect_historical_matches(self, mock_repository, sample_match):
        """Test collecting historical match data."""
        historical_matches = [sample_match]
        mock_repository.get_match_by_id.return_value = sample_match
        mock_repository.get_historical_matches.return_value = historical_matches
        
        collector = HistoricalContextCollector(mock_repository, max_historical_matches=5)
        context = collector.collect("test_match_001")
        
        assert "historical_h2h" in context
        assert len(context["historical_h2h"]) == 1
        
        mock_repository.get_historical_matches.assert_called_once_with(
            "team_home", "team_away", 5
        )
    
    def test_collect_no_match(self, mock_repository):
        """Test collecting historical data when match doesn't exist."""
        mock_repository.get_match_by_id.return_value = None
        
        collector = HistoricalContextCollector(mock_repository)
        context = collector.collect("nonexistent_match")
        
        assert context["historical_h2h"] == []


class TestContextAggregator:
    """Tests for ContextAggregator."""
    
    def test_aggregate_multiple_collectors(self):
        """Test aggregating context from multiple collectors."""
        # Create mock collectors
        collector1 = Mock()
        collector1.collect.return_value = {"data1": "value1"}
        
        collector2 = Mock()
        collector2.collect.return_value = {"data2": "value2"}
        
        aggregator = ContextAggregator([collector1, collector2])
        result = aggregator.aggregate("test_match")
        
        assert result["data1"] == "value1"
        assert result["data2"] == "value2"
        
        collector1.collect.assert_called_once_with("test_match")
        collector2.collect.assert_called_once_with("test_match")
    
    def test_aggregate_with_error(self):
        """Test aggregation continues when one collector fails."""
        # Create collectors, one that fails
        collector1 = Mock()
        collector1.collect.side_effect = Exception("Collector error")
        
        collector2 = Mock()
        collector2.collect.return_value = {"data2": "value2"}
        
        aggregator = ContextAggregator([collector1, collector2])
        result = aggregator.aggregate("test_match")
        
        # Should still get data from working collector
        assert result["data2"] == "value2"
        assert "data1" not in result
    
    def test_add_remove_collectors(self):
        """Test adding and removing collectors."""
        collector1 = Mock()
        collector2 = Mock()
        
        aggregator = ContextAggregator([collector1])
        assert len(aggregator._collectors) == 1
        
        # Add collector
        aggregator.add_collector(collector2)
        assert len(aggregator._collectors) == 2
        
        # Remove collector
        aggregator.remove_collector(collector1)
        assert len(aggregator._collectors) == 1
        assert collector2 in aggregator._collectors


class TestContextManager:
    """Tests for ContextManager."""
    
    def test_get_analysis_context_success(self, mock_repository, sample_match, sample_team_stats):
        """Test successful context retrieval."""
        # Setup mock repository
        mock_repository.get_match_by_id.return_value = sample_match
        mock_repository.get_team_stats.return_value = sample_team_stats
        mock_repository.get_historical_matches.return_value = []
        
        manager = ContextManager(mock_repository)
        context = manager.get_analysis_context("test_match_001")
        
        assert context.match == sample_match
        assert len(context.home_team_stats) == 1
        assert len(context.away_team_stats) == 1
        assert context.historical_h2h == []
    
    def test_get_analysis_context_no_match(self, mock_repository):
        """Test context retrieval when match doesn't exist."""
        mock_repository.get_match_by_id.return_value = None
        
        manager = ContextManager(mock_repository)
        
        with pytest.raises(ValueError, match="Match data not found"):
            manager.get_analysis_context("nonexistent_match")
    
    def test_validate_context_valid(self, sample_analysis_context):
        """Test context validation with valid context."""
        manager = ContextManager(Mock())
        
        is_valid = manager.validate_context(sample_analysis_context)
        assert is_valid is True
    
    def test_validate_context_invalid(self, mock_repository):
        """Test context validation with invalid context."""
        from baseball_analytics.domain.models import AnalysisContext
        
        # Create invalid context (no match)
        invalid_context = AnalysisContext(
            match=None,
            home_team_stats=[],
            away_team_stats=[]
        )
        
        manager = ContextManager(mock_repository)
        is_valid = manager.validate_context(invalid_context)
        assert is_valid is False
    
    def test_add_custom_collector(self, mock_repository):
        """Test adding custom collector."""
        manager = ContextManager(mock_repository)
        
        custom_collector = Mock()
        manager.add_custom_collector(custom_collector)
        
        # Verify collector was added to aggregator
        assert custom_collector in manager._aggregator._collectors
