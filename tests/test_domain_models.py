"""Tests for domain models."""

import pytest
from datetime import datetime
from pydantic import ValidationError

from baseball_analytics.domain.models import (
    Team, Match, TeamStats, AnalysisContext, AnalysisInsight,
    PredictionResult, AnalysisResult, SportType, MatchStatus
)


class TestTeam:
    """Tests for Team model."""
    
    def test_valid_team_creation(self):
        """Test creating a valid team."""
        team = Team(
            id="test_team",
            name="Test Team",
            short_name="TEST"
        )
        
        assert team.id == "test_team"
        assert team.name == "Test Team"
        assert team.short_name == "TEST"
    
    def test_team_name_validation(self):
        """Test team name validation."""
        # Empty name should fail
        with pytest.raises(ValidationError):
            Team(id="test", name="", short_name="TEST")
        
        # Whitespace-only name should fail
        with pytest.raises(ValidationError):
            Team(id="test", name="   ", short_name="TEST")
    
    def test_team_name_trimming(self):
        """Test that team names are trimmed."""
        team = Team(
            id="test",
            name="  Test Team  ",
            short_name="  TEST  "
        )
        
        assert team.name == "Test Team"
        assert team.short_name == "TEST"


class TestMatch:
    """Tests for Match model."""
    
    def test_valid_match_creation(self, sample_team_home, sample_team_away):
        """Test creating a valid match."""
        match = Match(
            match_id="test_match",
            home_team=sample_team_home,
            away_team=sample_team_away,
            match_date=datetime(2024, 6, 18, 19, 0)
        )
        
        assert match.match_id == "test_match"
        assert match.sport == SportType.BASEBALL
        assert match.status == MatchStatus.SCHEDULED
    
    def test_same_team_validation(self, sample_team_home):
        """Test that home and away teams must be different."""
        with pytest.raises(ValidationError):
            Match(
                match_id="test_match",
                home_team=sample_team_home,
                away_team=sample_team_home,  # Same team
                match_date=datetime(2024, 6, 18, 19, 0)
            )


class TestTeamStats:
    """Tests for TeamStats model."""
    
    def test_valid_stats_creation(self):
        """Test creating valid team statistics."""
        stats = TeamStats(
            team_id="test_team",
            match_id="test_match",
            runs=5,
            hits=8,
            errors=1,
            batting_average=0.275
        )
        
        assert stats.team_id == "test_team"
        assert stats.runs == 5
        assert stats.batting_average == 0.275
    
    def test_negative_values_validation(self):
        """Test that negative values are not allowed."""
        with pytest.raises(ValidationError):
            TeamStats(
                team_id="test_team",
                match_id="test_match",
                runs=-1  # Negative value
            )
    
    def test_batting_average_range(self):
        """Test batting average range validation."""
        # Valid range
        stats = TeamStats(
            team_id="test_team",
            match_id="test_match",
            batting_average=0.5
        )
        assert stats.batting_average == 0.5
        
        # Invalid range
        with pytest.raises(ValidationError):
            TeamStats(
                team_id="test_team",
                match_id="test_match",
                batting_average=1.5  # > 1.0
            )


class TestAnalysisInsight:
    """Tests for AnalysisInsight model."""
    
    def test_valid_insight_creation(self):
        """Test creating a valid insight."""
        insight = AnalysisInsight(
            category="offense",
            title="Strong batting performance",
            content="The team showed excellent batting skills.",
            confidence=0.85
        )
        
        assert insight.category == "offense"
        assert insight.confidence == 0.85
    
    def test_confidence_range_validation(self):
        """Test confidence score range validation."""
        # Valid range
        insight = AnalysisInsight(
            category="test",
            title="Test",
            content="Test content",
            confidence=0.5
        )
        assert insight.confidence == 0.5
        
        # Invalid range
        with pytest.raises(ValidationError):
            AnalysisInsight(
                category="test",
                title="Test",
                content="Test content",
                confidence=1.5  # > 1.0
            )


class TestPredictionResult:
    """Tests for PredictionResult model."""
    
    def test_valid_prediction_creation(self):
        """Test creating a valid prediction."""
        prediction = PredictionResult(
            home_win_probability=0.6,
            away_win_probability=0.4,
            draw_probability=0.0
        )
        
        assert prediction.home_win_probability == 0.6
        assert prediction.away_win_probability == 0.4
    
    def test_probability_sum_validation(self):
        """Test that probabilities must sum to 1.0."""
        # Valid sum
        prediction = PredictionResult(
            home_win_probability=0.5,
            away_win_probability=0.3,
            draw_probability=0.2
        )
        assert abs(sum([
            prediction.home_win_probability,
            prediction.away_win_probability,
            prediction.draw_probability
        ]) - 1.0) < 0.01
        
        # Invalid sum
        with pytest.raises(ValidationError):
            PredictionResult(
                home_win_probability=0.6,
                away_win_probability=0.6,  # Sum > 1.0
                draw_probability=0.0
            )


class TestAnalysisResult:
    """Tests for AnalysisResult model."""
    
    def test_valid_analysis_result_creation(self):
        """Test creating a valid analysis result."""
        insights = [
            AnalysisInsight(
                category="offense",
                title="Good batting",
                content="Strong offensive performance",
                confidence=0.8
            )
        ]
        
        prediction = PredictionResult(
            home_win_probability=0.6,
            away_win_probability=0.4,
            draw_probability=0.0
        )
        
        result = AnalysisResult(
            match_id="test_match",
            insights=insights,
            prediction=prediction,
            key_points=["Strong offense", "Weak defense"],
            hashtags=["baseball", "analysis"],
            confidence_score=0.75
        )
        
        assert result.match_id == "test_match"
        assert len(result.insights) == 1
        assert result.confidence_score == 0.75
    
    def test_hashtag_formatting(self):
        """Test hashtag formatting validation."""
        result = AnalysisResult(
            match_id="test_match",
            hashtags=["baseball", "#already_formatted", "needs formatting"],
            confidence_score=0.5
        )
        
        # All hashtags should start with #
        for hashtag in result.hashtags:
            assert hashtag.startswith("#")
        
        # Should contain formatted versions
        assert "#baseball" in result.hashtags
        assert "#already_formatted" in result.hashtags
        assert "#needsformatting" in result.hashtags


class TestAnalysisContext:
    """Tests for AnalysisContext model."""
    
    def test_valid_context_creation(self, sample_analysis_context):
        """Test creating a valid analysis context."""
        context = sample_analysis_context
        
        assert context.match.match_id == "test_match_001"
        assert len(context.home_team_stats) == 1
        assert len(context.away_team_stats) == 1
        assert context.weather_conditions is not None
