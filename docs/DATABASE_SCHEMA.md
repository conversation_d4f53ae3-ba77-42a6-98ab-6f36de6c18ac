# Supabase 데이터베이스 스키마 문서

## 개요

Sportic365 플랫폼은 Supabase PostgreSQL 데이터베이스를 사용하여 스포츠 경기 데이터, 분석 콘텐츠, SNS 포스팅 등을 관리합니다. 이 문서는 실제 데이터베이스의 모든 테이블과 컬럼 구조를 상세하게 설명합니다.

## 프로젝트 정보

- **프로젝트 ID**: ozdeoipuyiadspcxlvnb
- **프로젝트명**: proto
- **리전**: ap-northeast-2 (아시아 태평양 - 서울)
- **PostgreSQL 버전**: **********
- **상태**: ACTIVE_HEALTHY

---

## 테이블 구조

### 1. target_games (예정된 경기)
**설명**: 분석 대상인 예정된 경기 정보를 저장하는 핵심 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `match_id` | TEXT | NO | - | 경기 고유 ID (PRIMARY KEY) |
| `match_num` | INTEGER | NO | - | 경기 번호 |
| `sports` | TEXT | NO | - | 스포츠 종목 (baseball, soccer, basketball 등) |
| `league` | TEXT | NO | - | 리그 코드 |
| `match_date` | DATE | NO | - | 경기 날짜 |
| `match_time` | TIME | NO | - | 경기 시간 |
| `game_type` | TEXT | NO | - | 경기 유형 (W: 정규시즌, P: 플레이오프 등) |
| `home_team` | TEXT | NO | - | 홈팀 ID |
| `away_team` | TEXT | NO | - | 원정팀 ID |
| `home_team_id` | TEXT | YES | - | 홈팀 고유 ID |
| `away_team_id` | TEXT | YES | - | 원정팀 고유 ID |
| `home_team_name` | TEXT | YES | - | 홈팀명 |
| `away_team_name` | TEXT | YES | - | 원정팀명 |
| `league_id` | TEXT | YES | - | 리그 고유 ID |
| `league_name` | TEXT | YES | - | 리그 전체 이름 |
| `win_odds` | NUMERIC | YES | - | 홈팀 승리 배당률 |
| `draw_odds` | NUMERIC | YES | - | 무승부 배당률 |
| `loss_odds` | NUMERIC | YES | - | 원정팀 승리 배당률 |
| `base_value` | TEXT | YES | - | 언더/오버 기준값 |
| `under_odds` | NUMERIC | YES | - | 언더 배당률 |
| `over_odds` | NUMERIC | YES | - | 오버 배당률 |
| `bet_close_date` | DATE | NO | - | 베팅 마감 날짜 |
| `bet_close_time` | TIME | NO | - | 베팅 마감 시간 |
| `year` | INTEGER | NO | - | 연도 |
| `round` | INTEGER | NO | - | 라운드 |
| `created_at` | TIMESTAMPTZ | NO | now() AT TIME ZONE 'Asia/Seoul' | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | now() | 수정 시간 |

#### 사용 예시

```python
# 예정된 경기 조회
response = supabase.table('target_games').select('*').eq('sports', 'baseball').execute()

# 특정 날짜의 경기 조회
response = supabase.table('target_games').select('*').eq('match_date', '2024-06-18').execute()

# 특정 팀의 경기 조회
response = supabase.table('target_games').select('*').or_(
    f'home_team_id.eq.{team_id},away_team_id.eq.{team_id}'
).execute()
```

---

### 2. match_result (경기 결과)
**크기**: 231 MB (304,719 rows)  
**설명**: 완료된 경기의 결과와 예측 정확도를 저장하는 핵심 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `match_id` | TEXT | NO | - | 경기 고유 ID (PRIMARY KEY) |
| `match_num` | INTEGER | NO | - | 경기 번호 |
| `sports` | TEXT | NO | - | 스포츠 종목 |
| `league` | TEXT | NO | - | 리그 코드 |
| `league_id` | TEXT | YES | - | 리그 고유 ID |
| `league_name` | TEXT | YES | - | 리그 전체 이름 |
| `match_date` | DATE | NO | - | 경기 날짜 |
| `match_time` | TIME | NO | - | 경기 시간 |
| `game_type` | TEXT | NO | - | 경기 유형 |
| `home_team` | TEXT | NO | - | 홈팀 ID |
| `away_team` | TEXT | NO | - | 원정팀 ID |
| `home_team_id` | TEXT | YES | - | 홈팀 고유 ID |
| `away_team_id` | TEXT | YES | - | 원정팀 고유 ID |
| `home_team_name` | TEXT | YES | - | 홈팀명 |
| `away_team_name` | TEXT | YES | - | 원정팀명 |
| `home_score` | NUMERIC | YES | - | 홈팀 득점 |
| `away_score` | NUMERIC | YES | - | 원정팀 득점 |
| `total_score` | NUMERIC | YES | - | 총 득점 |
| `match_result` | TEXT | YES | - | 경기 결과 (W: 홈팀 승, L: 원정팀 승, D: 무승부) |
| `result` | VARCHAR | YES | - | 최종 결과 |
| `base_value` | NUMERIC | YES | - | 언더/오버 기준값 |
| `win_odds` | NUMERIC | YES | - | 홈팀 승리 배당률 |
| `draw_odds` | NUMERIC | YES | - | 무승부 배당률 |
| `loss_odds` | NUMERIC | YES | - | 원정팀 승리 배당률 |
| `under_odds` | NUMERIC | YES | - | 언더 배당률 |
| `over_odds` | NUMERIC | YES | - | 오버 배당률 |
| `home_win_probability` | NUMERIC | YES | - | 홈팀 승률 예측 (0.0-1.0) |
| `away_win_probability` | NUMERIC | YES | - | 원정팀 승률 예측 (0.0-1.0) |
| `draw_probability` | NUMERIC | YES | - | 무승부 확률 예측 (0.0-1.0) |
| `over_probability` | SMALLINT | YES | - | 오버 확률 (0-100) |
| `under_probability` | SMALLINT | YES | - | 언더 확률 (0-100) |
| `odds_prediction` | VARCHAR | YES | - | 배당률 기반 예측 |
| `sportic_prediction` | VARCHAR | YES | - | Sportic365 AI 예측 |
| `round` | SMALLINT | NO | - | 라운드 |
| `year` | INTEGER | NO | - | 연도 |
| `created_at` | TIMESTAMPTZ | YES | CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Seoul' | 생성 시간 |

#### 사용 예시

```python
# 특정 경기 결과 조회
response = supabase.table('match_result').select('*').eq('match_id', 'BSW025073025').execute()

# 예측 정확도 분석
response = supabase.table('match_result').select(
    'match_id', 'sportic_prediction', 'result', 'home_win_probability', 'away_win_probability'
).not_.is_('result', 'null').execute()

# 특정 팀의 최근 경기 결과
response = supabase.table('match_result').select('*').or_(
    f'home_team_id.eq.{team_id},away_team_id.eq.{team_id}'
).order('match_date', desc=True).limit(10).execute()
```

---

### 3. sportic_contents (분석 콘텐츠)
**설명**: AI가 생성한 경기 분석 콘텐츠를 저장하는 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `match_id` | TEXT | NO | - | 경기 고유 ID (PRIMARY KEY) |
| `sports` | TEXT | YES | - | 스포츠 종목 |
| `game_type` | TEXT | NO | - | 경기 유형 |
| `league_name` | TEXT | NO | - | 리그명 |
| `home_team_name` | TEXT | NO | - | 홈팀명 |
| `away_team_name` | TEXT | NO | - | 원정팀명 |
| `match_date` | DATE | NO | - | 경기 날짜 |
| `match_time` | TIME | NO | - | 경기 시간 |
| `h2h_content` | JSONB | YES | - | 상대전적 분석 콘텐츠 |
| `h2h_wdl_summary` | JSONB | YES | - | 상대전적 승무패 요약 |
| `h2h_wdl_matches` | JSONB | YES | - | 상대전적 경기 기록 |
| `home_wdl_summary` | JSONB | YES | - | 홈팀 승무패 요약 |
| `home_wdl_matches` | JSONB | YES | - | 홈팀 최근 경기 기록 |
| `away_wdl_summary` | JSONB | YES | - | 원정팀 승무패 요약 |
| `away_wdl_matches` | JSONB | YES | - | 원정팀 최근 경기 기록 |
| `home_content` | TEXT | YES | - | 홈팀 분석 콘텐츠 |
| `away_content` | TEXT | YES | - | 원정팀 분석 콘텐츠 |
| `sportic_news` | JSONB | YES | - | 관련 스포츠 뉴스 |
| `hom_win_odds` | NUMERIC | YES | - | 홈팀 승리 배당률 |
| `away_win_odds` | NUMERIC | YES | - | 원정팀 승리 배당률 |
| `draw_odds` | NUMERIC | YES | - | 무승부 배당률 |
| `odds_analysis` | JSONB | YES | - | 배당률 분석 |
| `implied_probabilities` | JSONB | YES | - | 암시 확률 |
| `rework` | TEXT | YES | - | 재작업 플래그 |
| `created_at` | TIMESTAMPTZ | NO | CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Seoul' | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Seoul' | 수정 시간 |

#### 사용 예시

```python
# 특정 경기의 분석 콘텐츠 조회
response = supabase.table('sportic_contents').select('*').eq('match_id', 'BSW025073025').execute()

# 상대전적 데이터만 조회
response = supabase.table('sportic_contents').select(
    'match_id', 'h2h_wdl_summary', 'h2h_wdl_matches'
).eq('match_id', 'BSW025073025').execute()

# 특정 날짜의 모든 분석 콘텐츠
response = supabase.table('sportic_contents').select('*').eq('match_date', '2024-06-18').execute()
```

---

### 4. sportic_sns (SNS 콘텐츠)
**설명**: 각 플랫폼별 SNS 포스팅 콘텐츠를 저장하는 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `match_id` | TEXT | NO | - | 경기 고유 ID (PRIMARY KEY) |
| `match_date` | DATE | YES | - | 경기 날짜 |
| `match_time` | TIME | YES | - | 경기 시간 |
| `sports` | TEXT | YES | - | 스포츠 종목 |
| `game_type` | TEXT | YES | - | 경기 유형 |
| `league_name` | TEXT | YES | - | 리그명 |
| `home_team_name` | TEXT | YES | - | 홈팀명 |
| `away_team_name` | TEXT | YES | - | 원정팀명 |
| `naver_text` | JSONB | YES | - | 네이버 포스팅 텍스트 |
| `facebook_text` | JSONB | YES | - | 페이스북 포스팅 텍스트 |
| `instagram_text` | TEXT | YES | - | 인스타그램 포스팅 텍스트 |
| `x_text` | JSONB | YES | - | X(트위터) 포스팅 텍스트 |
| `tags` | JSONB | YES | - | 해시태그 목록 |
| `data_url` | JSONB | YES | - | 관련 데이터 URL |
| `naver_posted` | VARCHAR | YES | - | 네이버 포스팅 상태 |
| `facebook_posted` | VARCHAR | YES | - | 페이스북 포스팅 상태 |
| `instagram_posted` | VARCHAR | YES | - | 인스타그램 포스팅 상태 |
| `x_posted` | VARCHAR | YES | - | X 포스팅 상태 |
| `blogger_posted` | VARCHAR | YES | - | 블로거 포스팅 상태 |
| `rework` | TEXT | YES | - | 재작업 플래그 |
| `created_at` | TIMESTAMPTZ | YES | now() AT TIME ZONE 'Asia/Seoul' | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | now() AT TIME ZONE 'Asia/Seoul' | 수정 시간 |

#### 사용 예시

```python
# 특정 경기의 SNS 콘텐츠 조회
response = supabase.table('sportic_sns').select('*').eq('match_id', 'BSW025073025').execute()

# 네이버 포스팅 대상 조회
response = supabase.table('sportic_sns').select('*').is_('naver_posted', 'null').execute()

# 특정 플랫폼의 포스팅 상태 업데이트
response = supabase.table('sportic_sns').update({
    'naver_posted': 'completed'
}).eq('match_id', 'BSW025073025').execute()
```

---

### 5. sportic_pick (베팅 픽)
**설명**: AI가 생성한 베팅 추천을 저장하는 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `id` | TEXT | NO | - | 픽 고유 ID (PRIMARY KEY) |
| `sportic_pick` | JSONB | YES | - | 베팅 픽 데이터 |
| `naver_posted` | VARCHAR | YES | - | 네이버 포스팅 상태 |
| `created_at` | TIMESTAMPTZ | NO | now() | 생성 시간 |

#### 사용 예시

```python
# 베팅 픽 조회
response = supabase.table('sportic_pick').select('*').eq('id', match_id).execute()

# 미포스팅 픽 조회
response = supabase.table('sportic_pick').select('*').is_('naver_posted', 'null').execute()
```

---

### 6. team_stats (팀 통계)
**설명**: 야구 경기의 상세한 팀 통계 데이터를 저장하는 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `id` | TEXT | NO | - | 통계 고유 ID (PRIMARY KEY) |
| `match_id` | TEXT | NO | - | 경기 고유 ID |
| `sports` | TEXT | YES | 'baseball' | 스포츠 종목 |
| `sportic_team_id` | TEXT | YES | - | Sportic365 팀 ID |
| `team_id` | TEXT | NO | - | 팀 고유 ID |
| `team_name` | TEXT | YES | - | 팀명 |
| `team_role` | TEXT | YES | - | 팀 역할 (home/away) |
| `league` | TEXT | YES | - | 리그 |
| `match_date` | DATE | YES | now()::date | 경기 날짜 |
| `match_time` | TIME | YES | now()::time | 경기 시간 |
| `pitcher_profile` | JSONB | YES | - | 투수 프로필 |
| `pitcher_stats` | JSONB | YES | - | 투수 통계 |
| `season_stats` | JSONB | YES | - | 시즌 통계 |
| `season_summary` | JSONB | YES | - | 시즌 요약 |
| `recent_matches` | JSONB | YES | - | 최근 경기 기록 |
| `recent_matches_summary` | JSONB | YES | - | 최근 경기 요약 |
| `created_at` | TIMESTAMPTZ | YES | now() AT TIME ZONE 'Asia/Seoul' AT TIME ZONE 'UTC' | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | now() AT TIME ZONE 'Asia/Seoul' AT TIME ZONE 'UTC' | 수정 시간 |

#### 사용 예시

```python
# 특정 경기의 팀 통계 조회
response = supabase.table('team_stats').select('*').eq('match_id', 'BSW025073025').execute()

# 특정 팀의 통계 조회
response = supabase.table('team_stats').select('*').eq('team_id', 'team_001').execute()

# 투수 정보만 조회
response = supabase.table('team_stats').select(
    'team_name', 'pitcher_profile', 'pitcher_stats'
).eq('match_id', 'BSW025073025').execute()
```

---

### 7. team_info (팀 정보)
**설명**: 모든 스포츠 팀의 기본 정보를 저장하는 마스터 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `team_id` | TEXT | NO | - | 팀 고유 ID |
| `sports` | TEXT | NO | - | 스포츠 종목 |
| `team_name` | TEXT | NO | - | 팀명 |
| `team_full_name` | TEXT | NO | - | 팀 전체 이름 |
| `eng_name` | VARCHAR | YES | - | 영문 팀명 |
| `eng_full_name` | TEXT | YES | - | 영문 전체 이름 |
| `team_logo` | TEXT | YES | - | 팀 로고 URL |
| `sportic_league_id` | TEXT | YES | - | Sportic365 리그 ID |
| `sportic_team_id` | VARCHAR | YES | - | Sportic365 팀 ID |
| `created_at` | TIMESTAMPTZ | YES | CURRENT_TIMESTAMP | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | CURRENT_TIMESTAMP | 수정 시간 |

#### 사용 예시

```python
# 모든 야구 팀 조회
response = supabase.table('team_info').select('*').eq('sports', 'baseball').execute()

# 특정 팀 정보 조회
response = supabase.table('team_info').select('*').eq('team_id', 'SSG').execute()

# 팀 로고 URL 업데이트
response = supabase.table('team_info').update({
    'team_logo': 'https://example.com/logo.png'
}).eq('team_id', 'SSG').execute()
```

---

### 8. league_info (리그 정보)
**설명**: 모든 스포츠 리그의 기본 정보를 저장하는 마스터 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `league_id` | TEXT | NO | - | 리그 고유 ID |
| `league` | TEXT | NO | - | 리그 코드 |
| `league_name` | TEXT | NO | - | 리그명 |
| `sportic_league_id` | TEXT | NO | - | Sportic365 리그 ID |
| `eng_league` | TEXT | YES | - | 영문 리그 코드 |
| `eng_leauge_name` | TEXT | YES | - | 영문 리그명 |
| `league_order_kr` | INTEGER | YES | - | 한국어 정렬 순서 |
| `league_order_en` | INTEGER | YES | - | 영어 정렬 순서 |
| `created_at` | TIMESTAMPTZ | YES | CURRENT_TIMESTAMP | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | CURRENT_TIMESTAMP | 수정 시간 |

#### 사용 예시

```python
# 모든 리그 정보 조회
response = supabase.table('league_info').select('*').order('league_order_kr').execute()

# 특정 리그 정보 조회
response = supabase.table('league_info').select('*').eq('league_id', 'KBO').execute()
```

---

### 9. sportic_news (스포츠 뉴스)
**설명**: 스포츠 관련 뉴스를 저장하는 테이블

#### 컬럼 상세 정보

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `id` | TEXT | NO | - | 뉴스 고유 ID (PRIMARY KEY) |
| `contents` | TEXT | NO | - | 뉴스 내용 |
| `date` | DATE | YES | - | 뉴스 날짜 |
| `home_team` | VARCHAR | YES | - | 홈팀 |
| `away_team` | VARCHAR | YES | - | 원정팀 |
| `created_at` | TIMESTAMP | NO | CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Seoul' | 생성 시간 |

---

### 10. 기타 테이블

#### front_user (프론트엔드 사용자)
사용자 정보를 저장하는 테이블

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `id` | BIGINT | NO | - | 사용자 고유 ID (PRIMARY KEY) |
| `auth_id` | UUID | YES | - | 인증 ID |
| `email` | TEXT | YES | - | 이메일 |
| `nickname` | TEXT | YES | - | 닉네임 |
| `profile_image` | TEXT | YES | - | 프로필 이미지 URL |
| `kakao_id` | TEXT | YES | - | 카카오 ID |
| `last_login` | TIMESTAMPTZ | YES | - | 마지막 로그인 |
| `is_active` | BOOLEAN | YES | true | 활성 상태 |
| `created_at` | TIMESTAMPTZ | NO | now() | 생성 시간 |

#### items (아이템)
사용자 아이템을 저장하는 테이블

| 컬럼명 | 데이터 타입 | NULL 허용 | 기본값 | 설명 |
|--------|-------------|-----------|--------|------|
| `id` | UUID | NO | uuid_generate_v4() | 아이템 고유 ID (PRIMARY KEY) |
| `user_id` | UUID | NO | - | 사용자 ID |
| `name` | VARCHAR(100) | NO | - | 아이템명 |
| `description` | TEXT | YES | - | 아이템 설명 |
| `created_at` | TIMESTAMPTZ | NO | now() | 생성 시간 |
| `updated_at` | TIMESTAMPTZ | YES | - | 수정 시간 |

---

## 데이터 관계

### 주요 관계

1. **target_games ↔ match_result**: 경기 예정 → 결과
   - `target_games.match_id` = `match_result.match_id`

2. **target_games ↔ sportic_contents**: 경기 → 분석 콘텐츠
   - `target_games.match_id` = `sportic_contents.match_id`

3. **target_games ↔ sportic_sns**: 경기 → SNS 콘텐츠
   - `target_games.match_id` = `sportic_sns.match_id`

4. **target_games ↔ team_stats**: 경기 → 팀 통계
   - `target_games.match_id` = `team_stats.match_id`

5. **team_info**: 모든 팀 정보의 마스터 테이블
   - `target_games.home_team_id` = `team_info.team_id`
   - `target_games.away_team_id` = `team_info.team_id`

6. **league_info**: 모든 리그 정보의 마스터 테이블
   - `target_games.league_id` = `league_info.league_id`

### ERD 다이어그램

```mermaid
erDiagram
    target_games ||--|| sportic_contents : "match_id"
    target_games ||--|| sportic_sns : "match_id"
    target_games ||--o| match_result : "match_id"
    target_games }o--|| team_stats : "match_id"
    target_games }o--|| team_info : "home_team_id"
    target_games }o--|| team_info : "away_team_id"
    target_games }o--|| league_info : "league_id"
    
    target_games {
        text match_id PK
        text sports
        text league
        date match_date
        time match_time
        text home_team_id
        text away_team_id
    }
    
    match_result {
        text match_id PK
        numeric home_score
        numeric away_score
        text result
        numeric home_win_probability
        numeric away_win_probability
    }
    
    sportic_contents {
        text match_id PK
        jsonb h2h_wdl_summary
        jsonb odds_analysis
        text home_content
        text away_content
    }
    
    sportic_sns {
        text match_id PK
        jsonb naver_text
        jsonb facebook_text
        text instagram_text
        jsonb x_text
    }
    
    team_stats {
        text id PK
        text match_id
        text team_id
        jsonb pitcher_stats
        jsonb season_stats
    }
    
    team_info {
        text team_id PK
        text sports
        text team_name
        text team_full_name
        text team_logo
    }
    
    league_info {
        text league_id PK
        text league_name
        text sportic_league_id
    }
```

---

## 인덱스 및 성능

### 권장 인덱스

```sql
-- 자주 사용되는 조회 패턴에 대한 인덱스
CREATE INDEX idx_target_games_date ON target_games(match_date);
CREATE INDEX idx_target_games_sports ON target_games(sports);
CREATE INDEX idx_target_games_teams ON target_games(home_team_id, away_team_id);

CREATE INDEX idx_match_result_date ON match_result(match_date);
CREATE INDEX idx_match_result_teams ON match_result(home_team_id, away_team_id);

CREATE INDEX idx_sportic_contents_date ON sportic_contents(match_date);
CREATE INDEX idx_sportic_sns_posted ON sportic_sns(naver_posted, facebook_posted);

CREATE INDEX idx_team_stats_match ON team_stats(match_id);
CREATE INDEX idx_team_stats_team ON team_stats(team_id);
```

### 성능 최적화 팁

1. **날짜 범위 조회 시**: `match_date` 컬럼에 인덱스 활용
2. **팀별 조회 시**: `home_team_id`, `away_team_id` 복합 인덱스 활용
3. **JSONB 필드 조회 시**: GIN 인덱스 고려
4. **대용량 데이터 조회 시**: `LIMIT`와 `OFFSET` 사용

---

## 데이터 타입 가이드

### JSONB 필드 구조

#### `h2h_wdl_summary` 예시
```json
{
  "total_matches": 10,
  "home_wins": 6,
  "away_wins": 3,
  "draws": 1,
  "home_win_rate": 0.6,
  "away_win_rate": 0.3,
  "draw_rate": 0.1
}
```

#### `pitcher_stats` 예시
```json
{
  "name": "김투수",
  "era": 3.45,
  "wins": 15,
  "losses": 8,
  "strikeouts": 180,
  "walks": 65,
  "innings_pitched": 200.1
}
```

#### `naver_text` 예시
```json
{
  "title": "삼성 vs 두산 경기 분석",
  "content": "오늘 잠실에서 펼쳐질 삼성과 두산의 경기는...",
  "hashtags": ["#KBO", "#야구", "#삼성vs두산"],
  "summary": "홈팀 우세 예상"
}
```

---

## 마이그레이션 및 백업

### 백업 테이블

- `match_result_backup`: `match_result`의 백업 테이블
- 동일한 구조로 데이터 보존

### 데이터 일관성

1. **외래 키 제약조건**: 명시적으로 설정되지 않았으나 애플리케이션 레벨에서 관리
2. **NOT NULL 제약조건**: 필수 필드에 대해 적절히 설정
3. **기본값**: 타임스탬프 필드에 자동 설정

---

## 보안 및 권한

### Row Level Security (RLS)

Supabase의 RLS 정책을 통해 데이터 접근 제어:

```sql
-- 예시: 사용자별 데이터 접근 제한
ALTER TABLE front_user ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own data" ON front_user
    FOR SELECT USING (auth.uid() = auth_id);
```

### API 키 관리

- **Public Key**: 클라이언트 사이드에서 안전하게 사용 가능
- **Service Role Key**: 서버 사이드에서만 사용, 모든 권한 보유

---

이 문서는 실제 Supabase 데이터베이스 구조를 정확히 반영하며, 개발 및 운영에 필요한 모든 정보를 포함하고 있습니다. 