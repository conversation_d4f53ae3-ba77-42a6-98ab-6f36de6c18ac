# API 레퍼런스 문서

## 개요

Sportic365 Baseball Analytics Platform의 완전한 API 레퍼런스입니다. 실제 코드 구조와 Supabase 데이터베이스를 기반으로 모든 클래스, 메서드, 파라미터를 상세히 설명합니다.

## 목차

1. [데이터 접근 계층 (Repository)](#데이터-접근-계층-repository)
2. [도메인 모델](#도메인-모델)
3. [스포츠별 에이전트](#스포츠별-에이전트)
4. [애플리케이션 서비스](#애플리케이션-서비스)
5. [콘텐츠 생성](#콘텐츠-생성)
6. [설정 관리](#설정-관리)

---

## 데이터 접근 계층 (Repository)

### RealSupabaseRepository

실제 Supabase 데이터베이스 구조를 기반으로 한 메인 데이터 접근 클래스입니다.

```python
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository

# 초기화
repository = RealSupabaseRepository(url="your_supabase_url", key="your_supabase_key")
```

#### 핵심 메서드

##### `get_sns_target_matches() -> List[Dict[str, Any]]`

SNS 포스팅 대상 경기를 조회합니다.

**매개변수:** 없음

**반환값:**
- `List[Dict[str, Any]]`: 조건에 맞는 경기 데이터 목록

**동작 방식:**
- Supabase RPC 함수 `get_sns_target_matches()` 호출
- 경기 생성 가능 시간 윈도우 내의 경기만 필터링
- 스포츠별 생성 시간 제한 적용

**예시:**
```python
target_matches = repository.get_sns_target_matches()
print(f"Found {len(target_matches)} target matches")

for match in target_matches:
    print(f"Match: {match['home_team_name']} vs {match['away_team_name']}")
    print(f"Date: {match['match_date']} {match['match_time']}")
    print(f"Sport: {match['sports']}")
```

##### `get_match_data(match_id: str) -> Optional[Dict[str, Any]]`

기본 경기 정보를 `target_games` 테이블에서 조회합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID (예: "BSW025073025")

**반환값:**
- `Dict[str, Any]` 또는 `None`: 경기 데이터 또는 None

**예시:**
```python
match_data = repository.get_match_data("BSW025073025")
if match_data:
    print(f"Home: {match_data['home_team_name']}")
    print(f"Away: {match_data['away_team_name']}")
    print(f"Date: {match_data['match_date']}")
```

##### `get_sportic_content(match_id: str, fields: Optional[List[str]] = None) -> Optional[Dict[str, Any]]`

`sportic_contents` 테이블에서 분석 콘텐츠를 조회합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID
- `fields` (Optional[List[str]]): 조회할 필드 목록 (기본값: 모든 필드)

**반환값:**
- `Dict[str, Any]` 또는 `None`: 분석 콘텐츠 또는 None

**사용 가능한 필드:**
- `h2h_content`: 상대전적 분석 콘텐츠
- `h2h_wdl_summary`: 상대전적 승무패 요약
- `h2h_wdl_matches`: 상대전적 경기 기록
- `home_wdl_summary`: 홈팀 승무패 요약
- `away_wdl_summary`: 원정팀 승무패 요약
- `odds_analysis`: 배당률 분석
- `implied_probabilities`: 암시 확률

**예시:**
```python
# 모든 콘텐츠 조회
content = repository.get_sportic_content("BSW025073025")

# 특정 필드만 조회
h2h_data = repository.get_sportic_content(
    "BSW025073025", 
    fields=["h2h_wdl_summary", "h2h_wdl_matches"]
)
```

##### `get_naver_text(match_id: str) -> Optional[str]`

`sportic_sns` 테이블에서 네이버 포스팅 텍스트를 조회합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID

**반환값:**
- `str` 또는 `None`: 네이버 포스팅 텍스트 또는 None

**예시:**
```python
naver_text = repository.get_naver_text("BSW025073025")
if naver_text:
    print(f"Naver content: {naver_text[:100]}...")
```

##### `get_betting_picks(match_id: str) -> Optional[Dict[str, Any]]`

`sportic_pick` 테이블에서 베팅 픽 정보를 조회합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID

**반환값:**
- `Dict[str, Any]` 또는 `None`: 베팅 픽 데이터 또는 None

**예시:**
```python
picks = repository.get_betting_picks("BSW025073025")
if picks:
    print(f"Pick data: {picks['sportic_pick']}")
```

##### `get_team_stats(match_id: str) -> List[Dict[str, Any]]`

야구 경기의 상세 팀 통계를 `team_stats` 테이블에서 조회합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID

**반환값:**
- `List[Dict[str, Any]]`: 팀 통계 데이터 목록 (홈팀, 원정팀)

**포함 데이터:**
- `pitcher_profile`: 투수 프로필
- `pitcher_stats`: 투수 통계
- `season_stats`: 시즌 통계
- `season_summary`: 시즌 요약
- `recent_matches`: 최근 경기 기록
- `recent_matches_summary`: 최근 경기 요약

**예시:**
```python
team_stats = repository.get_team_stats("BSW025073025")
for stats in team_stats:
    print(f"Team: {stats['team_name']} ({stats['team_role']})")
    if stats['pitcher_profile']:
        print(f"Pitcher: {stats['pitcher_profile']['name']}")
```

#### WDL (Win-Draw-Loss) 데이터 메서드

##### `get_h2h_wdl_summary(match_id: str) -> Optional[Dict[str, Any]]`

상대전적 승무패 요약 데이터를 조회합니다.

##### `get_home_wdl_summary(match_id: str) -> Optional[Dict[str, Any]]`

홈팀의 승무패 요약 데이터를 조회합니다.

##### `get_away_wdl_summary(match_id: str) -> Optional[Dict[str, Any]]`

원정팀의 승무패 요약 데이터를 조회합니다.

##### `get_h2h_wdl_matches(match_id: str) -> Optional[List[Dict[str, Any]]]`

상대전적 경기 기록을 조회합니다.

#### 완전한 데이터 조회 메서드

##### `get_complete_match_data(match_id: str) -> Optional[Dict[str, Any]]`

모든 소스에서 데이터를 조합하여 완전한 경기 데이터를 제공합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID

**반환값:**
- `Dict[str, Any]` 또는 `None`: 통합된 경기 데이터

**포함 데이터:**
- `target_games` 테이블의 기본 경기 정보
- `sportic_contents` 테이블의 분석 콘텐츠
- `sportic_sns` 테이블의 네이버 텍스트
- `sportic_pick` 테이블의 베팅 픽
- `team_stats` 테이블의 팀 통계 (야구만)

**예시:**
```python
complete_data = repository.get_complete_match_data("BSW025073025")
if complete_data:
    print(f"Match: {complete_data['home_team_name']} vs {complete_data['away_team_name']}")
    print(f"H2H Summary: {complete_data.get('h2h_wdl_summary')}")
    print(f"Naver Text: {complete_data.get('naver_text', 'N/A')}")
    print(f"Team Stats: {len(complete_data.get('team_stats', []))} records")
```

#### 유틸리티 메서드

##### `is_baseball(match_data: Dict[str, Any]) -> bool`

경기가 야구인지 확인합니다.

##### `_is_within_creation_window(match_data: Dict[str, Any]) -> bool`

경기가 콘텐츠 생성 가능 시간 윈도우 내에 있는지 확인합니다.

**시간 윈도우:**
- 야구: 경기 시작 3일 전부터 경기 시작 1시간 전까지
- 축구: 경기 시작 2일 전부터 경기 시작 2시간 전까지
- 농구/배구: 경기 시작 1일 전부터 경기 시작 1시간 전까지

---

### SupabaseMCPRepository

MCP 통합이 포함된 향상된 레포지토리 클래스입니다.

```python
from baseball_analytics.infrastructure.supabase_mcp_repository import SupabaseMCPRepository

repository = SupabaseMCPRepository(url="your_supabase_url", key="your_supabase_key")
```

#### 추가 메서드

##### `get_match_by_id(match_id: str) -> Optional[Match]`

경기 ID로 `Match` 도메인 모델을 조회합니다.

**반환값:**
- `Match` 객체 또는 `None`

##### `get_team_by_id(team_id: str) -> Optional[Team]`

팀 ID로 `Team` 도메인 모델을 조회합니다.

##### `get_historical_matches(home_team_id: str, away_team_id: str, limit: int = 10) -> List[Match]`

두 팀 간의 과거 경기 기록을 조회합니다.

---

### EnhancedSupabaseRepository

SOLID 원칙을 적용한 향상된 레포지토리 구현체입니다.

```python
from baseball_analytics.infrastructure.enhanced_database import (
    EnhancedSupabaseRepository, 
    DatabaseConnectionManager
)

connection_manager = DatabaseConnectionManager(url, key)
repository = EnhancedSupabaseRepository(connection_manager)
```

#### 향상된 기능

- **데이터 검증**: Pydantic 모델을 사용한 자동 데이터 검증
- **에러 처리**: 구조화된 예외 처리 및 로깅
- **연결 관리**: 데이터베이스 연결의 효율적 관리
- **타입 안전성**: 완전한 타입 힌트 지원

---

## 도메인 모델

### Match

경기 정보를 나타내는 핵심 도메인 모델입니다.

```python
from baseball_analytics.domain.models import Match, Team, SportType, MatchStatus

match = Match(
    match_id="BSW025073025",
    sport=SportType.BASEBALL,
    home_team=home_team,
    away_team=away_team,
    match_date=datetime.now(),
    status=MatchStatus.SCHEDULED,
    venue="잠실야구장",
    season="2024",
    home_score=5,
    away_score=3
)
```

#### 속성

| 속성명 | 타입 | 설명 |
|--------|------|------|
| `match_id` | str | 경기 고유 ID |
| `sport` | SportType | 스포츠 종목 |
| `home_team` | Team | 홈팀 정보 |
| `away_team` | Team | 원정팀 정보 |
| `match_date` | datetime | 경기 일시 |
| `status` | MatchStatus | 경기 상태 |
| `venue` | Optional[str] | 경기장 |
| `season` | Optional[str] | 시즌 |
| `home_score` | Optional[int] | 홈팀 득점 |
| `away_score` | Optional[int] | 원정팀 득점 |

### Team

팀 정보를 나타내는 도메인 모델입니다.

```python
team = Team(
    id="SSG",
    name="SSG 랜더스",
    short_name="SSG"
)
```

#### 속성

| 속성명 | 타입 | 설명 |
|--------|------|------|
| `id` | str | 팀 고유 ID |
| `name` | str | 팀 전체 이름 |
| `short_name` | str | 팀 약칭 |

### TeamStats

팀 통계 정보를 나타내는 도메인 모델입니다.

```python
stats = TeamStats(
    team_id="SSG",
    match_id="BSW025073025",
    runs=5,
    hits=12,
    errors=1,
    batting_avg=0.285,
    era=3.45
)
```

### SportType

지원하는 스포츠 종목을 나타내는 열거형입니다.

```python
from baseball_analytics.domain.models import SportType

# 사용 가능한 스포츠 종목
SportType.BASEBALL    # 야구
SportType.SOCCER      # 축구
SportType.BASKETBALL  # 농구
SportType.VOLLEYBALL  # 배구
```

### MatchStatus

경기 상태를 나타내는 열거형입니다.

```python
from baseball_analytics.domain.models import MatchStatus

MatchStatus.SCHEDULED   # 예정
MatchStatus.LIVE        # 진행 중
MatchStatus.FINISHED    # 종료
MatchStatus.POSTPONED   # 연기
MatchStatus.CANCELLED   # 취소
```

---

## 스포츠별 에이전트

### BaseballAgent

야구 전문 분석 에이전트입니다.

```python
from baseball_analytics.agents.baseball_agent import BaseballAgent
from baseball_analytics.llm.providers import OpenAIProvider

llm_provider = OpenAIProvider(api_key="your_api_key")
agent = BaseballAgent(llm_provider=llm_provider, repository=repository)
```

#### 메서드

##### `analyze_match(match_id: str) -> Dict[str, Any]`

야구 경기를 종합적으로 분석합니다.

**매개변수:**
- `match_id` (str): 경기 고유 ID

**반환값:**
- `Dict[str, Any]`: 분석 결과

**분석 내용:**
- 팀 통계 분석
- 투수 분석
- 타자 분석
- 상대전적 분석
- 경기 예측

**예시:**
```python
analysis = agent.analyze_match("BSW025073025")
print(f"Analysis: {analysis['summary']}")
print(f"Prediction: {analysis['prediction']}")
```

##### `generate_pitcher_analysis(pitcher_data: Dict[str, Any]) -> str`

투수 데이터를 분석하여 텍스트를 생성합니다.

##### `generate_team_comparison(home_stats: Dict, away_stats: Dict) -> str`

두 팀의 통계를 비교 분석합니다.

### SoccerAgent

축구 전문 분석 에이전트입니다.

```python
from baseball_analytics.agents.soccer_agent import SoccerAgent

agent = SoccerAgent(llm_provider=llm_provider, repository=repository)
```

#### 특화 기능

- 포메이션 분석
- 선수 개별 통계
- 리그 순위 고려
- 최근 폼 분석

### BasketballAgent

농구 전문 분석 에이전트입니다.

#### 특화 기능

- 쿼터별 득점 패턴
- 리바운드/어시스트 분석
- 3점슛 성공률 분석
- 페이스 분석

### VolleyballAgent

배구 전문 분석 에이전트입니다.

#### 특화 기능

- 세트별 분석
- 공격 성공률
- 블로킹 효율성
- 서브 에이스 분석

---

## 애플리케이션 서비스

### AnalysisService

경기 분석을 담당하는 서비스 클래스입니다.

```python
from baseball_analytics.application.analysis_service import AnalysisService

service = AnalysisService(
    repository=repository,
    llm_provider=llm_provider
)
```

#### 메서드

##### `analyze_matches(match_ids: List[str]) -> List[Dict[str, Any]]`

여러 경기를 일괄 분석합니다.

##### `get_analysis_context(match_id: str) -> AnalysisContext`

경기 분석에 필요한 모든 컨텍스트를 수집합니다.

### ContentService

콘텐츠 생성을 담당하는 서비스 클래스입니다.

```python
from baseball_analytics.application.content_service import ContentService

service = ContentService(
    repository=repository,
    content_generator=content_generator
)
```

#### 메서드

##### `generate_match_content(match_id: str, content_type: str) -> str`

특정 타입의 경기 콘텐츠를 생성합니다.

**콘텐츠 타입:**
- `preview`: 경기 프리뷰
- `analysis`: 상세 분석
- `prediction`: 경기 예측
- `summary`: 경기 요약

---

## 콘텐츠 생성

### ContentGenerator

범용 콘텐츠 생성 클래스입니다.

```python
from baseball_analytics.content.generators import ContentGenerator

generator = ContentGenerator(llm_provider=llm_provider)
```

#### 메서드

##### `generate_content(template: str, context: Dict[str, Any]) -> str`

템플릿과 컨텍스트를 기반으로 콘텐츠를 생성합니다.

##### `generate_match_preview(match_data: Dict[str, Any]) -> str`

경기 프리뷰 콘텐츠를 생성합니다.

### SNSGenerator

SNS 플랫폼별 콘텐츠 생성 클래스입니다.

```python
from baseball_analytics.content.sns_generator import SNSGenerator

generator = SNSGenerator(llm_provider=llm_provider)
```

#### 메서드

##### `generate_naver_post(match_data: Dict[str, Any]) -> Dict[str, Any]`

네이버 블로그 포스트를 생성합니다.

**반환값 구조:**
```json
{
  "title": "경기 제목",
  "content": "포스트 내용",
  "tags": ["태그1", "태그2"],
  "summary": "요약"
}
```

##### `generate_facebook_post(match_data: Dict[str, Any]) -> Dict[str, Any]`

페이스북 포스트를 생성합니다.

##### `generate_instagram_post(match_data: Dict[str, Any]) -> str`

인스타그램 포스트를 생성합니다.

##### `generate_x_post(match_data: Dict[str, Any]) -> Dict[str, Any]`

X(트위터) 포스트를 생성합니다.

---

## 설정 관리

### GlobalConfig

전역 설정을 관리하는 클래스입니다.

```python
from baseball_analytics.config.global_config import GlobalConfig

config = GlobalConfig()
```

#### 주요 설정

##### 데이터베이스 설정

```python
config.SUPABASE_URL      # Supabase URL
config.SUPABASE_KEY      # Supabase API 키
```

##### LLM 설정

```python
config.LLM_OPENAI_API_KEY        # OpenAI API 키
config.LLM_ANTHROPIC_API_KEY     # Anthropic API 키
config.LLM_GOOGLE_API_KEY        # Google API 키
config.LLM_DEFAULT_PROVIDER      # 기본 LLM 제공자
config.LLM_DEFAULT_MODEL         # 기본 모델명
```

##### 애플리케이션 설정

```python
config.APP_ENVIRONMENT    # 환경 (development/production)
config.LOG_LEVEL         # 로그 레벨
config.DEBUG_MODE        # 디버그 모드
```

### 환경 변수 설정

`.env` 파일 예시:

```env
# 데이터베이스 설정
DB_SUPABASE_URL=https://your-project.supabase.co
DB_SUPABASE_KEY=your_supabase_key_here

# LLM 설정
LLM_OPENAI_API_KEY=sk-your_openai_key_here
LLM_ANTHROPIC_API_KEY=sk-ant-your_anthropic_key_here
LLM_GOOGLE_API_KEY=your_google_key_here
LLM_DEFAULT_PROVIDER=openai
LLM_DEFAULT_MODEL=gpt-4o-mini

# 애플리케이션 설정
APP_ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG_MODE=true

# 시간대 설정
TIMEZONE=Asia/Seoul

# 콘텐츠 생성 설정
CONTENT_MAX_LENGTH=2000
CONTENT_LANGUAGE=ko

# SNS 설정
SNS_ENABLE_NAVER=true
SNS_ENABLE_FACEBOOK=true
SNS_ENABLE_INSTAGRAM=true
SNS_ENABLE_X=true
```

---

## 에러 처리 및 로깅

### 표준 에러 처리

모든 메서드는 표준화된 에러 처리를 제공합니다:

```python
try:
    result = repository.get_match_data("invalid_id")
except Exception as e:
    logger.error(f"Error retrieving match: {e}")
    return None
```

### 로깅 설정

```python
import logging

# 로그 레벨 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
```

---

## 성능 최적화

### 배치 처리

여러 경기를 효율적으로 처리하기 위한 배치 메서드:

```python
# 여러 경기 동시 처리
match_ids = ["BSW025073025", "BSW025073026", "BSW025073027"]
results = []

for match_id in match_ids:
    result = repository.get_complete_match_data(match_id)
    if result:
        results.append(result)
```

### 캐싱

자주 사용되는 데이터의 캐싱:

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def get_cached_team_info(team_id: str):
    return repository.get_team_by_id(team_id)
```

### 연결 풀링

데이터베이스 연결의 효율적 관리:

```python
# 연결 관리자 사용
connection_manager = DatabaseConnectionManager(url, key)
repository = EnhancedSupabaseRepository(connection_manager)
```

---

## 테스트 및 디버깅

### 단위 테스트

```python
import pytest
from unittest.mock import Mock

def test_get_match_data():
    # Mock repository
    mock_client = Mock()
    repository = RealSupabaseRepository("test_url", "test_key")
    repository.client = mock_client
    
    # Test
    result = repository.get_match_data("test_match_id")
    assert result is not None
```

### 통합 테스트

```python
def test_complete_workflow():
    # 실제 데이터베이스 연결 테스트
    repository = RealSupabaseRepository(
        config.SUPABASE_URL,
        config.SUPABASE_KEY
    )
    
    # 전체 워크플로우 테스트
    matches = repository.get_sns_target_matches()
    assert len(matches) > 0
    
    match_data = repository.get_complete_match_data(matches[0]["match_id"])
    assert match_data is not None
```

---

## 마이그레이션 가이드

### 버전 업그레이드

기존 코드를 새 API로 마이그레이션하는 방법:

```python
# 이전 버전
from db.database import get_match_data

# 새 버전
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository

repository = RealSupabaseRepository(url, key)
match_data = repository.get_match_data(match_id)
```

### 호환성 유지

기존 코드와의 호환성을 위한 래퍼 함수:

```python
def legacy_get_match_data(match_id: str):
    """Legacy compatibility wrapper."""
    repository = RealSupabaseRepository(config.SUPABASE_URL, config.SUPABASE_KEY)
    return repository.get_match_data(match_id)
```

---

이 API 레퍼런스는 실제 코드 구조와 Supabase 데이터베이스를 정확히 반영하며, 개발에 필요한 모든 정보를 포함하고 있습니다. 각 메서드의 매개변수, 반환값, 사용 예시가 상세히 기술되어 있어 개발자가 쉽게 활용할 수 있습니다. 