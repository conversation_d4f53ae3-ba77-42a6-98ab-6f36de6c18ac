# 사용자 가이드

## 개요

이 가이드는 Sportic365 Baseball Analytics Platform을 사용하는 방법을 단계별로 설명합니다. 초보자부터 고급 사용자까지 모든 레벨의 사용자를 대상으로 합니다.

## 목차

1. [시작하기](#시작하기)
2. [기본 사용법](#기본-사용법)
3. [고급 기능](#고급-기능)
4. [실제 사용 사례](#실제-사용-사례)
5. [문제 해결](#문제-해결)

---

## 시작하기

### 환경 설정

#### 1. 필수 요구사항

- Python 3.9 이상
- Supabase 계정 및 프로젝트
- OpenAI, Google, 또는 Anthropic API 키 중 하나 이상

#### 2. 설치

```bash
# 저장소 클론
git clone https://github.com/your-org/sportic365.git
cd sportic365

# 의존성 설치
pip install -r requirements.txt

# 개발 환경 설정 (선택사항)
pip install -e ".[dev]"
```

#### 3. 환경 변수 설정

`.env` 파일을 생성하고 다음 내용을 추가하세요:

```env
# 데이터베이스 설정
DB_SUPABASE_URL=your_supabase_url_here
DB_SUPABASE_KEY=your_supabase_key_here

# LLM 설정
LLM_OPENAI_API_KEY=your_openai_api_key_here
LLM_DEFAULT_PROVIDER=openai
LLM_DEFAULT_MODEL=gpt-4o-mini

# 애플리케이션 설정
APP_ENVIRONMENT=development
LOG_LEVEL=INFO
```

#### 4. 연결 테스트

```python
from baseball_analytics.infrastructure.config import get_settings
from baseball_analytics.infrastructure.database import SupabaseConnection

settings = get_settings()
connection = SupabaseConnection(
    settings.database.supabase_url,
    settings.database.supabase_key
)

# 연결 테스트
client = connection.connect()
print("데이터베이스 연결 성공!")
```

---

## 기본 사용법

### 1. 간단한 경기 분석

가장 기본적인 사용법부터 시작해보겠습니다.

```python
from baseball_analytics.real_main import main

# 특정 경기 분석
if __name__ == "__main__":
    match_id = "BSW025073025"  # 실제 경기 ID로 변경
    main()
```

### 2. 경기 목록 조회

분석 가능한 경기들을 먼저 확인해보세요.

```python
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository
from baseball_analytics.infrastructure.config import get_settings

# 설정 로드
settings = get_settings()

# Repository 초기화
repository = RealSupabaseRepository(
    settings.database.supabase_url,
    settings.database.supabase_key
)

# 대상 경기 목록 조회
matches = repository.get_sns_target_matches()
print(f"분석 가능한 경기: {len(matches)}개")

for match in matches[:5]:  # 처음 5개만 출력
    print(f"- {match['match_id']}: {match.get('home_team_name')} vs {match.get('away_team_name')}")
```

### 3. 경기 데이터 조회

특정 경기의 상세 데이터를 조회해보세요.

```python
# 경기 기본 정보
match_id = "BSW025073025"
match_data = repository.get_match_data(match_id)

if match_data:
    print(f"경기: {match_data['home_team_name']} vs {match_data['away_team_name']}")
    print(f"날짜: {match_data['match_date']}")
    print(f"리그: {match_data['league']}")

# 야구 팀 통계 (야구 경기인 경우)
if match_data and match_data.get('sports') == 'baseball':
    team_stats = repository.get_team_stats(match_id)
    for stats in team_stats:
        print(f"{stats['team_name']}: {stats['runs']}점, {stats['hits']}안타")
```

### 4. AI 분석 실행

실제 AI 분석을 실행해보세요.

```python
from baseball_analytics.agents.real_baseball_agent import RealBaseballAgent
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager

# LLM 매니저 설정
llm_manager = setup_llm_manager(settings.llm)

# 야구 에이전트 초기화
agent = RealBaseballAgent(repository, llm_manager)

# 포괄적 분석 실행
match_id = "BSW025073025"
analysis = agent.analyze_match_comprehensive(match_id)

print("=== 분석 결과 ===")
print(analysis.get("summary", "분석 결과가 없습니다."))
```

---

## 고급 기능

### 1. 멀티 스포츠 분석

여러 스포츠를 동시에 분석할 수 있습니다.

```python
from baseball_analytics.real_sport_main import main as sport_main

# 멀티 스포츠 분석 실행
sport_main()
```

### 2. 배치 분석

여러 경기를 한 번에 분석합니다.

```python
from concurrent.futures import ThreadPoolExecutor
import time

def analyze_single_match(match_id):
    """단일 경기 분석"""
    try:
        analysis = agent.analyze_match_comprehensive(match_id)
        return {
            "match_id": match_id,
            "status": "success",
            "analysis": analysis
        }
    except Exception as e:
        return {
            "match_id": match_id,
            "status": "error",
            "error": str(e)
        }

# 여러 경기 동시 분석
match_ids = ["BSW025073025", "BSW025073026", "BSW025073027"]

with ThreadPoolExecutor(max_workers=3) as executor:
    results = list(executor.map(analyze_single_match, match_ids))

# 결과 출력
for result in results:
    if result["status"] == "success":
        print(f"✅ {result['match_id']}: 분석 완료")
    else:
        print(f"❌ {result['match_id']}: {result['error']}")
```

### 3. 커스텀 콘텐츠 생성

다양한 톤과 스타일로 콘텐츠를 생성할 수 있습니다.

```python
# 분석 결과를 바탕으로 다양한 콘텐츠 생성
analysis_result = "홈팀이 최근 5경기에서 4승을 거두며 좋은 흐름을 보이고 있습니다..."

# 다양한 톤으로 소셜 미디어 콘텐츠 생성
tones = ["professional", "enthusiastic", "casual"]

for tone in tones:
    content = agent.generate_social_media_content(analysis_result, tone=tone)
    print(f"\n=== {tone.upper()} 톤 ===")
    print(content)
```

### 4. 데이터 내보내기

분석 결과를 다양한 형식으로 내보낼 수 있습니다.

```python
import json
import csv
from datetime import datetime

def export_analysis_to_json(analysis_results, filename=None):
    """분석 결과를 JSON으로 내보내기"""
    if not filename:
        filename = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2)
    
    print(f"분석 결과가 {filename}에 저장되었습니다.")

def export_match_list_to_csv(matches, filename=None):
    """경기 목록을 CSV로 내보내기"""
    if not filename:
        filename = f"matches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        if matches:
            fieldnames = matches[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(matches)
    
    print(f"경기 목록이 {filename}에 저장되었습니다.")

# 사용 예시
matches = repository.get_sns_target_matches()
export_match_list_to_csv(matches)
```

---

## 실제 사용 사례

### 사례 1: 일일 경기 분석 리포트

매일 오전에 실행하여 당일 경기들을 분석하는 스크립트입니다.

```python
#!/usr/bin/env python3
"""
일일 경기 분석 스크립트
매일 오전 실행하여 당일 경기 분석
"""

from datetime import datetime, timedelta
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository
from baseball_analytics.agents.real_baseball_agent import RealBaseballAgent
from baseball_analytics.infrastructure.config import get_settings
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager

def daily_analysis_report():
    """일일 분석 리포트 생성"""
    print(f"=== {datetime.now().strftime('%Y-%m-%d')} 일일 경기 분석 리포트 ===")
    
    # 초기화
    settings = get_settings()
    repository = RealSupabaseRepository(settings.database.supabase_url, settings.database.supabase_key)
    llm_manager = setup_llm_manager(settings.llm)
    agent = RealBaseballAgent(repository, llm_manager)
    
    # 대상 경기 조회
    matches = repository.get_sns_target_matches()
    today_matches = [
        match for match in matches 
        if match.get('match_date') == datetime.now().strftime('%Y-%m-%d')
    ]
    
    print(f"오늘 경기 수: {len(today_matches)}개")
    
    # 각 경기 분석
    for i, match in enumerate(today_matches, 1):
        match_id = match['match_id']
        print(f"\n[{i}/{len(today_matches)}] {match_id} 분석 중...")
        
        try:
            analysis = agent.analyze_match_comprehensive(match_id)
            
            print(f"✅ 분석 완료")
            print(f"   경기: {match.get('home_team_name')} vs {match.get('away_team_name')}")
            print(f"   요약: {analysis.get('summary', '요약 없음')[:100]}...")
            
            # SNS 콘텐츠 생성
            social_content = agent.generate_social_media_content(
                analysis.get('summary', ''), 
                tone="professional"
            )
            print(f"   SNS 콘텐츠: {social_content[:50]}...")
            
        except Exception as e:
            print(f"❌ 분석 실패: {e}")
    
    print("\n=== 일일 리포트 완료 ===")

if __name__ == "__main__":
    daily_analysis_report()
```

### 사례 2: 팀 성과 추적

특정 팀의 성과를 지속적으로 추적하는 스크립트입니다.

```python
def track_team_performance(team_id, days=30):
    """팀 성과 추적"""
    print(f"=== {team_id} 팀 성과 추적 (최근 {days}일) ===")
    
    # 최근 경기 조회
    matches = repository.get_sns_target_matches()
    team_matches = [
        match for match in matches
        if (match.get('home_team') == team_id or match.get('away_team') == team_id)
        and _is_within_days(match.get('match_date'), days)
    ]
    
    wins = 0
    losses = 0
    total_runs = 0
    
    print(f"최근 {days}일간 경기 수: {len(team_matches)}개")
    
    for match in team_matches:
        match_id = match['match_id']
        team_stats = repository.get_team_stats(match_id)
        
        for stats in team_stats:
            if stats['team_id'] == team_id:
                runs = stats.get('runs', 0)
                total_runs += runs
                
                # 승패 계산 (간단한 로직)
                if stats.get('wins', 0) > stats.get('losses', 0):
                    wins += 1
                else:
                    losses += 1
                
                print(f"  {match['match_date']}: {runs}점")
    
    # 통계 출력
    if len(team_matches) > 0:
        avg_runs = total_runs / len(team_matches)
        win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0
        
        print(f"\n=== 통계 요약 ===")
        print(f"승률: {win_rate:.2%} ({wins}승 {losses}패)")
        print(f"평균 득점: {avg_runs:.1f}점")

def _is_within_days(date_str, days):
    """날짜가 지정된 일수 내에 있는지 확인"""
    try:
        match_date = datetime.strptime(date_str, '%Y-%m-%d')
        cutoff_date = datetime.now() - timedelta(days=days)
        return match_date >= cutoff_date
    except:
        return False

# 사용 예시
track_team_performance("LG", days=30)
```

### 사례 3: 자동 SNS 포스팅

분석 결과를 자동으로 SNS에 포스팅하는 시스템입니다.

```python
def auto_sns_posting():
    """자동 SNS 포스팅"""
    print("=== 자동 SNS 포스팅 시작 ===")
    
    # 포스팅되지 않은 경기 조회
    unposted_matches = []
    all_matches = repository.get_sns_target_matches()
    
    for match in all_matches:
        match_id = match['match_id']
        
        # SNS 포스팅 상태 확인
        naver_text = repository.get_naver_text(match_id)
        if not naver_text:  # 아직 포스팅되지 않음
            unposted_matches.append(match)
    
    print(f"포스팅 대상 경기: {len(unposted_matches)}개")
    
    for match in unposted_matches[:5]:  # 한 번에 5개까지만 처리
        match_id = match['match_id']
        
        try:
            # 경기 분석
            analysis = agent.analyze_match_comprehensive(match_id)
            
            # SNS 콘텐츠 생성
            social_content = agent.generate_social_media_content(
                analysis.get('summary', ''),
                tone="enthusiastic"
            )
            
            # 실제 포스팅은 여기서 구현
            # post_to_social_media(social_content)
            
            print(f"✅ {match_id}: SNS 콘텐츠 생성 완료")
            print(f"   내용: {social_content[:100]}...")
            
        except Exception as e:
            print(f"❌ {match_id}: 포스팅 실패 - {e}")

# 사용 예시 (cron job으로 실행 가능)
if __name__ == "__main__":
    auto_sns_posting()
```

---

## 문제 해결

### 자주 발생하는 문제들

#### 1. 데이터베이스 연결 오류

**문제:** `ConnectionError: Failed to connect to Supabase`

**해결방법:**
```python
# 연결 설정 확인
from baseball_analytics.infrastructure.config import get_settings

settings = get_settings()
print(f"Supabase URL: {settings.database.supabase_url}")
print(f"API Key 길이: {len(settings.database.supabase_key)}")

# .env 파일 확인
# DB_SUPABASE_URL과 DB_SUPABASE_KEY가 올바르게 설정되어 있는지 확인
```

#### 2. LLM API 오류

**문제:** `OpenAI API Error: Invalid API key`

**해결방법:**
```python
# API 키 확인
import os
print(f"OpenAI API Key: {os.getenv('LLM_OPENAI_API_KEY', 'Not set')[:10]}...")

# 다른 LLM 제공자 사용
# .env 파일에서 LLM_DEFAULT_PROVIDER를 변경
# LLM_DEFAULT_PROVIDER=google  # 또는 anthropic
```

#### 3. 경기 데이터 없음

**문제:** `No matches found for analysis`

**해결방법:**
```python
# 데이터베이스에서 사용 가능한 경기 확인
matches = repository.get_sns_target_matches()
print(f"전체 경기 수: {len(matches)}")

# 특정 날짜 범위의 경기 확인
from datetime import datetime, timedelta
recent_matches = [
    match for match in matches
    if match.get('match_date') >= (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
]
print(f"최근 7일 경기 수: {len(recent_matches)}")
```

#### 4. 메모리 부족

**문제:** 대량 분석 시 메모리 부족

**해결방법:**
```python
# 배치 크기 줄이기
def analyze_matches_in_batches(match_ids, batch_size=5):
    """배치 단위로 경기 분석"""
    for i in range(0, len(match_ids), batch_size):
        batch = match_ids[i:i+batch_size]
        print(f"배치 {i//batch_size + 1} 처리 중... ({len(batch)}개 경기)")
        
        for match_id in batch:
            try:
                analysis = agent.analyze_match_comprehensive(match_id)
                # 결과 처리
            except Exception as e:
                print(f"오류: {match_id} - {e}")
        
        # 메모리 정리를 위한 잠시 대기
        import time
        time.sleep(1)

# 사용 예시
match_ids = [match['match_id'] for match in matches[:20]]
analyze_matches_in_batches(match_ids, batch_size=3)
```

### 로그 확인

문제 발생 시 로그를 확인하여 원인을 파악하세요.

```python
import logging

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('baseball_analytics.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 분석 시 로깅
try:
    result = agent.analyze_match_comprehensive(match_id)
    logger.info(f"분석 성공: {match_id}")
except Exception as e:
    logger.error(f"분석 실패: {match_id} - {e}")
```

### 성능 최적화

#### 1. 캐싱 사용

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def get_cached_match_data(match_id):
    return repository.get_match_data(match_id)

@lru_cache(maxsize=100)
def get_cached_team_stats(match_id):
    return repository.get_team_stats(match_id)
```

#### 2. 비동기 처리

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def async_analyze_matches(match_ids):
    """비동기 경기 분석"""
    loop = asyncio.get_event_loop()
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        tasks = [
            loop.run_in_executor(executor, analyze_single_match, match_id)
            for match_id in match_ids
        ]
        
        results = await asyncio.gather(*tasks)
    
    return results

# 사용 예시
match_ids = ["BSW025073025", "BSW025073026"]
results = asyncio.run(async_analyze_matches(match_ids))
```

---

## 추가 리소스

### 유용한 스크립트들

프로젝트 루트 디렉토리의 다음 스크립트들을 참고하세요:

- `run_real_analysis.py`: 실제 데이터 분석 실행
- `run_multi_sport.py`: 멀티 스포츠 분석
- `test_real_system.py`: 시스템 테스트
- `explore_supabase.py`: 데이터베이스 구조 탐색

### 문서들

- `REAL_USAGE.md`: 실제 사용법 상세 가이드
- `REAL_MULTI_SPORT_USAGE.md`: 멀티 스포츠 사용법
- `docs/DATABASE_SCHEMA.md`: 데이터베이스 스키마 문서
- `docs/API_REFERENCE.md`: API 레퍼런스

### 지원

문제가 발생하거나 질문이 있으시면:

1. GitHub Issues에 문제를 보고해주세요
2. 로그 파일과 함께 상세한 오류 내용을 포함해주세요
3. 재현 가능한 최소한의 코드 예시를 제공해주세요 