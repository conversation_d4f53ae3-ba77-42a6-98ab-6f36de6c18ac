# 시스템 아키텍처 문서

## 개요

Sportic365 Baseball Analytics Platform은 현대적인 소프트웨어 아키텍처 원칙을 따라 설계된 스포츠 분석 플랫폼입니다. 이 문서는 시스템의 전체적인 구조와 각 컴포넌트 간의 관계를 설명합니다.

## 아키텍처 원칙

### 1. SOLID 원칙

#### Single Responsibility Principle (SRP)
- 각 클래스는 하나의 책임만 가집니다
- `SupabaseRepository`: 데이터 접근만 담당
- `BaseballAgent`: 야구 분석만 담당
- `ContentGenerator`: 콘텐츠 생성만 담당

#### Open/Closed Principle (OCP)
- 새로운 기능 추가 시 기존 코드 수정 없이 확장 가능
- 새로운 스포츠 에이전트 추가 가능
- 새로운 LLM 제공자 추가 가능

#### Liskov Substitution Principle (LSP)
- 모든 Repository 구현체는 상호 교체 가능
- 모든 Agent는 동일한 인터페이스 제공

#### Interface Segregation Principle (ISP)
- 각 인터페이스는 특정 목적에 맞게 분리
- `DataRepository`, `LLMProvider`, `ContentGenerator` 등

#### Dependency Inversion Principle (DIP)
- 고수준 모듈은 저수준 모듈에 의존하지 않음
- 추상화에 의존하며 의존성 주입 사용

### 2. Domain-Driven Design (DDD)

```
baseball_analytics/
├── domain/           # 도메인 로직과 엔티티
├── application/      # 애플리케이션 서비스
├── infrastructure/   # 외부 시스템 연동
└── agents/          # AI 에이전트 (특화 도메인)
```

## 시스템 구조

### 레이어 아키텍처

```mermaid
graph TB
    UI[사용자 인터페이스] --> APP[애플리케이션 계층]
    APP --> DOMAIN[도메인 계층]
    APP --> INFRA[인프라스트럭처 계층]
    INFRA --> DB[(Supabase DB)]
    INFRA --> LLM[LLM APIs]
```

#### 1. 도메인 계층 (Domain Layer)
**위치:** `baseball_analytics/domain/`

**책임:**
- 비즈니스 엔티티와 값 객체 정의
- 도메인 규칙과 로직 구현
- 외부 의존성 없는 순수한 비즈니스 로직

**주요 컴포넌트:**
```python
# models.py
class Match:
    """경기 엔티티"""
    match_id: str
    sport_type: SportType
    home_team: Team
    away_team: Team
    # ... 기타 속성

class Team:
    """팀 엔티티"""
    id: str
    name: str
    short_name: str

class TeamStats:
    """팀 통계 값 객체"""
    runs: Optional[int]
    hits: Optional[int]
    # ... 기타 통계
```

#### 2. 애플리케이션 계층 (Application Layer)
**위치:** `baseball_analytics/application/`

**책임:**
- 유스케이스 구현
- 도메인 객체들의 조율
- 트랜잭션 관리

**주요 컴포넌트:**
```python
# services.py
class AnalysisService:
    """경기 분석 서비스"""
    def __init__(self, repository: DataRepository, llm_manager: LLMManager):
        self._repository = repository
        self._llm_manager = llm_manager
    
    def analyze_match(self, match_id: str) -> AnalysisResult:
        # 유스케이스 구현
        pass
```

#### 3. 인프라스트럭처 계층 (Infrastructure Layer)
**위치:** `baseball_analytics/infrastructure/`

**책임:**
- 외부 시스템과의 연동
- 데이터베이스 접근
- API 호출
- 설정 관리

**주요 컴포넌트:**

##### 데이터베이스 접근
```python
# database.py
class SupabaseRepository(DataRepository):
    """Supabase 데이터베이스 구현"""
    def get_match_by_id(self, match_id: str) -> Optional[Match]:
        # Supabase 특화 구현
        pass

# real_supabase_repository.py
class RealSupabaseRepository:
    """실제 데이터베이스 구조 최적화 구현"""
    def get_sns_target_matches(self) -> List[Dict[str, Any]]:
        # 실제 RPC 함수 호출
        pass
```

##### LLM 제공자
```python
# llm_providers.py
class OpenAIProvider(LLMProvider):
    """OpenAI API 구현"""
    pass

class GoogleProvider(LLMProvider):
    """Google AI API 구현"""
    pass
```

##### 설정 관리
```python
# config.py
class DatabaseSettings(BaseSettings):
    supabase_url: str
    supabase_key: str
    cache_ttl: int

class LLMSettings(BaseSettings):
    default_provider: str
    default_model: str
    openai_api_key: str
```

#### 4. AI 에이전트 계층 (Agents Layer)
**위치:** `baseball_analytics/agents/`

**책임:**
- 스포츠별 특화 분석
- AI 기반 콘텐츠 생성
- 도메인 지식 활용

**주요 컴포넌트:**
```python
# baseball_agents.py
class BaseballAgent:
    """야구 전용 AI 에이전트"""
    def analyze_match_context(self, context: AnalysisContext) -> str:
        # 야구 특화 분석
        pass

# real_baseball_agent.py
class RealBaseballAgent:
    """실제 데이터 사용 야구 에이전트"""
    def analyze_match_comprehensive(self, match_id: str) -> Dict[str, Any]:
        # 포괄적 경기 분석
        pass

# sport_specific_agents.py
class SportSpecificAgent(ABC):
    """스포츠별 에이전트 인터페이스"""
    @abstractmethod
    def analyze_match_context(self, context) -> str:
        pass
```

## 데이터 플로우

### 1. 경기 분석 플로우

```mermaid
sequenceDiagram
    participant User
    participant Service as AnalysisService
    participant Repo as Repository
    participant Agent as BaseballAgent
    participant LLM as LLM Provider
    participant DB as Supabase

    User->>Service: analyze_match(match_id)
    Service->>Repo: get_match_by_id(match_id)
    Repo->>DB: SELECT * FROM target_games
    DB-->>Repo: match_data
    Repo-->>Service: Match object
    
    Service->>Repo: get_team_stats(match_id)
    Repo->>DB: SELECT * FROM team_stats
    DB-->>Repo: stats_data
    Repo-->>Service: TeamStats list
    
    Service->>Agent: analyze_match_context(context)
    Agent->>LLM: generate_analysis(prompt)
    LLM-->>Agent: analysis_result
    Agent-->>Service: analysis_text
    
    Service-->>User: AnalysisResult
```

### 2. 멀티 스포츠 분석 플로우

```mermaid
flowchart TD
    A[멀티 스포츠 분석 시작] --> B[대상 경기 조회]
    B --> C{스포츠 종목 확인}
    
    C -->|야구| D[BaseballAgent 사용]
    C -->|축구| E[SoccerAgent 사용]
    C -->|농구| F[BasketballAgent 사용]
    C -->|배구| G[VolleyballAgent 사용]
    
    D --> H[야구 특화 분석]
    E --> I[축구 특화 분석]
    F --> J[농구 특화 분석]
    G --> K[배구 특화 분석]
    
    H --> L[결과 통합]
    I --> L
    J --> L
    K --> L
    
    L --> M[콘텐츠 생성]
    M --> N[분석 완료]
```

## 확장성 설계

### 1. 새로운 스포츠 추가

새로운 스포츠를 추가할 때는 다음 단계를 따릅니다:

```python
# 1. 도메인 모델 확장
class TennisStats(BaseModel):
    aces: int
    double_faults: int
    first_serve_percentage: float

# 2. 에이전트 구현
class TennisAgent(SportSpecificAgent):
    def analyze_match_context(self, context: AnalysisContext) -> str:
        # 테니스 특화 분석 로직
        pass

# 3. Repository 메서드 추가 (필요시)
class SupabaseRepository:
    def get_tennis_stats(self, match_id: str) -> List[TennisStats]:
        # 테니스 통계 조회
        pass

# 4. 팩토리에 등록
class AgentFactory:
    @staticmethod
    def create_agent(sport_type: SportType) -> SportSpecificAgent:
        if sport_type == SportType.TENNIS:
            return TennisAgent()
        # ... 기타 스포츠
```

### 2. 새로운 LLM 제공자 추가

```python
# 1. Provider 인터페이스 구현
class ClaudeProvider(LLMProvider):
    def generate_text(self, prompt: str, **kwargs) -> str:
        # Claude API 호출
        pass

# 2. 설정에 추가
class LLMSettings:
    claude_api_key: Optional[str] = None

# 3. 팩토리에 등록
class LLMProviderFactory:
    @staticmethod
    def create_provider(provider_type: str) -> LLMProvider:
        if provider_type == "claude":
            return ClaudeProvider()
        # ... 기타 제공자
```

### 3. 새로운 데이터 소스 추가

```python
# 1. Repository 인터페이스 구현
class MySQLRepository(DataRepository):
    def get_match_by_id(self, match_id: str) -> Optional[Match]:
        # MySQL 데이터베이스 접근
        pass

# 2. 연결 클래스 구현
class MySQLConnection(DatabaseConnection):
    def connect(self) -> Any:
        # MySQL 연결
        pass
```

## 성능 최적화

### 1. 캐싱 전략

```python
# 메모리 캐싱
@lru_cache(maxsize=100)
def get_cached_match(match_id: str) -> Optional[Match]:
    return repository.get_match_by_id(match_id)

# Redis 캐싱 (향후 구현)
class RedisCache:
    def get(self, key: str) -> Optional[Any]:
        pass
    
    def set(self, key: str, value: Any, ttl: int = 300):
        pass
```

### 2. 비동기 처리

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncAnalysisService:
    async def analyze_matches_batch(self, match_ids: List[str]) -> List[AnalysisResult]:
        loop = asyncio.get_event_loop()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            tasks = [
                loop.run_in_executor(executor, self.analyze_match, match_id)
                for match_id in match_ids
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return [r for r in results if not isinstance(r, Exception)]
```

### 3. 데이터베이스 최적화

```python
# 배치 쿼리
class OptimizedRepository:
    def get_matches_batch(self, match_ids: List[str]) -> List[Match]:
        # IN 절을 사용한 배치 조회
        response = self.client.table('target_games').select('*').in_('match_id', match_ids).execute()
        return [self._convert_to_match(data) for data in response.data]
    
    def get_team_stats_batch(self, match_ids: List[str]) -> Dict[str, List[TeamStats]]:
        # 여러 경기의 통계를 한 번에 조회
        response = self.client.table('team_stats').select('*').in_('match_id', match_ids).execute()
        
        # match_id별로 그룹화
        stats_by_match = {}
        for stats_data in response.data:
            match_id = stats_data['match_id']
            if match_id not in stats_by_match:
                stats_by_match[match_id] = []
            stats_by_match[match_id].append(self._convert_to_team_stats(stats_data))
        
        return stats_by_match
```

## 보안 고려사항

### 1. API 키 관리

```python
# 환경 변수를 통한 보안 키 관리
class SecuritySettings(BaseSettings):
    supabase_key: str = Field(..., env="DB_SUPABASE_KEY")
    openai_api_key: str = Field(..., env="LLM_OPENAI_API_KEY")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

### 2. 데이터 검증

```python
from pydantic import BaseModel, validator

class MatchInput(BaseModel):
    match_id: str
    
    @validator('match_id')
    def validate_match_id(cls, v):
        if not re.match(r'^[A-Z]{3}\d{9}$', v):
            raise ValueError('Invalid match ID format')
        return v
```

### 3. 에러 처리

```python
class BaseballAnalyticsError(Exception):
    """기본 예외 클래스"""
    pass

class DatabaseError(BaseballAnalyticsError):
    """데이터베이스 관련 오류"""
    pass

class AnalysisError(BaseballAnalyticsError):
    """분석 관련 오류"""
    pass

# 사용 예시
try:
    result = service.analyze_match(match_id)
except DatabaseError as e:
    logger.error(f"데이터베이스 오류: {e}")
    # 적절한 에러 응답
except AnalysisError as e:
    logger.error(f"분석 오류: {e}")
    # 적절한 에러 응답
```

## 모니터링 및 로깅

### 1. 구조화된 로깅

```python
import structlog

logger = structlog.get_logger()

class AnalysisService:
    def analyze_match(self, match_id: str) -> AnalysisResult:
        logger.info("분석 시작", match_id=match_id)
        
        try:
            # 분석 로직
            result = self._perform_analysis(match_id)
            
            logger.info(
                "분석 완료",
                match_id=match_id,
                confidence_score=result.confidence_score,
                analysis_time=result.analysis_time
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "분석 실패",
                match_id=match_id,
                error=str(e),
                exc_info=True
            )
            raise
```

### 2. 메트릭 수집

```python
from prometheus_client import Counter, Histogram, Gauge

# 메트릭 정의
analysis_requests = Counter('analysis_requests_total', 'Total analysis requests', ['sport_type'])
analysis_duration = Histogram('analysis_duration_seconds', 'Analysis duration')
active_analyses = Gauge('active_analyses', 'Number of active analyses')

class MonitoredAnalysisService:
    def analyze_match(self, match_id: str) -> AnalysisResult:
        analysis_requests.labels(sport_type='baseball').inc()
        active_analyses.inc()
        
        with analysis_duration.time():
            try:
                return self._perform_analysis(match_id)
            finally:
                active_analyses.dec()
```

## 테스트 전략

### 1. 단위 테스트

```python
# tests/test_domain_models.py
def test_match_creation():
    home_team = Team(id="LG", name="LG 트윈스", short_name="LG")
    away_team = Team(id="KT", name="KT 위즈", short_name="KT")
    
    match = Match(
        match_id="BSW025073025",
        sport_type=SportType.BASEBALL,
        league="KBO",
        match_date=datetime.now(),
        home_team=home_team,
        away_team=away_team,
        status=MatchStatus.SCHEDULED
    )
    
    assert match.match_id == "BSW025073025"
    assert match.home_team.name == "LG 트윈스"
```

### 2. 통합 테스트

```python
# tests/test_integration.py
@pytest.fixture
def test_repository():
    return SupabaseRepository(test_connection)

def test_match_analysis_integration(test_repository):
    service = AnalysisService(test_repository, mock_llm_manager)
    result = service.analyze_match("TEST_MATCH_001")
    
    assert result is not None
    assert result.confidence_score > 0
```

### 3. E2E 테스트

```python
# tests/test_e2e.py
def test_complete_analysis_workflow():
    # 실제 워크플로우 테스트
    matches = repository.get_sns_target_matches()
    assert len(matches) > 0
    
    match_id = matches[0]['match_id']
    analysis = agent.analyze_match_comprehensive(match_id)
    
    assert 'summary' in analysis
    assert len(analysis['summary']) > 0
```

## 배포 전략

### 1. Docker 컨테이너화

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["python", "-m", "baseball_analytics.main"]
```

### 2. Docker Compose

```yaml
version: '3.8'
services:
  baseball-analytics:
    build: .
    environment:
      - DB_SUPABASE_URL=${DB_SUPABASE_URL}
      - DB_SUPABASE_KEY=${DB_SUPABASE_KEY}
      - LLM_OPENAI_API_KEY=${LLM_OPENAI_API_KEY}
    volumes:
      - ./logs:/app/logs
```

### 3. CI/CD 파이프라인

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: pytest
      - name: Run linting
        run: flake8 baseball_analytics/
```

## 향후 개선 계획

### 1. 마이크로서비스 아키텍처 전환

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Analysis      │    │   Content       │    │   Notification  │
│   Service       │    │   Service       │    │   Service       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    └─────────────────┘
```

### 2. 이벤트 기반 아키텍처

```python
# 이벤트 발행
class MatchAnalyzedEvent:
    match_id: str
    analysis_result: AnalysisResult
    timestamp: datetime

# 이벤트 처리
class ContentGenerationHandler:
    def handle(self, event: MatchAnalyzedEvent):
        # 분석 완료 시 자동으로 콘텐츠 생성
        pass
```

### 3. 실시간 분석

```python
# WebSocket을 통한 실시간 업데이트
class RealTimeAnalysisService:
    async def stream_analysis(self, match_id: str):
        async for update in self._analysis_stream(match_id):
            yield update
```

이 아키텍처는 확장 가능하고 유지보수가 용이하며, 새로운 요구사항에 유연하게 대응할 수 있도록 설계되었습니다. 