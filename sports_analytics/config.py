"""
Unified Configuration Module for Multi-Sport Analytics Platform.

This module provides centralized configuration management with support for
environment variables, validation, and type safety.
"""

import os
from pathlib import Path
from typing import Optional
from dataclasses import dataclass
from enum import Enum


class Environment(str, Enum):
    """Application environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    GOOGLE = "google"


@dataclass
class DatabaseSettings:
    """Database configuration settings."""
    supabase_url: str
    supabase_key: str
    connection_timeout: int = 30
    max_retries: int = 3
    
    def __post_init__(self):
        if not self.supabase_url:
            raise ValueError("Supabase URL is required")
        if not self.supabase_key:
            raise ValueError("Supabase key is required")


@dataclass 
class LLMSettings:
    """LLM configuration settings."""
    openai_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    default_provider: LLMProvider = LLMProvider.OPENAI
    default_model: str = "gpt-4o-mini"
    max_tokens: int = 4000
    temperature: float = 0.1
    
    def __post_init__(self):
        # Validate at least one API key is provided
        if not any([self.openai_api_key, self.google_api_key]):
            raise ValueError("At least one LLM API key must be provided")
        
        # Auto-detect default provider if not set
        if self.default_provider == LLMProvider.OPENAI and not self.openai_api_key:
            if self.google_api_key:
                self.default_provider = LLMProvider.GOOGLE


@dataclass
class ApplicationSettings:
    """Application-level configuration."""
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    log_level: str = "INFO"
    
    def __post_init__(self):
        if self.environment == Environment.DEVELOPMENT:
            self.debug = True
            self.log_level = "DEBUG"


@dataclass
class Settings:
    """Main settings container."""
    database: DatabaseSettings
    llm: LLMSettings
    app: ApplicationSettings
    
    @classmethod
    def from_env(cls) -> 'Settings':
        """Create settings from environment variables."""
        # Import config values
        from config import (
            SUPABASE_URL, SUPABASE_KEY, OPENAI_API_KEY, 
            GEMINI_API_KEY, APP_ENVIRONMENT, LOG_LEVEL
        )
        
        # Database settings
        database = DatabaseSettings(
            supabase_url=SUPABASE_URL or "",
            supabase_key=SUPABASE_KEY or "",
            connection_timeout=int(os.getenv("DB_CONNECTION_TIMEOUT", "30")),
            max_retries=int(os.getenv("DB_MAX_RETRIES", "3"))
        )
        
        # LLM settings
        llm = LLMSettings(
            openai_api_key=OPENAI_API_KEY,
            google_api_key=GEMINI_API_KEY,
            default_provider=LLMProvider.OPENAI if OPENAI_API_KEY else LLMProvider.GOOGLE,
            default_model=os.getenv("LLM_DEFAULT_MODEL", "gpt-4o-mini"),
            max_tokens=int(os.getenv("LLM_MAX_TOKENS", "4000")),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0.1"))
        )
        
        # Application settings
        app = ApplicationSettings(
            environment=Environment(APP_ENVIRONMENT),
            debug=os.getenv("APP_DEBUG", "false").lower() == "true",
            log_level=LOG_LEVEL
        )
        
        return cls(database=database, llm=llm, app=app)


def get_settings() -> Settings:
    """Get application settings."""
    return Settings.from_env()


def create_directories():
    """Create necessary directories."""
    directories = [
        "logs",
        "cache", 
        "data",
        "exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)


def validate_settings(settings: Settings) -> bool:
    """Validate settings configuration."""
    try:
        # Database validation
        if not settings.database.supabase_url:
            raise ValueError("Supabase URL is required")
        if not settings.database.supabase_key:
            raise ValueError("Supabase key is required")
        
        # LLM validation
        if not any([
            settings.llm.openai_api_key,
            settings.llm.google_api_key
        ]):
            raise ValueError("At least one LLM API key is required")
        
        return True
        
    except ValueError as e:
        print(f"Configuration validation error: {e}")
        return False
