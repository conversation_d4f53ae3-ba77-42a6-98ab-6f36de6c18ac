#!/usr/bin/env python3
"""
Unified Multi-Sport Analytics Platform - Main Application.

This is the single entry point for all sports analysis functionality.
Supports baseball (rich analysis), soccer, basketball, volleyball (WDL analysis).

Usage:
    python -m sports_analytics.main list                    # List target matches
    python -m sports_analytics.main analyze <match_id>      # Analyze specific match
    python -m sports_analytics.main batch                   # Analyze all matches
    python -m sports_analytics.main test                    # Test system
"""

import sys
import argparse
import logging
from typing import Dict, List, Any

from sports_analytics.config import get_settings
from sports_analytics.infrastructure.llm_providers import setup_llm_manager
from sports_analytics.infrastructure.database import get_sns_target_matches, get_sport_type
from sports_analytics.agents.sports_agents import UnifiedAnalysisService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_system():
    """Initialize system components."""
    try:
        # Load settings
        settings = get_settings()
        
        # Setup LLM
        llm_manager = setup_llm_manager(settings.llm)
        llm = llm_manager.get_model()
        
        # Create unified analysis service
        analysis_service = UnifiedAnalysisService(llm)
        
        return analysis_service
        
    except Exception as e:
        logger.error(f"Failed to setup system: {e}")
        raise


def list_target_matches():
    """List all target matches grouped by sport."""
    try:
        target_matches = get_sns_target_matches()
        
        if not target_matches:
            print("📋 분석 대상 경기가 없습니다.")
            return
        
        # Group by sport
        sport_groups = {}
        for match in target_matches:
            sport = get_sport_type(match)
            sport_name = {
                'baseball': '⚾ 야구',
                'soccer': '⚽ 축구',
                'basketball': '🏀 농구',
                'volleyball': '🏐 배구'
            }.get(sport, f'🏆 {sport}')
            
            if sport_name not in sport_groups:
                sport_groups[sport_name] = []
            sport_groups[sport_name].append(match)
        
        # Display results
        total_matches = len(target_matches)
        print(f"📋 분석 대상 경기 ({total_matches}개)")
        print("=" * 60)
        
        for sport_name, matches in sport_groups.items():
            print(f"\n{sport_name} ({len(matches)}개)")
            print("-" * 30)
            
            for i, match in enumerate(matches, 1):
                match_id = match.get('match_id', 'Unknown')
                home_team = match.get('home_team', 'Unknown')
                away_team = match.get('away_team', 'Unknown')
                match_date = match.get('match_date', 'Unknown')
                match_time = match.get('match_time', 'Unknown')
                
                print(f"   {i}. {match_id}")
                print(f"      🏟️ {home_team} vs {away_team}")
                print(f"      📅 {match_date} {match_time}")
                print()
        
    except Exception as e:
        logger.error(f"Error listing matches: {e}")
        print(f"❌ 경기 목록 조회 실패: {e}")


def analyze_match(match_id: str):
    """Analyze a specific match."""
    try:
        print(f"🔄 경기 분석 시작: {match_id}")
        print("=" * 80)
        
        # Setup system
        analysis_service = setup_system()
        
        # Perform analysis
        result = analysis_service.analyze_match(match_id)
        
        if "error" in result:
            print(f"❌ 분석 실패: {result['error']}")
            return
        
        # Display results
        sport_icons = {
            'baseball': '⚾',
            'soccer': '⚽',
            'basketball': '🏀',
            'volleyball': '🏐'
        }
        
        sport = result.get('sport', 'unknown')
        sport_icon = sport_icons.get(sport, '🏆')
        analysis_type = result.get('analysis_type', 'unknown')
        
        print(f"🏟️ {sport_icon} {sport.title()} 전문 분석 ({analysis_type})")
        print("=" * 80)
        print()
        
        # Basic info
        print(f"🏟️ {result.get('home_team', 'Unknown')} vs {result.get('away_team', 'Unknown')}")
        print(f"📅 {result.get('match_date', 'Unknown')} {result.get('match_time', 'Unknown')}")
        print()
        
        # Sport-specific analysis
        if sport == "baseball" and "pitcher_analysis" in result:
            print("⚾ 투수 분석")
            print("-" * 40)
            print(result['pitcher_analysis'])
            print()
            
            print("🏏 팀 분석")
            print("-" * 40)
            print(result['team_analysis'])
            print()
        elif "sport_analysis" in result:
            print(f"{sport_icon} {sport.title()} 종합 분석")
            print("-" * 40)
            print(result['sport_analysis'])
            print()
        
        # Common analysis
        if "h2h_analysis" in result:
            print("🔄 맞대결 분석")
            print("-" * 40)
            print(result['h2h_analysis'])
            print()
        
        if "home_away_analysis" in result:
            print("🏠 홈/원정 폼 분석")
            print("-" * 40)
            print(result['home_away_analysis'])
            print()
        
        # Analysis metadata
        print("📊 분석 정보")
        print("-" * 40)
        print(f"분석 품질: {result.get('analysis_quality', 'Unknown')}")
        print(f"데이터 소스: {result.get('data_source', 'Unknown')}")
        if result.get('note'):
            print(f"참고사항: {result['note']}")
        
    except Exception as e:
        logger.error(f"Error analyzing match: {e}")
        print(f"❌ 경기 분석 실패: {e}")


def batch_analyze():
    """Analyze all target matches."""
    try:
        print("📊 일괄 분석 시작")
        print("=" * 60)
        
        # Setup system
        analysis_service = setup_system()
        
        # Get target matches
        target_matches = analysis_service.get_target_matches()
        
        if not target_matches:
            print("📋 분석 대상 경기가 없습니다.")
            return
        
        print(f"🔄 {len(target_matches)}개 경기 분석 중...")
        print()
        
        # Analyze each match
        results = []
        for i, match_data in enumerate(target_matches, 1):
            match_id = match_data.get("match_id")
            if not match_id:
                continue
            
            print(f"[{i}/{len(target_matches)}] {match_id} 분석 중...")
            
            try:
                result = analysis_service.analyze_match(match_id)
                if "error" not in result:
                    results.append(result)
                    sport = result.get('sport', 'unknown')
                    home_team = result.get('home_team', 'Unknown')
                    away_team = result.get('away_team', 'Unknown')
                    print(f"   ✅ {sport}: {home_team} vs {away_team}")
                else:
                    print(f"   ❌ 실패: {result['error']}")
            except Exception as e:
                print(f"   ❌ 오류: {e}")
                continue
        
        # Summary
        print()
        print("📊 일괄 분석 완료")
        print("=" * 60)
        print(f"✅ 성공: {len(results)}개")
        print(f"❌ 실패: {len(target_matches) - len(results)}개")
        
        # Group by sport
        sport_counts = {}
        for result in results:
            sport = result.get('sport', 'unknown')
            sport_counts[sport] = sport_counts.get(sport, 0) + 1
        
        print("\n📈 스포츠별 분석 결과:")
        for sport, count in sport_counts.items():
            sport_name = {
                'baseball': '⚾ 야구',
                'soccer': '⚽ 축구',
                'basketball': '🏀 농구',
                'volleyball': '🏐 배구'
            }.get(sport, f'🏆 {sport}')
            print(f"   {sport_name}: {count}개")
        
    except Exception as e:
        logger.error(f"Error in batch analysis: {e}")
        print(f"❌ 일괄 분석 실패: {e}")


def test_system():
    """Test system components."""
    try:
        print("🧪 시스템 테스트 시작")
        print("=" * 60)
        
        # Test 1: Configuration
        print("1. 설정 테스트...")
        settings = get_settings()
        print(f"   ✅ 데이터베이스: {settings.database.supabase_url[:20]}...")
        print(f"   ✅ LLM 제공자: {settings.llm.default_provider}")
        
        # Test 2: Database connection
        print("\n2. 데이터베이스 연결 테스트...")
        target_matches = get_sns_target_matches()
        print(f"   ✅ 대상 경기: {len(target_matches)}개")
        
        # Test 3: LLM connection
        print("\n3. LLM 연결 테스트...")
        llm_manager = setup_llm_manager(settings.llm)
        llm = llm_manager.get_model()
        test_response = llm.invoke("안녕하세요. 테스트입니다.")
        print(f"   ✅ LLM 응답: {str(test_response)[:50]}...")
        
        # Test 4: Analysis service
        print("\n4. 분석 서비스 테스트...")
        analysis_service = UnifiedAnalysisService(llm)
        print("   ✅ 통합 분석 서비스 초기화 완료")
        
        print("\n🎉 모든 테스트 통과!")
        
    except Exception as e:
        logger.error(f"System test failed: {e}")
        print(f"❌ 시스템 테스트 실패: {e}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Unified Multi-Sport Analytics Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m sports_analytics.main list                    # List target matches
  python -m sports_analytics.main analyze BSW025073025    # Analyze baseball match
  python -m sports_analytics.main analyze SOC025073025    # Analyze soccer match
  python -m sports_analytics.main batch                   # Analyze all matches
  python -m sports_analytics.main test                    # Test system
        """
    )
    
    parser.add_argument(
        'command',
        choices=['list', 'analyze', 'batch', 'test'],
        help='Command to execute'
    )
    
    parser.add_argument(
        'match_id',
        nargs='?',
        help='Match ID for analyze command'
    )
    
    args = parser.parse_args()
    
    try:
        if args.command == 'list':
            list_target_matches()
        elif args.command == 'analyze':
            if not args.match_id:
                print("❌ 경기 ID가 필요합니다. 예: python -m sports_analytics.main analyze BSW025073025")
                sys.exit(1)
            analyze_match(args.match_id)
        elif args.command == 'batch':
            batch_analyze()
        elif args.command == 'test':
            test_system()
            
    except KeyboardInterrupt:
        print("\n⏹️ 사용자에 의해 중단되었습니다.")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"❌ 애플리케이션 오류: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
