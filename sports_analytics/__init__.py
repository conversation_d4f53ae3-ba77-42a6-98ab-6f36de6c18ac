"""
Multi-Sport Analytics Platform.

A comprehensive sports analytics platform built with modern Python architecture,
featuring AI-powered analysis, real-time data processing, and automated content generation.

Supports:
- Baseball: Rich analysis using team_stats + WDL data
- Soccer/Basketball/Volleyball: WDL data analysis
- Automatic sport detection and appropriate agent selection
"""

__version__ = "1.0.0"
__author__ = "Sports Analytics Team"

from .config import get_settings, Settings

__all__ = [
    "get_settings",
    "Settings"
]
