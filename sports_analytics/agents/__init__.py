"""
Sports Analysis Agents Package.

This package contains specialized AI agents for different sports:
- BaseballAgent: Rich analysis using team_stats + WDL data
- SoccerAgent: Analysis using WDL data
- BasketballAgent: Analysis using WDL data  
- VolleyballAgent: Analysis using WDL data
- CommonAgents: H2H and Home/Away analysis for all sports

Each agent uses the tool pattern for modularity and follows SOLID principles.
"""

from .sports_agents import (
    BaseballPitcherAgent,
    BaseballTeamAgent,
    SoccerAgent,
    BasketballAgent,
    VolleyballAgent,
    H2HAgent,
    HomeAwayAgent,
    UnifiedAnalysisService
)

__all__ = [
    "BaseballPitcherAgent",
    "BaseballTeamAgent", 
    "SoccerAgent",
    "BasketballAgent",
    "VolleyballAgent",
    "H2HAgent",
    "HomeAwayAgent",
    "UnifiedAnalysisService"
]
