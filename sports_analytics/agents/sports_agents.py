"""
Unified Sports Analysis Agents using actual Supabase database structure.

This module implements specialized agents for all sports based on actual data availability:

Baseball Agents (rich data from team_stats):
- BaseballPitcherAgent: Pitcher analysis using pitcher_profile/pitcher_stats
- BaseballTeamAgent: Team analysis using season_stats/recent_matches

Other Sports Agents (WDL data only):
- SoccerAgent: Soccer analysis using WDL data from sportic_contents
- BasketballAgent: Basketball analysis using WDL data
- VolleyballAgent: Volleyball analysis using WDL data

Common Agents:
- H2HAgent: Head-to-head analysis for all sports
- HomeAwayAgent: Home/away form analysis for all sports

Each agent uses actual database fields and follows the tool pattern for modularity.
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from sports_analytics.infrastructure.database import (
    get_match_data, get_sportic_content, get_team_stats,
    get_wdl_data, get_content_for_match, get_betting_picks, get_sns_target_matches,
    get_sport_type
)

logger = logging.getLogger(__name__)


# ========== Analysis Result Models ==========

class SportAnalysisResult(BaseModel):
    """Base analysis result for all sports."""
    
    match_id: str = Field(..., description="경기 ID")
    sport: str = Field(..., description="스포츠 종목")
    home_team: str = Field(..., description="홈팀")
    away_team: str = Field(..., description="원정팀")
    match_date: str = Field(..., description="경기 날짜")
    
    summary: str = Field(..., description="경기 요약")
    key_factors: List[str] = Field(..., description="주요 승부 요인")
    prediction: str = Field(..., description="경기 예측")
    recommendation: str = Field(..., description="베팅 추천")
    confidence: float = Field(..., ge=0.0, le=1.0, description="신뢰도")
    hashtags: List[str] = Field(default_factory=list, description="해시태그")


class BaseballAnalysisResult(SportAnalysisResult):
    """Baseball-specific analysis result with rich team_stats data."""
    
    pitcher_analysis: str = Field(..., description="투수 분석")
    team_analysis: str = Field(..., description="팀 분석")
    h2h_analysis: str = Field(..., description="맞대결 분석")
    betting_analysis: str = Field(..., description="배당 분석")


class GeneralSportAnalysisResult(SportAnalysisResult):
    """General sport analysis result using WDL data only."""
    
    h2h_analysis: str = Field(..., description="맞대결 분석")
    home_form: str = Field(..., description="홈팀 폼")
    away_form: str = Field(..., description="원정팀 폼")
    betting_analysis: str = Field(..., description="배당 분석")


# ========== Data Collection Tools ==========

@tool
def get_match_basic_data(match_id: str) -> Dict[str, Any]:
    """Get basic match data from target_games table."""
    try:
        match_data = get_match_data(match_id)
        if not match_data:
            return {"error": f"Match not found: {match_id}"}
        return match_data
    except Exception as e:
        logger.error(f"Error getting match data: {e}")
        return {"error": str(e)}


@tool
def get_baseball_team_stats_data(match_id: str) -> List[Dict[str, Any]]:
    """Get baseball team statistics from team_stats table (JSONB fields)."""
    try:
        team_stats = get_team_stats(match_id)
        return team_stats if team_stats else []
    except Exception as e:
        logger.error(f"Error getting team stats: {e}")
        return []


@tool
def get_h2h_wdl_data(match_id: str) -> Dict[str, Any]:
    """Get head-to-head WDL data from sportic_contents."""
    try:
        h2h_summary = get_wdl_data(match_id, 'h2h', 'summary')
        h2h_matches = get_wdl_data(match_id, 'h2h', 'matches')
        return {
            "summary": h2h_summary,
            "matches": h2h_matches
        }
    except Exception as e:
        logger.error(f"Error getting H2H data: {e}")
        return {}


@tool
def get_home_away_wdl_data(match_id: str) -> Dict[str, Any]:
    """Get home/away WDL data from sportic_contents."""
    try:
        home_summary = get_wdl_data(match_id, 'home', 'summary')
        away_summary = get_wdl_data(match_id, 'away', 'summary')
        home_matches = get_wdl_data(match_id, 'home', 'matches')
        away_matches = get_wdl_data(match_id, 'away', 'matches')
        
        return {
            "home_summary": home_summary,
            "away_summary": away_summary,
            "home_matches": home_matches,
            "away_matches": away_matches
        }
    except Exception as e:
        logger.error(f"Error getting home/away data: {e}")
        return {}


@tool
def get_betting_data(match_id: str) -> Dict[str, Any]:
    """Get betting picks and odds data."""
    try:
        betting_picks = get_betting_picks(match_id)
        return betting_picks if betting_picks else {}
    except Exception as e:
        logger.error(f"Error getting betting data: {e}")
        return {}


@tool
def get_existing_content(match_id: str) -> str:
    """Get existing analysis content."""
    try:
        content = get_content_for_match(match_id)
        return content if content else "기존 분석 없음"
    except Exception as e:
        logger.error(f"Error getting existing content: {e}")
        return "기존 분석 없음"


# ========== Baseball Agents (Rich Data) ==========

class BaseballPitcherAgent:
    """
    Baseball pitcher analysis agent using pitcher_profile and pitcher_stats from team_stats.
    
    This agent specializes in analyzing starting pitchers using rich data from the team_stats table.
    """
    
    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_baseball_team_stats_data, get_h2h_wdl_data]
        self.prompt = self._create_pitcher_prompt()
    
    def _create_pitcher_prompt(self) -> ChatPromptTemplate:
        """Create baseball pitcher-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 투수 전문 분석가입니다.
            team_stats 테이블의 pitcher_profile과 pitcher_stats JSONB 데이터를 활용하여 
            선발 투수들을 상세히 분석합니다.
            
            분석 요소:
            1. 투수 기본 정보 (이름, 나이, 포지션)
            2. 시즌 성적 (ERA, 승패, 이닝, 피안타, 볼넷, 삼진)
            3. 최근 폼 (최근 5경기 성적)
            4. 상대 타선과의 매치업
            5. 구장 특성 고려
            
            객관적이고 전문적인 투수 분석을 제공하세요.
            """),
            ("human", """
            다음 야구 경기의 투수진을 분석해주세요:
            
            경기 정보:
            {match_info}
            
            팀 통계 데이터 (team_stats):
            {team_stats_data}
            
            H2H 데이터:
            {h2h_data}
            
            투수 매치업을 중심으로 상세한 분석을 제공해주세요.
            """)
        ])
    
    def analyze(self, match_id: str) -> str:
        """Analyze pitchers for the match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            team_stats_data = get_baseball_team_stats_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})
            
            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "team_stats_data": self._format_team_stats(team_stats_data),
                "h2h_data": self._format_h2h_data(h2h_data)
            }
            
            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"Error in pitcher analysis: {e}")
            return f"투수 분석 중 오류가 발생했습니다: {e}"
    
    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"
        
        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """
    
    def _format_team_stats(self, team_stats_data: List[Dict[str, Any]]) -> str:
        """Format team_stats data for prompt."""
        if not team_stats_data:
            return "팀 통계 데이터가 없습니다."
        
        formatted = "팀별 투수 정보:\n"
        for stats in team_stats_data:
            team_name = stats.get('team_name', 'Unknown')
            formatted += f"\n{team_name}:\n"
            
            # Pitcher profile
            pitcher_profile = stats.get('pitcher_profile', {})
            if pitcher_profile:
                formatted += f"  투수 프로필: {pitcher_profile}\n"
            
            # Pitcher stats
            pitcher_stats = stats.get('pitcher_stats', {})
            if pitcher_stats:
                formatted += f"  투수 통계: {pitcher_stats}\n"
        
        return formatted
    
    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        """Format H2H data for prompt."""
        if not h2h_data:
            return "H2H 데이터가 없습니다."
        
        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기: {h2h_data['matches']}\n"
        
        return formatted


class BaseballTeamAgent:
    """
    Baseball team analysis agent using season_stats and recent_matches from team_stats.
    
    This agent specializes in analyzing team performance using rich data from the team_stats table.
    """
    
    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_baseball_team_stats_data, get_home_away_wdl_data]
        self.prompt = self._create_team_prompt()
    
    def _create_team_prompt(self) -> ChatPromptTemplate:
        """Create baseball team-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 팀 분석 전문가입니다.
            team_stats 테이블의 season_stats와 recent_matches JSONB 데이터를 활용하여
            팀의 전반적인 성적과 폼을 분석합니다.
            
            분석 요소:
            1. 시즌 성적 (승률, 득점, 실점, 타율, 팀 ERA)
            2. 최근 폼 (최근 10경기 성적)
            3. 홈/원정 성적 차이
            4. 주요 타자들의 컨디션
            5. 불펜 상황
            
            데이터 기반의 객관적인 팀 분석을 제공하세요.
            """),
            ("human", """
            다음 야구 경기의 팀들을 분석해주세요:
            
            경기 정보:
            {match_info}
            
            팀 통계 데이터:
            {team_stats_data}
            
            홈/원정 폼 데이터:
            {home_away_data}
            
            양 팀의 전력과 최근 폼을 비교 분석해주세요.
            """)
        ])
    
    def analyze(self, match_id: str) -> str:
        """Analyze teams for the match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            team_stats_data = get_baseball_team_stats_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})
            
            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "team_stats_data": self._format_team_stats(team_stats_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }
            
            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"Error in team analysis: {e}")
            return f"팀 분석 중 오류가 발생했습니다: {e}"
    
    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"
        
        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """
    
    def _format_team_stats(self, team_stats_data: List[Dict[str, Any]]) -> str:
        """Format team_stats data for prompt."""
        if not team_stats_data:
            return "팀 통계 데이터가 없습니다."
        
        formatted = "팀별 상세 통계:\n"
        for stats in team_stats_data:
            team_name = stats.get('team_name', 'Unknown')
            formatted += f"\n{team_name}:\n"
            
            # Season stats
            season_stats = stats.get('season_stats', {})
            if season_stats:
                formatted += f"  시즌 통계: {season_stats}\n"
            
            # Recent matches
            recent_matches = stats.get('recent_matches', {})
            if recent_matches:
                formatted += f"  최근 경기: {recent_matches}\n"
            
            # Season summary
            season_summary = stats.get('season_summary', {})
            if season_summary:
                formatted += f"  시즌 요약: {season_summary}\n"
        
        return formatted
    
    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        """Format home/away data for prompt."""
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."
        
        formatted = "홈/원정 폼:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 폼: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 폼: {home_away_data['away_summary']}\n"

        return formatted


# ========== Other Sports Agents (WDL Data Only) ==========

class SoccerAgent:
    """
    Soccer analysis agent using WDL data from sportic_contents.

    This agent analyzes soccer matches using available WDL (Win-Draw-Loss) data.
    """

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_h2h_wdl_data, get_home_away_wdl_data]
        self.prompt = self._create_soccer_prompt()

    def _create_soccer_prompt(self) -> ChatPromptTemplate:
        """Create soccer-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 축구 분석 전문가입니다.
            WDL(승무패) 데이터를 활용하여 축구 경기를 분석합니다.

            분석 요소:
            1. 맞대결 기록 (승무패 통계)
            2. 홈/원정 폼 분석
            3. 최근 경기 흐름
            4. 전술적 특징
            5. 주요 변수 (부상, 징계 등)

            축구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 축구 경기를 분석해주세요:

            경기 정보:
            {match_info}

            맞대결 기록:
            {h2h_data}

            홈/원정 폼:
            {home_away_data}

            축구 특성을 고려한 상세한 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze soccer match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})

            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_data": self._format_h2h_data(h2h_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }

            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in soccer analysis: {e}")
            return f"축구 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """

    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        """Format H2H data for prompt."""
        if not h2h_data:
            return "맞대결 데이터가 없습니다."

        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기: {h2h_data['matches']}\n"

        return formatted

    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        """Format home/away data for prompt."""
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."

        formatted = "홈/원정 폼:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 폼: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 폼: {home_away_data['away_summary']}\n"

        return formatted


class BasketballAgent:
    """Basketball analysis agent using WDL data from sportic_contents."""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_h2h_wdl_data, get_home_away_wdl_data]
        self.prompt = self._create_basketball_prompt()

    def _create_basketball_prompt(self) -> ChatPromptTemplate:
        """Create basketball-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 농구 분석 전문가입니다.
            WDL(승패) 데이터를 활용하여 농구 경기를 분석합니다.

            분석 요소:
            1. 맞대결 기록 (승패 통계)
            2. 홈/원정 폼 분석
            3. 최근 경기 흐름
            4. 공격/수비 스타일
            5. 주요 선수 컨디션

            농구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 농구 경기를 분석해주세요:

            경기 정보:
            {match_info}

            맞대결 기록:
            {h2h_data}

            홈/원정 폼:
            {home_away_data}

            농구 특성을 고려한 상세한 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze basketball match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})

            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_data": self._format_h2h_data(h2h_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }

            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in basketball analysis: {e}")
            return f"농구 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """

    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        """Format H2H data for prompt."""
        if not h2h_data:
            return "맞대결 데이터가 없습니다."

        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기: {h2h_data['matches']}\n"

        return formatted

    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        """Format home/away data for prompt."""
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."

        formatted = "홈/원정 폼:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 폼: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 폼: {home_away_data['away_summary']}\n"

        return formatted


class VolleyballAgent:
    """Volleyball analysis agent using WDL data from sportic_contents."""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_h2h_wdl_data, get_home_away_wdl_data]
        self.prompt = self._create_volleyball_prompt()

    def _create_volleyball_prompt(self) -> ChatPromptTemplate:
        """Create volleyball-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 배구 분석 전문가입니다.
            WDL(승패) 데이터를 활용하여 배구 경기를 분석합니다.

            분석 요소:
            1. 맞대결 기록 (승패 통계)
            2. 홈/원정 폼 분석
            3. 최근 경기 흐름
            4. 공격/블로킹 스타일
            5. 세트 스코어 패턴

            배구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 배구 경기를 분석해주세요:

            경기 정보:
            {match_info}

            맞대결 기록:
            {h2h_data}

            홈/원정 폼:
            {home_away_data}

            배구 특성을 고려한 상세한 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze volleyball match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})

            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_data": self._format_h2h_data(h2h_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }

            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in volleyball analysis: {e}")
            return f"배구 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """

    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        """Format H2H data for prompt."""
        if not h2h_data:
            return "맞대결 데이터가 없습니다."

        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기: {h2h_data['matches']}\n"

        return formatted

    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        """Format home/away data for prompt."""
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."

        formatted = "홈/원정 폼:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 폼: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 폼: {home_away_data['away_summary']}\n"

        return formatted


# ========== Common Agents ==========

class H2HAgent:
    """Head-to-head analysis agent for all sports."""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_h2h_wdl_data]
        self.prompt = self._create_h2h_prompt()

    def _create_h2h_prompt(self) -> ChatPromptTemplate:
        """Create H2H analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 스포츠 맞대결 분석 전문가입니다.
            두 팀 간의 과거 맞대결 기록을 분석하여 인사이트를 제공합니다.

            분석 요소:
            1. 전체 맞대결 승부 기록
            2. 최근 맞대결 트렌드
            3. 특별한 패턴이나 경향
            4. 심리적 우위

            객관적이고 통계 기반의 분석을 제공하세요.
            """),
            ("human", """
            다음 경기의 맞대결 기록을 분석해주세요:

            경기 정보:
            {match_info}

            맞대결 데이터:
            {h2h_data}

            맞대결 관점에서 상세한 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze head-to-head record."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})

            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_data": self._format_h2h_data(h2h_data)
            }

            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in H2H analysis: {e}")
            return f"맞대결 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """

    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        """Format H2H data for prompt."""
        if not h2h_data:
            return "맞대결 데이터가 없습니다."

        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기: {h2h_data['matches']}\n"

        return formatted


class HomeAwayAgent:
    """Home/away form analysis agent for all sports."""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_home_away_wdl_data]
        self.prompt = self._create_home_away_prompt()

    def _create_home_away_prompt(self) -> ChatPromptTemplate:
        """Create home/away analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 스포츠 홈/원정 폼 분석 전문가입니다.
            홈팀의 홈 경기 성적과 원정팀의 원정 경기 성적을 분석합니다.

            분석 요소:
            1. 홈팀의 홈 경기 성적
            2. 원정팀의 원정 경기 성적
            3. 홈 어드밴티지 정도
            4. 최근 폼 변화

            홈/원정 특성을 고려한 분석을 제공하세요.
            """),
            ("human", """
            다음 경기의 홈/원정 폼을 분석해주세요:

            경기 정보:
            {match_info}

            홈/원정 데이터:
            {home_away_data}

            홈/원정 관점에서 상세한 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze home/away form."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})

            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }

            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in home/away analysis: {e}")
            return f"홈/원정 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format match information for prompt."""
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """

    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        """Format home/away data for prompt."""
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."

        formatted = "홈/원정 폼:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 폼: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 폼: {home_away_data['away_summary']}\n"

        return formatted


# ========== Unified Analysis Service ==========

class UnifiedAnalysisService:
    """
    Unified analysis service that automatically detects sport type and
    selects appropriate agents based on data availability.

    This service follows the Single Responsibility Principle by delegating
    sport-specific analysis to specialized agents.
    """

    def __init__(self, llm: BaseChatModel):
        self.llm = llm

        # Initialize sport-specific agents
        self.baseball_pitcher_agent = BaseballPitcherAgent(llm)
        self.baseball_team_agent = BaseballTeamAgent(llm)
        self.soccer_agent = SoccerAgent(llm)
        self.basketball_agent = BasketballAgent(llm)
        self.volleyball_agent = VolleyballAgent(llm)

        # Initialize common agents
        self.h2h_agent = H2HAgent(llm)
        self.home_away_agent = HomeAwayAgent(llm)

    def analyze_match(self, match_id: str) -> Dict[str, Any]:
        """
        Analyze a match using appropriate agents based on sport type and data availability.

        Returns a comprehensive analysis result with sport-specific insights.
        """
        try:
            # Get basic match data
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            if "error" in match_data:
                return {"error": f"Failed to get match data: {match_data['error']}"}

            # Determine sport type
            sport = get_sport_type(match_data)

            # Initialize result
            result = {
                "match_id": match_id,
                "sport": sport,
                "home_team": match_data.get("home_team", "Unknown"),
                "away_team": match_data.get("away_team", "Unknown"),
                "match_date": match_data.get("match_date", "Unknown"),
                "match_time": match_data.get("match_time", "Unknown"),
                "analysis_type": self._get_analysis_type(sport, match_id)
            }

            # Perform sport-specific analysis
            if sport == "baseball":
                result.update(self._analyze_baseball(match_id))
            elif sport == "soccer":
                result.update(self._analyze_soccer(match_id))
            elif sport == "basketball":
                result.update(self._analyze_basketball(match_id))
            elif sport == "volleyball":
                result.update(self._analyze_volleyball(match_id))
            else:
                result.update(self._analyze_unknown_sport(match_id))

            # Add common analysis
            result["h2h_analysis"] = self.h2h_agent.analyze(match_id)
            result["home_away_analysis"] = self.home_away_agent.analyze(match_id)

            return result

        except Exception as e:
            logger.error(f"Error in unified analysis: {e}")
            return {"error": f"Analysis failed: {e}"}

    def _get_analysis_type(self, sport: str, match_id: str) -> str:
        """Determine analysis type based on sport and data availability."""
        if sport == "baseball":
            # Check if team_stats data is available
            team_stats = get_baseball_team_stats_data.invoke({"match_id": match_id})
            if team_stats:
                return "baseball_rich"  # Rich analysis with team_stats
            else:
                return "baseball_wdl"   # WDL-only analysis
        else:
            return f"{sport}_wdl"  # WDL-only analysis for other sports

    def _analyze_baseball(self, match_id: str) -> Dict[str, Any]:
        """Analyze baseball match using specialized agents."""
        try:
            # Check data availability
            team_stats = get_baseball_team_stats_data.invoke({"match_id": match_id})

            if team_stats:
                # Rich analysis using team_stats data
                pitcher_analysis = self.baseball_pitcher_agent.analyze(match_id)
                team_analysis = self.baseball_team_agent.analyze(match_id)

                return {
                    "pitcher_analysis": pitcher_analysis,
                    "team_analysis": team_analysis,
                    "analysis_quality": "high",
                    "data_source": "team_stats + WDL"
                }
            else:
                # Fallback to WDL-only analysis
                return self._analyze_with_wdl_only(match_id, "baseball")

        except Exception as e:
            logger.error(f"Error in baseball analysis: {e}")
            return {"error": f"Baseball analysis failed: {e}"}

    def _analyze_soccer(self, match_id: str) -> Dict[str, Any]:
        """Analyze soccer match using soccer agent."""
        try:
            sport_analysis = self.soccer_agent.analyze(match_id)

            return {
                "sport_analysis": sport_analysis,
                "analysis_quality": "medium",
                "data_source": "WDL only"
            }

        except Exception as e:
            logger.error(f"Error in soccer analysis: {e}")
            return {"error": f"Soccer analysis failed: {e}"}

    def _analyze_basketball(self, match_id: str) -> Dict[str, Any]:
        """Analyze basketball match using basketball agent."""
        try:
            sport_analysis = self.basketball_agent.analyze(match_id)

            return {
                "sport_analysis": sport_analysis,
                "analysis_quality": "medium",
                "data_source": "WDL only"
            }

        except Exception as e:
            logger.error(f"Error in basketball analysis: {e}")
            return {"error": f"Basketball analysis failed: {e}"}

    def _analyze_volleyball(self, match_id: str) -> Dict[str, Any]:
        """Analyze volleyball match using volleyball agent."""
        try:
            sport_analysis = self.volleyball_agent.analyze(match_id)

            return {
                "sport_analysis": sport_analysis,
                "analysis_quality": "medium",
                "data_source": "WDL only"
            }

        except Exception as e:
            logger.error(f"Error in volleyball analysis: {e}")
            return {"error": f"Volleyball analysis failed: {e}"}

    def _analyze_unknown_sport(self, match_id: str) -> Dict[str, Any]:
        """Analyze unknown sport using generic WDL analysis."""
        try:
            return self._analyze_with_wdl_only(match_id, "unknown")

        except Exception as e:
            logger.error(f"Error in unknown sport analysis: {e}")
            return {"error": f"Unknown sport analysis failed: {e}"}

    def _analyze_with_wdl_only(self, match_id: str, sport: str) -> Dict[str, Any]:
        """Generic WDL-only analysis for any sport."""
        try:
            # Use H2H and Home/Away agents for basic analysis
            h2h_analysis = self.h2h_agent.analyze(match_id)
            home_away_analysis = self.home_away_agent.analyze(match_id)

            return {
                "sport_analysis": f"{sport} 경기 기본 분석 (WDL 데이터 기반)",
                "analysis_quality": "basic",
                "data_source": "WDL only",
                "note": "상세 통계 데이터가 없어 기본 분석만 제공됩니다."
            }

        except Exception as e:
            logger.error(f"Error in WDL-only analysis: {e}")
            return {"error": f"WDL analysis failed: {e}"}

    def get_target_matches(self) -> List[Dict[str, Any]]:
        """Get all target matches for analysis."""
        try:
            return get_sns_target_matches()
        except Exception as e:
            logger.error(f"Error getting target matches: {e}")
            return []

    def analyze_all_matches(self) -> List[Dict[str, Any]]:
        """Analyze all target matches."""
        target_matches = self.get_target_matches()
        results = []

        for match_data in target_matches:
            try:
                match_id = match_data.get("match_id")
                if match_id:
                    analysis = self.analyze_match(match_id)
                    results.append(analysis)
            except Exception as e:
                logger.error(f"Error analyzing match {match_data.get('match_id', 'unknown')}: {e}")
                continue

        return results
