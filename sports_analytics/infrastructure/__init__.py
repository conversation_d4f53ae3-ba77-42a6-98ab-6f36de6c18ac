"""
Infrastructure layer for sports analytics platform.

This package contains all external integrations and infrastructure components:
- Database access (Supabase)
- LLM providers (OpenAI, Google)
- Logging and configuration
"""

from .database import (
    get_match_data, get_team_stats, get_wdl_data, 
    get_sns_target_matches, get_sport_type
)
from .llm_providers import setup_llm_manager

__all__ = [
    "get_match_data",
    "get_team_stats", 
    "get_wdl_data",
    "get_sns_target_matches",
    "get_sport_type",
    "setup_llm_manager"
]
