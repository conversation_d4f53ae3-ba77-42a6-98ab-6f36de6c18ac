"""
LLM Provider Management Module.

This module provides a unified interface for managing different LLM providers
and models for the sports analytics platform.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

try:
    from langchain_core.language_models import BaseChatModel
    from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
    from langchain_openai import ChatOpenAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    BaseChatModel = object

from sports_analytics.config import LLMSettings

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    @abstractmethod
    def get_model(self, model_name: Optional[str] = None, **kwargs) -> BaseChatModel:
        """Get a chat model instance."""
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider."""
    
    def __init__(self, api_key: str, default_model: str = "gpt-4o-mini"):
        self.api_key = api_key
        self.default_model = default_model
    
    def get_model(self, model_name: Optional[str] = None, **kwargs) -> BaseChatModel:
        """Get OpenAI chat model."""
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("langchain packages are required for LLM functionality")
        
        model = model_name or self.default_model
        temperature = kwargs.get('temperature', 0.3)
        max_tokens = kwargs.get('max_tokens', None)
        
        return ChatOpenAI(
            api_key=self.api_key,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens
        )


class GoogleProvider(LLMProvider):
    """Google Generative AI provider."""
    
    def __init__(self, api_key: str, default_model: str = "gemini-pro"):
        self.api_key = api_key
        self.default_model = default_model
    
    def get_model(self, model_name: Optional[str] = None, **kwargs) -> BaseChatModel:
        """Get Google chat model."""
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("langchain packages are required for LLM functionality")
        
        model = model_name or self.default_model
        temperature = kwargs.get('temperature', 0.3)
        
        return ChatGoogleGenerativeAI(
            google_api_key=self.api_key,
            model=model,
            temperature=temperature
        )


class LLMManager:
    """
    LLM Manager for handling multiple providers and models.
    
    This class provides a centralized way to manage different LLM providers
    and switch between them based on configuration.
    """
    
    _instance: Optional['LLMManager'] = None
    
    def __init__(self, settings: LLMSettings):
        self.settings = settings
        self.providers: Dict[str, LLMProvider] = {}
        self._default_provider: Optional[LLMProvider] = None
        self._setup_providers()
    
    def _setup_providers(self):
        """Setup available LLM providers based on API keys."""
        # Setup OpenAI if key is available
        if self.settings.openai_api_key:
            self.providers['openai'] = OpenAIProvider(
                self.settings.openai_api_key,
                self.settings.default_model if self.settings.default_model.startswith('gpt') else 'gpt-4o-mini'
            )
            logger.info("OpenAI provider configured")
        
        # Setup Google if key is available
        if self.settings.google_api_key:
            self.providers['google'] = GoogleProvider(
                self.settings.google_api_key,
                self.settings.default_model if self.settings.default_model.startswith('gemini') else 'gemini-pro'
            )
            logger.info("Google provider configured")
        
        # Set default provider
        if self.settings.default_provider.value in self.providers:
            self._default_provider = self.providers[self.settings.default_provider.value]
        elif self.providers:
            # Use first available provider as default
            provider_name = list(self.providers.keys())[0]
            self._default_provider = self.providers[provider_name]
            logger.warning(f"Default provider '{self.settings.default_provider}' not available, using '{provider_name}'")
        else:
            raise ValueError("No LLM providers configured. Please provide at least one API key.")
    
    def get_model(self, provider: Optional[str] = None, model_name: Optional[str] = None, **kwargs) -> BaseChatModel:
        """
        Get a model from the specified provider.
        
        Args:
            provider: Provider name ('openai', 'google')
            model_name: Specific model name
            **kwargs: Additional model parameters
            
        Returns:
            BaseChatModel: The requested model instance
        """
        if provider and provider in self.providers:
            return self.providers[provider].get_model(model_name, **kwargs)
        elif self._default_provider:
            return self._default_provider.get_model(model_name, **kwargs)
        else:
            raise ValueError("No LLM providers available")
    
    def get_available_providers(self) -> list[str]:
        """Get list of available provider names."""
        return list(self.providers.keys())
    
    def is_provider_available(self, provider: str) -> bool:
        """Check if a provider is available."""
        return provider in self.providers


def setup_llm_manager(settings: LLMSettings) -> LLMManager:
    """
    Setup and return an LLM manager instance.
    
    Args:
        settings: LLM configuration settings
        
    Returns:
        LLMManager: Configured LLM manager
    """
    if LLMManager._instance is None:
        LLMManager._instance = LLMManager(settings)
    
    return LLMManager._instance


def get_llm_manager() -> Optional[LLMManager]:
    """Get the current LLM manager instance."""
    return LLMManager._instance


# Factory functions for easy access
def create_openai_model(api_key: str, model: str = "gpt-4o-mini", **kwargs) -> BaseChatModel:
    """Create an OpenAI model directly."""
    provider = OpenAIProvider(api_key)
    return provider.get_model(model, **kwargs)


def create_google_model(api_key: str, model: str = "gemini-pro", **kwargs) -> BaseChatModel:
    """Create a Google model directly."""
    provider = GoogleProvider(api_key)
    return provider.get_model(model, **kwargs)
