"""
Unified Configuration Module for Multi-Sport Analytics Platform.

This module manages all configuration settings including:
- Database connections (Supabase)
- LLM API keys (OpenAI, Google)
- Application settings
- Logging configuration
"""

import os
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path(__file__).parent.parent / '.env'
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

# Database Configuration
SUPABASE_URL: Optional[str] = os.getenv("SUPABASE_URL")
SUPABASE_KEY: Optional[str] = os.getenv("SUPABASE_KEY")

# LLM API Configuration
OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")

# Application Configuration
APP_ENVIRONMENT: str = os.getenv("APP_ENVIRONMENT", "development")
LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

# Default LLM Provider
DEFAULT_LLM_PROVIDER: str = "openai" if OPENAI_API_KEY else "google"

# Validation
def validate_config():
    """Validate required configuration."""
    errors = []

    if not SUPABASE_URL:
        errors.append("SUPABASE_URL is required")
    if not SUPABASE_KEY:
        errors.append("SUPABASE_KEY is required")
    if not OPENAI_API_KEY and not GEMINI_API_KEY:
        errors.append("At least one LLM API key (OPENAI_API_KEY or GEMINI_API_KEY) is required")

    if errors:
        raise ValueError(f"Configuration errors: {', '.join(errors)}")

# Auto-validate on import
try:
    validate_config()
except ValueError as e:
    print(f"⚠️  Configuration Warning: {e}")
    print("Please check your environment variables or .env file")