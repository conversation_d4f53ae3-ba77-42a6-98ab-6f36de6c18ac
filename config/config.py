"""
Supabase 및 OpenAI API 설정 관리 모듈
"""
import os
from typing import Optional
from dotenv import load_dotenv

# .env 파일 로드
load_dotenv()

# API 키 설정
OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")
SUPABASE_URL: Optional[str] = os.getenv("SUPABASE_URL")
SUPABASE_KEY: Optional[str] = os.getenv("SUPABASE_SECRET_ROLE")



# 개발 모드에서만 사용할 디버깅 코드
if __name__ == "__main__":
    print(f"OpenAI API Key 설정됨: {bool(OPENAI_API_KEY)}")
    print(f"Supabase URL: {SUPABASE_URL}")
    print(f"Supabase Key 설정됨: {bool(SUPABASE_KEY)}")
    print(f"Gemini API Key 설정됨: {bool(GEMINI_API_KEY)}")