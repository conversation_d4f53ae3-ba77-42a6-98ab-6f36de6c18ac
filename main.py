#!/usr/bin/env python3
"""
Unified Multi-Sport Analytics Platform Main Application.

This application uses the actual Supabase database structure and provides
sport-specific analysis using appropriate agents:

- Baseball: Uses team_stats table + WDL data (rich analysis)
- Soccer/Basketball/Volleyball: Uses WDL data only
- Automatic sport detection and agent selection
- Tool-based modular architecture

Usage:
    python main.py list                    # List target matches by sport
    python main.py analyze <match_id>      # Analyze specific match
    python main.py batch                   # Analyze all target matches
"""

import argparse
import logging
import sys
from pathlib import Path

import config.config as global_config
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import LLMSettings
from baseball_analytics.agents.unified_agents import UnifiedAnalysisService


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    
    # Create log directory
    Path("logs").mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/unified_analytics.log')
        ]
    )


def create_services():
    """Create unified analysis service."""
    logger = logging.getLogger(__name__)
    
    try:
        # Setup LLM manager
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        
        # Create unified analysis service
        llm = llm_manager.get_model()
        analysis_service = UnifiedAnalysisService(llm)
        
        logger.info("Unified analysis service initialized successfully")
        return {
            'analysis_service': analysis_service,
            'llm_manager': llm_manager
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise


def list_command(args, services) -> None:
    """Handle list target matches command."""
    logger = logging.getLogger(__name__)
    
    try:
        analysis_service = services['analysis_service']
        
        # Get target matches
        target_matches = analysis_service.get_target_matches()
        
        if target_matches:
            # Group by sport
            sport_groups = {}
            for match_data in target_matches:
                sport = match_data.get('sports', 'unknown').lower()
                sport_name = {
                    'baseball': '⚾ 야구',
                    '야구': '⚾ 야구',
                    'bseball': '⚾ 야구',
                    'soccer': '⚽ 축구',
                    'football': '⚽ 축구',
                    '축구': '⚽ 축구',
                    'basketball': '🏀 농구',
                    '농구': '🏀 농구',
                    'volleyball': '🏐 배구',
                    '배구': '🏐 배구'
                }.get(sport, f'🏆 {sport}')
                
                if sport_name not in sport_groups:
                    sport_groups[sport_name] = []
                sport_groups[sport_name].append(match_data)
            
            print(f"\n📋 분석 대상 경기 ({len(target_matches)}개)")
            print("=" * 60)
            
            for sport_name, matches in sport_groups.items():
                print(f"\n{sport_name} ({len(matches)}개)")
                print("-" * 30)
                
                for i, match_data in enumerate(matches, 1):
                    match_id = match_data.get("match_id", "Unknown")
                    home_team = match_data.get("home_team", "Unknown")
                    away_team = match_data.get("away_team", "Unknown")
                    match_date = match_data.get("match_date", "Unknown")
                    match_time = match_data.get("match_time", "Unknown")
                    
                    print(f"  {i:2d}. {match_id}")
                    print(f"      🏟️ {home_team} vs {away_team}")
                    print(f"      📅 {match_date} {match_time}")
                    print()
        else:
            print("현재 분석 대상 경기가 없습니다.")
            
    except Exception as e:
        logger.error(f"Failed to list matches: {e}")
        print(f"❌ Error: {e}")


def analyze_command(args, services) -> None:
    """Handle match analysis command."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting analysis for match: {args.match_id}")
        
        analysis_service = services['analysis_service']
        
        # Perform analysis
        analysis = analysis_service.analyze_match(args.match_id)
        
        if "error" in analysis:
            print(f"❌ Error: {analysis['error']}")
            return
        
        # Display results
        sport = analysis.get('sport', 'unknown')
        analysis_type = analysis.get('analysis_type', 'unknown')
        
        sport_name = {
            'baseball': '⚾ 야구',
            'soccer': '⚽ 축구',
            'basketball': '🏀 농구',
            'volleyball': '🏐 배구'
        }.get(sport, f'🏆 {sport}')
        
        print("\n" + "=" * 80)
        print(f"🏟️ {sport_name} 전문 분석 ({analysis_type})")
        print("=" * 80)
        print()
        
        print(f"🏟️ {analysis.get('home_team', 'Unknown')} vs {analysis.get('away_team', 'Unknown')}")
        print(f"📅 {analysis.get('match_date', 'Unknown')}")
        print()
        
        # Common analysis sections
        print("🔄 맞대결 분석")
        print("-" * 40)
        print(analysis.get('h2h_analysis', '분석 없음'))
        print()
        
        print("🏠 홈/원정 폼 분석")
        print("-" * 40)
        print(analysis.get('home_away_analysis', '분석 없음'))
        print()
        
        # Sport-specific sections
        if analysis_type == "baseball_rich":
            print("⚾ 투수 분석")
            print("-" * 40)
            print(analysis.get('pitcher_analysis', '분석 없음'))
            print()
            
            print("🏏 팀 분석")
            print("-" * 40)
            print(analysis.get('team_analysis', '분석 없음'))
            print()
            
        else:
            print(f"{sport_name} 종합 분석")
            print("-" * 40)
            print(analysis.get('sport_analysis', '분석 없음'))
            print()
        
        print("=" * 80)
        
        logger.info("Analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        print(f"❌ Error: {e}")
        sys.exit(1)


def batch_command(args, services) -> None:
    """Handle batch analysis command."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting batch analysis of all target matches")
        
        analysis_service = services['analysis_service']
        
        # Analyze all target matches
        analyses = analysis_service.analyze_all_target_matches()
        
        if not analyses:
            print("분석할 경기가 없습니다.")
            return
        
        # Group results by sport
        sport_groups = {}
        for analysis in analyses:
            if "error" in analysis:
                continue
                
            sport = analysis.get('sport', 'unknown')
            sport_name = {
                'baseball': '⚾ 야구',
                'soccer': '⚽ 축구',
                'basketball': '🏀 농구',
                'volleyball': '🏐 배구'
            }.get(sport, f'🏆 {sport}')
            
            if sport_name not in sport_groups:
                sport_groups[sport_name] = []
            sport_groups[sport_name].append(analysis)
        
        print(f"\n📊 일괄 분석 결과 ({len(analyses)}개 경기)")
        print("=" * 80)
        
        for sport_name, sport_analyses in sport_groups.items():
            print(f"\n{sport_name} ({len(sport_analyses)}개)")
            print("-" * 50)
            
            for i, analysis in enumerate(sport_analyses, 1):
                print(f"\n{i}. {analysis.get('home_team', 'Unknown')} vs {analysis.get('away_team', 'Unknown')}")
                print(f"   📅 {analysis.get('match_date', 'Unknown')}")
                print(f"   🔍 분석 타입: {analysis.get('analysis_type', 'Unknown')}")
                print(f"   📊 경기 ID: {analysis.get('match_id', 'Unknown')}")
        
        print("\n" + "=" * 80)
        logger.info("Batch analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Batch analysis failed: {e}")
        print(f"❌ Error: {e}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Unified Multi-Sport Analytics Platform"
    )
    
    # Global options
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List target matches by sport")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze a specific match")
    analyze_parser.add_argument(
        "match_id", 
        type=str, 
        help="Match ID to analyze"
    )
    
    # Batch analyze command
    batch_parser = subparsers.add_parser("batch", help="Analyze all target matches")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Unified Multi-Sport Analytics Platform")
    
    # Create services
    try:
        services = create_services()
        logger.info("Services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"❌ Initialization error: {e}")
        print("Please check your LLM API configurations.")
        sys.exit(1)
    
    # Execute command
    if args.command == "list":
        list_command(args, services)
    elif args.command == "analyze":
        analyze_command(args, services)
    elif args.command == "batch":
        batch_command(args, services)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
