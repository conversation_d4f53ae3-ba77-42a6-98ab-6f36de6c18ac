#!/usr/bin/env python3
"""
Test script for the unified multi-sport analysis system.

This script tests the integration with actual Supabase data
and verifies that all components work correctly.
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config.config as global_config
from baseball_analytics.infrastructure.unified_database import (
    get_match_data, get_team_stats, get_wdl_data, get_sns_target_matches,
    get_sport_type, is_baseball_match
)
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import LLMSettings
from baseball_analytics.agents.unified_agents import UnifiedAnalysisService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_database_connection():
    """Test Supabase database connection and data availability."""
    print("🔍 Testing database connection...")
    
    try:
        # Test basic query
        target_matches = get_sns_target_matches()
        print(f"✅ Database connection successful")
        print(f"📊 Found {len(target_matches)} target matches")
        
        if target_matches:
            # Group by sport and test data availability
            sport_counts = {}
            test_matches = {}
            
            for match_data in target_matches:
                sport = get_sport_type(match_data)
                sport_name = {
                    'baseball': '⚾ 야구',
                    'soccer': '⚽ 축구',
                    'basketball': '🏀 농구',
                    'volleyball': '🏐 배구'
                }.get(sport, f'🏆 {sport}')
                
                sport_counts[sport_name] = sport_counts.get(sport_name, 0) + 1
                
                # Store first match of each sport for testing
                if sport_name not in test_matches:
                    test_matches[sport_name] = match_data["match_id"]
            
            print("\n📈 Sport distribution:")
            for sport, count in sport_counts.items():
                print(f"   {sport}: {count}개")
            
            # Test data retrieval for each sport
            print("\n🔍 Testing data availability by sport:")
            for sport_name, match_id in test_matches.items():
                print(f"\n   {sport_name} ({match_id}):")
                
                # Test basic match data
                match_data = get_match_data(match_id)
                if match_data:
                    print(f"     ✅ Match data: {match_data['home_team']} vs {match_data['away_team']}")
                else:
                    print(f"     ❌ Match data: Failed")
                    continue
                
                # Test WDL data
                h2h_wdl = get_wdl_data(match_id, 'h2h', 'summary')
                home_wdl = get_wdl_data(match_id, 'home', 'summary')
                away_wdl = get_wdl_data(match_id, 'away', 'summary')
                print(f"     📈 H2H WDL: {'✅' if h2h_wdl else '❌'}")
                print(f"     🏠 Home WDL: {'✅' if home_wdl else '❌'}")
                print(f"     🚌 Away WDL: {'✅' if away_wdl else '❌'}")
                
                # Test team_stats for baseball
                if is_baseball_match(match_data):
                    team_stats = get_team_stats(match_id)
                    print(f"     ⚾ Team stats: {'✅' if team_stats else '❌'} ({len(team_stats)} records)")
            
            return True, test_matches
        else:
            print("⚠️  No target matches found")
            return True, {}
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False, {}


def test_llm_connection():
    """Test LLM API connection."""
    print("\n🤖 Testing LLM connection...")
    
    try:
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Test simple query
        test_response = llm.invoke("안녕하세요. 간단한 테스트입니다.")
        print(f"✅ LLM connection successful")
        print(f"🔤 Test response: {str(test_response)[:50]}...")
        
        return True, llm
        
    except Exception as e:
        print(f"❌ LLM connection failed: {e}")
        return False, None


def test_unified_analysis(test_matches: dict):
    """Test unified analysis service."""
    print(f"\n⚽ Testing unified analysis service...")
    
    try:
        # Setup components
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Create unified analysis service
        analysis_service = UnifiedAnalysisService(llm)
        
        # Test each sport
        for sport_name, match_id in test_matches.items():
            print(f"\n   🔄 Testing {sport_name} analysis ({match_id})...")
            
            try:
                analysis = analysis_service.analyze_match(match_id)
                
                if "error" in analysis:
                    print(f"     ❌ Analysis failed: {analysis['error']}")
                    continue
                
                print(f"     ✅ Analysis completed")
                print(f"     📊 Match: {analysis.get('home_team', 'Unknown')} vs {analysis.get('away_team', 'Unknown')}")
                print(f"     🏆 Sport: {analysis.get('sport', 'Unknown')}")
                print(f"     🔍 Analysis type: {analysis.get('analysis_type', 'Unknown')}")
                
                # Check sport-specific analysis
                if analysis.get('analysis_type') == 'baseball_rich':
                    print(f"     ⚾ Pitcher analysis: {'✅' if analysis.get('pitcher_analysis') else '❌'}")
                    print(f"     🏏 Team analysis: {'✅' if analysis.get('team_analysis') else '❌'}")
                else:
                    print(f"     🏆 Sport analysis: {'✅' if analysis.get('sport_analysis') else '❌'}")
                
                print(f"     🔄 H2H analysis: {'✅' if analysis.get('h2h_analysis') else '❌'}")
                print(f"     🏠 Home/Away analysis: {'✅' if analysis.get('home_away_analysis') else '❌'}")
                
            except Exception as e:
                print(f"     ❌ Analysis failed: {e}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ Unified analysis test failed: {e}")
        return False


def test_batch_analysis():
    """Test batch analysis of multiple sports."""
    print("\n📊 Testing batch analysis...")
    
    try:
        # Setup components
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Create analysis service
        analysis_service = UnifiedAnalysisService(llm)
        
        # Perform batch analysis (limit to first 3 matches for testing)
        print("🔄 Running batch analysis (first 3 matches)...")
        target_matches = analysis_service.get_target_matches()[:3]
        
        results = []
        for match_data in target_matches:
            try:
                match_id = match_data.get("match_id")
                if match_id:
                    analysis = analysis_service.analyze_match(match_id)
                    if "error" not in analysis:
                        results.append(analysis)
            except Exception as e:
                logger.error(f"Error in batch analysis: {e}")
                continue
        
        print(f"✅ Batch analysis completed")
        print(f"📊 Analyzed {len(results)} matches")
        
        # Group by sport
        sport_counts = {}
        for analysis in results:
            sport = analysis.get('sport', 'unknown')
            sport_counts[sport] = sport_counts.get(sport, 0) + 1
        
        print("\n📈 Analysis results by sport:")
        for sport, count in sport_counts.items():
            sport_name = {
                'baseball': '⚾ 야구',
                'soccer': '⚽ 축구',
                'basketball': '🏀 농구',
                'volleyball': '🏐 배구'
            }.get(sport, f'🏆 {sport}')
            print(f"   {sport_name}: {count}개")
        
        # Show sample results
        print("\n📋 Sample results:")
        for analysis in results[:2]:
            print(f"   🏟️ {analysis.get('home_team', 'Unknown')} vs {analysis.get('away_team', 'Unknown')} ({analysis.get('sport', 'unknown')})")
            print(f"      🔍 Analysis type: {analysis.get('analysis_type', 'Unknown')}")
        
        return True, results
        
    except Exception as e:
        print(f"❌ Batch analysis test failed: {e}")
        return False, None


def main():
    """Run all tests."""
    print("🚀 Starting Unified Multi-Sport Analysis System Tests")
    print("=" * 60)
    
    # Test 1: Database connection
    db_ok, test_matches = test_database_connection()
    if not db_ok:
        print("❌ Database tests failed. Please check your Supabase configuration.")
        return False
    
    # Test 2: LLM connection
    llm_ok, llm = test_llm_connection()
    if not llm_ok:
        print("❌ LLM tests failed. Please check your API keys.")
        return False
    
    # Test 3: Unified analysis (if we have test matches)
    if test_matches:
        analysis_ok = test_unified_analysis(test_matches)
        if not analysis_ok:
            print("❌ Unified analysis tests failed.")
            return False
        
        # Test 4: Batch analysis
        batch_ok, batch_results = test_batch_analysis()
        if not batch_ok:
            print("❌ Batch analysis tests failed.")
            return False
    else:
        print("⚠️  Skipping analysis tests (no target matches available)")
    
    print("\n🎉 All tests completed successfully!")
    print("=" * 60)
    print("✅ Database connection: OK")
    print("✅ LLM connection: OK")
    if test_matches:
        print("✅ Unified analysis: OK")
        print("✅ Batch analysis: OK")
    print("\n🚀 Unified system is ready for use!")
    
    # Show usage examples
    print("\n📖 Usage Examples:")
    print("=" * 60)
    print("# List available matches by sport:")
    print("python main.py list")
    print()
    if test_matches:
        # Show example for each sport
        for sport_name, match_id in list(test_matches.items())[:2]:
            print(f"# Analyze {sport_name} match:")
            print(f"python main.py analyze {match_id}")
            print()
    print("# Batch analyze all matches:")
    print("python main.py batch")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
