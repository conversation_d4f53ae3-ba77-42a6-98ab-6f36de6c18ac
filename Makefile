# Baseball Analytics Platform Makefile

.PHONY: help install install-dev test lint format clean run

help:
	@echo "Available commands:"
	@echo "  install     - Install package and dependencies"
	@echo "  install-dev - Install package with development dependencies"
	@echo "  test        - Run tests"
	@echo "  lint        - Run linting checks"
	@echo "  format      - Format code"
	@echo "  clean       - Clean build artifacts"
	@echo "  run         - Run the application"

install:
	pip install -e .

install-dev:
	pip install -e ".[dev]"

test:
	pytest tests/ -v --cov=baseball_analytics --cov-report=html

lint:
	ruff check baseball_analytics/
	mypy baseball_analytics/

format:
	black baseball_analytics/
	isort baseball_analytics/
	ruff format baseball_analytics/

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

run:
	python -m baseball_analytics.main

# Development commands
dev-setup: install-dev
	cp .env.template .env
	@echo "Please edit .env file with your configuration"

analyze-example:
	python -m baseball_analytics.main analyze BSW025073025 --content-type social_media_post

list-matches:
	python -m baseball_analytics.main list --days 7
