"""
Real Data Baseball Analysis Example.

This example demonstrates how to use the real Supabase data
for baseball analysis with the enhanced CAG architecture.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

from baseball_analytics.infrastructure.supabase_mcp_repository import SupabaseMCPRepository
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import get_settings
from baseball_analytics.agents.real_baseball_agent import RealAnalysisService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealDataAnalyzer:
    """
    Real data analyzer using actual Supabase database.
    
    This demonstrates the complete workflow using real data:
    1. Connect to actual Supabase database
    2. Retrieve real match data
    3. Perform AI-powered analysis
    4. Generate content in SporTic365 style
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.repository = None
        self.llm_manager = None
        self.analysis_service = None
    
    def setup(self):
        """Setup all components with real data connections."""
        logger.info("Setting up real data analyzer...")
        
        # Setup Supabase MCP repository
        self.repository = SupabaseMCPRepository(
            self.settings.database.supabase_url,
            self.settings.database.supabase_key
        )
        
        # Setup LLM manager
        self.llm_manager = setup_llm_manager(self.settings.llm)
        
        # Setup real analysis service
        llm = self.llm_manager.get_model()
        self.analysis_service = RealAnalysisService(llm, self.repository)
        
        logger.info("Real data analyzer setup completed")
    
    def test_database_connection(self):
        """Test database connection and data availability."""
        logger.info("Testing database connection...")
        
        try:
            # Test basic connection
            target_matches = self.repository.get_sns_target_matches()
            logger.info(f"Found {len(target_matches)} target matches")
            
            if target_matches:
                # Test data retrieval for first match
                match_data = target_matches[0]
                match_id = match_data["match_id"]
                logger.info(f"Testing data retrieval for match: {match_id}")
                
                # Test match data
                match = self.repository.get_match_by_id(match_id)
                logger.info(f"Match data: {match.home_team.name} vs {match.away_team.name}")
                
                # Test WDL data
                h2h_summary = self.repository.get_wdl_data(match_id, 'h2h', 'summary')
                logger.info(f"H2H summary available: {h2h_summary is not None}")
                
                # Test content data
                h2h_content = self.repository.get_h2h_content(match_id)
                logger.info(f"H2H content available: {h2h_content is not None}")
                
                # Test betting data
                betting_picks = self.repository.get_betting_picks(match_id)
                logger.info(f"Betting picks available: {betting_picks is not None}")
                
                return True
            else:
                logger.warning("No target matches found")
                return False
                
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def analyze_upcoming_matches(self) -> Dict[str, Any]:
        """Analyze all upcoming matches with real data."""
        logger.info("Analyzing upcoming matches with real data...")
        
        try:
            # Get analyses
            analyses = self.analysis_service.analyze_upcoming_matches()
            
            results = []
            for analysis in analyses:
                # Create summary
                summary = {
                    "match_id": analysis.match_id,
                    "teams": f"{analysis.home_team} vs {analysis.away_team}",
                    "match_date": analysis.match_date,
                    "venue": analysis.venue,
                    "prediction": analysis.final_prediction,
                    "recommendation": analysis.recommendation,
                    "confidence": analysis.confidence,
                    "key_factors": analysis.key_factors[:3],  # Top 3 factors
                    "social_media_content": self.analysis_service.format_for_social_media(analysis)
                }
                results.append(summary)
            
            return {
                "total_matches": len(results),
                "analyses": results,
                "analyzed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing upcoming matches: {e}")
            raise
    
    def analyze_specific_match_detailed(self, match_id: str) -> Dict[str, Any]:
        """Perform detailed analysis of a specific match."""
        logger.info(f"Performing detailed analysis for match: {match_id}")
        
        try:
            # Get comprehensive analysis
            analysis = self.analysis_service.analyze_specific_match(match_id)
            
            # Get additional raw data for reference
            match = self.repository.get_match_by_id(match_id)
            h2h_summary = self.repository.get_wdl_data(match_id, 'h2h', 'summary')
            home_wdl = self.repository.get_wdl_data(match_id, 'home', 'summary')
            away_wdl = self.repository.get_wdl_data(match_id, 'away', 'summary')
            h2h_content = self.repository.get_h2h_content(match_id)
            existing_content = self.repository.get_naver_text(match_id)
            betting_picks = self.repository.get_betting_picks(match_id)
            
            # Create detailed result
            result = {
                "analysis": analysis.dict(),
                "raw_data": {
                    "match_info": {
                        "match_id": match.match_id,
                        "home_team": match.home_team.name,
                        "away_team": match.away_team.name,
                        "match_date": match.match_date.isoformat(),
                        "venue": match.venue,
                        "sport": match.sport.value
                    },
                    "h2h_summary": h2h_summary,
                    "home_wdl_summary": home_wdl,
                    "away_wdl_summary": away_wdl,
                    "h2h_content": h2h_content,
                    "existing_content": existing_content,
                    "betting_picks": betting_picks
                },
                "formatted_content": {
                    "social_media": self.analysis_service.format_for_social_media(analysis),
                    "hashtags": analysis.hashtags
                },
                "metadata": {
                    "analyzed_at": datetime.now().isoformat(),
                    "confidence": analysis.confidence,
                    "data_sources": [
                        "target_games",
                        "sportic_contents", 
                        "sportic_sns",
                        "sportic_pick"
                    ]
                }
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error in detailed analysis: {e}")
            raise
    
    def demonstrate_sportic365_style(self, match_id: str):
        """Demonstrate SporTic365 style analysis output."""
        logger.info(f"Generating SporTic365 style analysis for match: {match_id}")
        
        try:
            analysis = self.analysis_service.analyze_specific_match(match_id)
            
            print("=" * 80)
            print(f"SporTic365 스타일 야구 분석")
            print("=" * 80)
            print()
            
            print(f"🏟️ {analysis.home_team} vs {analysis.away_team}")
            print(f"📅 {analysis.match_date}")
            if analysis.venue:
                print(f"🏟️ {analysis.venue}")
            print()
            
            print("📊 SporTic365 요약")
            print("-" * 40)
            print(analysis.summary)
            print()
            
            print("✅ 경기 요약")
            print("-" * 40)
            print(analysis.recent_h2h)
            print()
            
            print("🎯 배당 인사이트")
            print("-" * 40)
            print(analysis.betting_insight)
            print()
            
            print("🔍 분석 포인트")
            print("-" * 40)
            print(analysis.team_form_analysis)
            print()
            
            print("💡 종합 판단 및 베팅 추천")
            print("-" * 40)
            print(analysis.final_prediction)
            print()
            print(f"🎯 최종 추천: {analysis.recommendation}")
            confidence_stars = "🔴" * min(4, int(analysis.confidence * 4))
            confidence_stars += "⚪" * (4 - min(4, int(analysis.confidence * 4)))
            print(f"추천도: {confidence_stars} ({analysis.confidence:.1f}/4)")
            print()
            
            print("🏷️ 해시태그")
            print("-" * 40)
            print(" ".join(analysis.hashtags))
            print()
            
            print("=" * 80)
            
        except Exception as e:
            logger.error(f"Error generating SporTic365 style output: {e}")
            raise


def main():
    """Main function to demonstrate real data analysis."""
    analyzer = RealDataAnalyzer()
    
    try:
        # Setup
        analyzer.setup()
        
        # Test database connection
        print("=== Database Connection Test ===")
        connection_ok = analyzer.test_database_connection()
        if not connection_ok:
            print("Database connection failed. Please check your configuration.")
            return
        
        print("Database connection successful!")
        print()
        
        # Analyze upcoming matches
        print("=== Upcoming Matches Analysis ===")
        upcoming_results = analyzer.analyze_upcoming_matches()
        print(f"Found {upcoming_results['total_matches']} matches to analyze")
        
        # Show summary of first few matches
        for i, match_summary in enumerate(upcoming_results['analyses'][:3]):
            print(f"\n{i+1}. {match_summary['teams']}")
            print(f"   예측: {match_summary['prediction']}")
            print(f"   신뢰도: {match_summary['confidence']:.2f}")
            print(f"   주요 요인: {', '.join(match_summary['key_factors'])}")
        
        # Detailed analysis of first match
        if upcoming_results['analyses']:
            first_match_id = upcoming_results['analyses'][0]['match_id']
            print(f"\n=== Detailed Analysis: {first_match_id} ===")
            
            detailed_result = analyzer.analyze_specific_match_detailed(first_match_id)
            print(f"Match: {detailed_result['raw_data']['match_info']['home_team']} vs {detailed_result['raw_data']['match_info']['away_team']}")
            print(f"Confidence: {detailed_result['metadata']['confidence']:.2f}")
            print(f"Data sources: {', '.join(detailed_result['metadata']['data_sources'])}")
            
            # Show SporTic365 style output
            print(f"\n=== SporTic365 Style Output ===")
            analyzer.demonstrate_sportic365_style(first_match_id)
    
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise


if __name__ == "__main__":
    main()
