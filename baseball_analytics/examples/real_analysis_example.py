"""
Real Baseball Analysis Example using Enhanced Database and Agents.

This example demonstrates how to use the new CAG-based architecture
with actual database data and specialized agents.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

from baseball_analytics.infrastructure.enhanced_database import (
    DatabaseConnectionManager, 
    BaseballAnalysisRepository,
    RepositoryFactory
)
from baseball_analytics.infrastructure.llm_providers import (
    LLMProviderFactory, 
    LLMManager,
    setup_llm_manager
)
from baseball_analytics.infrastructure.config import get_settings
from baseball_analytics.agents.baseball_agents import BaseballAnalysisOrchestrator
from baseball_analytics.domain.models import ContentGenerationRequest, ContentType

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealBaseballAnalyzer:
    """
    Real baseball analyzer using the enhanced architecture.
    
    This class demonstrates the complete workflow from data retrieval
    to AI-powered analysis and content generation.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.repository = None
        self.llm_manager = None
        self.orchestrator = None
    
    def setup(self):
        """Setup all components."""
        logger.info("Setting up baseball analyzer...")
        
        # Setup database repository
        self.repository = RepositoryFactory.create_baseball_repository(
            self.settings.database.supabase_url,
            self.settings.database.supabase_key
        )
        
        # Test database connection
        if not self.repository._connection_manager.test_connection():
            raise ConnectionError("Failed to connect to database")
        
        # Setup LLM manager
        self.llm_manager = setup_llm_manager(self.settings.llm)
        
        # Setup analysis orchestrator
        llm = self.llm_manager.get_model()
        self.orchestrator = BaseballAnalysisOrchestrator(llm, self.repository)
        
        logger.info("Setup completed successfully")
    
    def analyze_upcoming_matches(self) -> Dict[str, Any]:
        """Analyze all upcoming matches."""
        logger.info("Retrieving upcoming matches...")
        
        # Get target matches for analysis
        upcoming_matches = self.repository.get_sns_target_matches()
        
        if not upcoming_matches:
            logger.info("No upcoming matches found")
            return {"matches": [], "total": 0}
        
        results = []
        for match_data in upcoming_matches:
            try:
                logger.info(f"Analyzing match: {match_data.match_id}")
                
                # Perform comprehensive analysis
                analysis = self.orchestrator.analyze_match_comprehensive(match_data.match_id)
                
                # Create analysis summary
                summary = self._create_analysis_summary(analysis)
                results.append(summary)
                
                logger.info(f"Analysis completed for match: {match_data.match_id}")
                
            except Exception as e:
                logger.error(f"Error analyzing match {match_data.match_id}: {e}")
                continue
        
        return {
            "matches": results,
            "total": len(results),
            "analyzed_at": datetime.now().isoformat()
        }
    
    def analyze_specific_match(self, match_id: str) -> Dict[str, Any]:
        """Analyze a specific match in detail."""
        logger.info(f"Analyzing specific match: {match_id}")
        
        try:
            # Get match data
            match = self.repository.get_match_by_id(match_id)
            if not match:
                raise ValueError(f"Match not found: {match_id}")
            
            # Perform comprehensive analysis
            analysis = self.orchestrator.analyze_match_comprehensive(match_id)
            
            # Get additional data for detailed analysis
            wdl_summary = self.repository.get_wdl_data(match_id, 'h2h', 'summary')
            home_wdl = self.repository.get_wdl_data(match_id, 'home', 'summary')
            away_wdl = self.repository.get_wdl_data(match_id, 'away', 'summary')
            
            # Create detailed analysis result
            result = {
                "match_info": {
                    "match_id": match.match_id,
                    "home_team": match.home_team.name,
                    "away_team": match.away_team.name,
                    "match_date": match.match_date.isoformat(),
                    "venue": match.venue
                },
                "comprehensive_analysis": analysis.dict(),
                "additional_data": {
                    "h2h_summary": wdl_summary,
                    "home_wdl_summary": home_wdl,
                    "away_wdl_summary": away_wdl
                },
                "analysis_metadata": {
                    "analyzed_at": datetime.now().isoformat(),
                    "confidence": analysis.overall_confidence,
                    "recommendation": analysis.recommendation
                }
            }
            
            logger.info(f"Detailed analysis completed for match: {match_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error in detailed analysis: {e}")
            raise
    
    def generate_analysis_content(self, match_id: str, content_type: str = "social_media_post") -> Dict[str, Any]:
        """Generate content based on analysis."""
        logger.info(f"Generating {content_type} content for match: {match_id}")
        
        try:
            # Perform analysis
            analysis = self.orchestrator.analyze_match_comprehensive(match_id)
            
            # Convert to analysis result format for content generation
            from baseball_analytics.domain.models import AnalysisResult, AnalysisInsight
            
            # Create insights from comprehensive analysis
            insights = []
            for factor in analysis.key_factors:
                insights.append(AnalysisInsight(
                    category="general",
                    title="주요 요인",
                    content=factor,
                    confidence=analysis.overall_confidence
                ))
            
            analysis_result = AnalysisResult(
                match_id=match_id,
                insights=insights,
                prediction=None,  # Could add prediction conversion
                key_points=analysis.key_factors,
                hashtags=self._generate_hashtags(analysis),
                confidence_score=analysis.overall_confidence
            )
            
            # Generate content using the generation layer
            from baseball_analytics.cag.generation import ContentGenerationFactory
            
            llm = self.llm_manager.get_model()
            content_engine = ContentGenerationFactory.create_default_engine(llm)
            
            request = ContentGenerationRequest(
                analysis_result=analysis_result,
                content_type=content_type,
                target_audience="야구팬",
                tone="전문적",
                include_hashtags=True
            )
            
            generated_content = content_engine.generate_content(request)
            
            result = {
                "content": generated_content.dict(),
                "analysis_summary": {
                    "prediction": analysis.prediction,
                    "recommendation": analysis.recommendation,
                    "confidence": analysis.overall_confidence
                },
                "generated_at": datetime.now().isoformat()
            }
            
            logger.info(f"Content generation completed for match: {match_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error generating content: {e}")
            raise
    
    def _create_analysis_summary(self, analysis) -> Dict[str, Any]:
        """Create a summary of the comprehensive analysis."""
        return {
            "match_id": analysis.match_id,
            "teams": f"{analysis.home_team} vs {analysis.away_team}",
            "match_date": analysis.match_date,
            "prediction": analysis.prediction,
            "recommendation": analysis.recommendation,
            "key_factors": analysis.key_factors[:3],  # Top 3 factors
            "confidence": analysis.overall_confidence,
            "pitcher_summary": {
                "home": analysis.pitcher_analysis.get("home", {}).get("analysis_summary", "N/A") if analysis.pitcher_analysis.get("home") else "N/A",
                "away": analysis.pitcher_analysis.get("away", {}).get("analysis_summary", "N/A") if analysis.pitcher_analysis.get("away") else "N/A"
            },
            "betting_summary": {
                "home_odds": analysis.betting_analysis.home_odds,
                "away_odds": analysis.betting_analysis.away_odds,
                "recommended_bet": analysis.betting_analysis.recommended_bet
            }
        }
    
    def _generate_hashtags(self, analysis) -> list[str]:
        """Generate hashtags from analysis."""
        hashtags = ["#KBO", "#야구분석", "#프로야구"]
        
        # Add team hashtags
        home_tag = f"#{analysis.home_team.replace(' ', '')}"
        away_tag = f"#{analysis.away_team.replace(' ', '')}"
        hashtags.extend([home_tag, away_tag])
        
        # Add matchup hashtag
        hashtags.append(f"#{analysis.home_team.replace(' ', '')}vs{analysis.away_team.replace(' ', '')}")
        
        return hashtags


def main():
    """Main function to demonstrate the analyzer."""
    analyzer = RealBaseballAnalyzer()
    
    try:
        # Setup
        analyzer.setup()
        
        # Example 1: Analyze upcoming matches
        print("=== Analyzing Upcoming Matches ===")
        upcoming_results = analyzer.analyze_upcoming_matches()
        print(f"Found {upcoming_results['total']} matches to analyze")
        
        for match_summary in upcoming_results['matches'][:2]:  # Show first 2
            print(f"\nMatch: {match_summary['teams']}")
            print(f"Prediction: {match_summary['prediction']}")
            print(f"Confidence: {match_summary['confidence']:.2f}")
            print(f"Key Factors: {', '.join(match_summary['key_factors'])}")
        
        # Example 2: Detailed analysis of specific match
        if upcoming_results['matches']:
            match_id = upcoming_results['matches'][0]['match_id']
            print(f"\n=== Detailed Analysis for {match_id} ===")
            
            detailed_result = analyzer.analyze_specific_match(match_id)
            print(f"Match: {detailed_result['match_info']['home_team']} vs {detailed_result['match_info']['away_team']}")
            print(f"Recommendation: {detailed_result['analysis_metadata']['recommendation']}")
            
            # Example 3: Generate content
            print(f"\n=== Generating Content for {match_id} ===")
            content_result = analyzer.generate_analysis_content(match_id, "social_media_post")
            print(f"Generated Content Title: {content_result['content']['title']}")
            print(f"Content Preview: {content_result['content']['body'][:100]}...")
    
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise


if __name__ == "__main__":
    main()
