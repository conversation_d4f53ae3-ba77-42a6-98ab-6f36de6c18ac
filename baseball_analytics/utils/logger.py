"""
Unified logging configuration for multi-sport analytics platform.

This module provides centralized logging functionality for all components
of the unified analytics platform.
"""

import datetime
import logging
import os
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path

# Constants
DEFAULT_LOG_LEVEL = logging.INFO
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
MAX_LOG_DAYS = 7  # Log retention days

# Log level mapping
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

# Logger cache
_loggers = {}


def setup_logging(log_level=DEFAULT_LOG_LEVEL, log_dir=None):
    """
    Setup centralized logging system.
    
    Args:
        log_level: Logging level (default: logging.INFO)
        log_dir: Log directory path (default: logs/)
        
    Returns:
        logging.Logger: Configured root logger
    """
    # Create log directory
    if log_dir is None:
        log_dir = Path(__file__).resolve().parent.parent.parent / 'logs'
    else:
        log_dir = Path(log_dir)
    
    os.makedirs(log_dir, exist_ok=True)
    
    # Log file with date
    today = datetime.datetime.now().strftime('%Y%m%d')
    log_file = log_dir / f'unified_analytics_{today}.log'
    
    # Root logger setup
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_format = logging.Formatter(DEFAULT_LOG_FORMAT)
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(log_level)
    file_format = logging.Formatter(DEFAULT_LOG_FORMAT)
    file_handler.setFormatter(file_format)
    root_logger.addHandler(file_handler)
    
    # Suppress noisy third-party logs
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('supabase').setLevel(logging.WARNING)
    
    return root_logger


def get_logger(name, log_level=None):
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        log_level: Logger level (optional)
        
    Returns:
        logging.Logger: Configured logger
    """
    # Return cached logger if exists
    if name in _loggers:
        logger = _loggers[name]
        if log_level is not None:
            logger.setLevel(log_level)
        return logger
    
    # Create new logger
    logger = logging.getLogger(name)
    
    # Prevent propagation to avoid duplicate logs
    logger.propagate = False
    
    # Set log level
    if log_level is not None:
        logger.setLevel(log_level)
    else:
        logger.setLevel(logging.getLogger().level)
    
    # Copy handlers from root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        logger.addHandler(handler)
    
    # Cache logger
    _loggers[name] = logger
    
    return logger


def set_log_level(logger_name=None, level='info'):
    """
    Set log level for a logger.
    
    Args:
        logger_name: Logger name (None for root logger)
        level: Log level string
    """
    log_level = LOG_LEVELS.get(level.lower(), logging.INFO)
    
    if logger_name is None or logger_name == 'root':
        # Set root logger level
        logging.getLogger().setLevel(log_level)
        
        # Update all handlers
        root_logger = logging.getLogger()
        for handler in root_logger.handlers:
            handler.setLevel(log_level)
        
        # Update cached loggers
        for _, logger in _loggers.items():
            logger.setLevel(log_level)
    else:
        # Set specific logger level
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
        
        # Update cache
        if logger_name in _loggers:
            _loggers[logger_name].setLevel(log_level)


# Initialize logging system on import
setup_logging()
