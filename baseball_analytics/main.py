"""
Main application entry point for Baseball Analytics Platform.

This module provides the main CLI interface and application setup.
"""

import argparse
import logging
import sys
from pathlib import Path

import config.config as global_config
from baseball_analytics.application.services import ApplicationServiceFactory
from baseball_analytics.domain.models import ContentType
from baseball_analytics.infrastructure.config import (create_directories,
                                                      get_settings,
                                                      validate_settings)
from baseball_analytics.infrastructure.database import (CacheRepository,
                                                        SupabaseConnection,
                                                        SupabaseRepository)
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager


def setup_logging(settings) -> None:
    """Setup logging configuration."""
    # Create log directory if needed
    if settings.logging.file_enabled:
        log_path = Path(settings.logging.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, settings.logging.level))
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Console handler
    if settings.logging.console_enabled:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, settings.logging.level))
        console_formatter = logging.Formatter(settings.logging.format)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if settings.logging.file_enabled:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            settings.logging.file_path,
            maxBytes=settings.logging.file_max_size,
            backupCount=settings.logging.file_backup_count
        )
        file_handler.setLevel(getattr(logging, settings.logging.level))
        file_formatter = logging.Formatter(settings.logging.format)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)


def create_application_services(command=None):
    """Create and configure application services."""
    # Use global config directly instead of pydantic settings
    # settings = get_settings()
    
    # Setup database connection using global config
    db_connection = SupabaseConnection(
        global_config.SUPABASE_URL,
        global_config.SUPABASE_KEY
    )
    
    # Create repository with caching enabled by default
    base_repository = SupabaseRepository(db_connection)
    repository = CacheRepository(base_repository, cache_ttl=300)
    
    services = {}
    
    # Always create scheduling service (needed for list command)
    scheduling_service = ApplicationServiceFactory.create_scheduling_service(repository)
    services['scheduling'] = scheduling_service
    
    # Only create LLM-dependent services for analyze command
    if command == "analyze":
        # Setup LLM manager using global config
        from baseball_analytics.infrastructure.config import LLMSettings
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY
        )
        llm_manager = setup_llm_manager(llm_settings)
        
        # Create application services
        analysis_service = ApplicationServiceFactory.create_analysis_service(
            repository, llm_manager
        )
        publishing_service = ApplicationServiceFactory.create_publishing_service()
        analytics_service = ApplicationServiceFactory.create_analytics_service()
        
        services.update({
            'analysis': analysis_service,
            'publishing': publishing_service,
            'analytics': analytics_service
        })
    
    return services


def analyze_match_command(args, services) -> None:
    """Handle match analysis command."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting analysis for match: {args.match_id}")
        
        analysis_service = services['analysis']
        
        if args.content_type:
            # Analyze and generate content
            analysis_result, content = analysis_service.analyze_and_generate_content(
                args.match_id,
                args.content_type,
                target_audience=args.audience,
                tone=args.tone,
                max_length=args.max_length,
                include_hashtags=not args.no_hashtags
            )
            
            print(f"\n=== Analysis Result ===")
            print(f"Match ID: {analysis_result.match_id}")
            print(f"Confidence: {analysis_result.confidence_score:.2f}")
            print(f"Key Points:")
            for point in analysis_result.key_points:
                print(f"  - {point}")
            
            if analysis_result.prediction:
                pred = analysis_result.prediction
                print(f"\nPrediction:")
                print(f"  Home Win: {pred.home_win_probability:.1%}")
                print(f"  Away Win: {pred.away_win_probability:.1%}")
                if pred.draw_probability > 0:
                    print(f"  Draw: {pred.draw_probability:.1%}")
            
            print(f"\n=== Generated Content ===")
            print(f"Type: {content.content_type}")
            print(f"Title: {content.title}")
            print(f"Content:\n{content.body}")
            if content.hashtags:
                print(f"Hashtags: {' '.join(content.hashtags)}")
        
        else:
            # Analysis only
            analysis_result = analysis_service.analyze_match(args.match_id)
            
            print(f"\n=== Analysis Result ===")
            print(f"Match ID: {analysis_result.match_id}")
            print(f"Confidence: {analysis_result.confidence_score:.2f}")
            
            print(f"\nInsights:")
            for insight in analysis_result.insights:
                print(f"  [{insight.category}] {insight.title}")
                print(f"    {insight.content}")
                print(f"    Confidence: {insight.confidence:.2f}")
            
            print(f"\nKey Points:")
            for point in analysis_result.key_points:
                print(f"  - {point}")
            
            if analysis_result.prediction:
                pred = analysis_result.prediction
                print(f"\nPrediction:")
                print(f"  Home Win: {pred.home_win_probability:.1%}")
                print(f"  Away Win: {pred.away_win_probability:.1%}")
                if pred.draw_probability > 0:
                    print(f"  Draw: {pred.draw_probability:.1%}")
        
        logger.info("Analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        print(f"Error: {e}")
        sys.exit(1)


def list_matches_command(args, services) -> None:
    """Handle list matches command."""
    logger = logging.getLogger(__name__)
    
    try:
        scheduling_service = services['scheduling']
        matches = scheduling_service.get_upcoming_matches(args.days)
        
        if matches:
            print(f"\nUpcoming matches (next {args.days} days):")
            for match_id in matches:
                print(f"  - {match_id}")
        else:
            print("No upcoming matches found.")
            
    except Exception as e:
        logger.error(f"Failed to list matches: {e}")
        print(f"Error: {e}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Baseball Analytics Platform - CAG-based analysis and content generation"
    )
    
    # Global options
    parser.add_argument(
        "--config", 
        type=str, 
        help="Path to configuration file"
    )
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze a baseball match")
    analyze_parser.add_argument(
        "match_id", 
        type=str, 
        help="Match ID to analyze"
    )
    analyze_parser.add_argument(
        "--content-type", 
        type=str, 
        choices=[ct.value for ct in ContentType],
        help="Generate content of specified type"
    )
    analyze_parser.add_argument(
        "--audience", 
        type=str, 
        default="general",
        help="Target audience for content"
    )
    analyze_parser.add_argument(
        "--tone", 
        type=str, 
        default="professional",
        choices=["professional", "casual", "enthusiastic", "analytical"],
        help="Content tone"
    )
    analyze_parser.add_argument(
        "--max-length", 
        type=int, 
        help="Maximum content length"
    )
    analyze_parser.add_argument(
        "--no-hashtags", 
        action="store_true",
        help="Exclude hashtags from content"
    )
    
    # List matches command
    list_parser = subparsers.add_parser("list", help="List upcoming matches")
    list_parser.add_argument(
        "--days", 
        type=int, 
        default=7,
        help="Number of days to look ahead"
    )
    
    # Parse arguments
    args = parser.parse_args()
    
    # Setup basic logging
    logging.basicConfig(level=logging.DEBUG if args.debug else logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Create necessary directories
    Path("logs").mkdir(exist_ok=True)
    
    logger.info("Starting Baseball Analytics Platform")
    logger.info("Environment: production")
    
    # Create application services
    try:
        services = create_application_services(args.command)
        logger.info("Application services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"Initialization error: {e}")
        sys.exit(1)
    
    # Execute command
    if args.command == "analyze":
        analyze_match_command(args, services)
    elif args.command == "list":
        list_matches_command(args, services)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
