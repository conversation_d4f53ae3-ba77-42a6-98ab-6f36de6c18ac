"""
Real Baseball Analytics Main Application.

This module provides the main CLI interface using real Supabase data
and the enhanced CAG architecture with specialized agents.
"""

import argparse
import logging
import sys
from pathlib import Path

import config.config as config
from baseball_analytics.agents.real_baseball_agent import RealAnalysisService
from baseball_analytics.infrastructure.config import LLMSettings
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.supabase_mcp_repository import \
    SupabaseMCPRepository


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    
    # Create log directory
    Path("logs").mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/baseball_analytics.log')
        ]
    )


def create_real_services():
    """Create services using real Supabase data."""
    logger = logging.getLogger(__name__)
    
    try:
        # Validate required environment variables
        if not config.SUPABASE_URL:
            raise ValueError("SUPABASE_URL is required")
        if not config.SUPABASE_KEY:
            raise ValueError("SUPABASE_KEY is required")
        
        # Setup Supabase MCP repository
        repository = SupabaseMCPRepository(
            config.SUPABASE_URL,
            config.SUPABASE_KEY
        )
        
        # Setup LLM manager
        llm_settings = LLMSettings(
            openai_api_key=config.OPENAI_API_KEY,
            google_api_key=config.GEMINI_API_KEY,
            default_provider=(
                "openai" if config.OPENAI_API_KEY else "google"
            )
        )
        llm_manager = setup_llm_manager(llm_settings)
        
        # Create real analysis service
        llm = llm_manager.get_model()
        analysis_service = RealAnalysisService(llm, repository)
        
        logger.info("Real services initialized successfully")
        return {
            'repository': repository,
            'analysis_service': analysis_service,
            'llm_manager': llm_manager
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize real services: {e}")
        raise


def analyze_command(args, services) -> None:
    """Handle real match analysis command."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting real analysis for match: {args.match_id}")
        
        analysis_service = services['analysis_service']
        
        # Perform analysis
        analysis = analysis_service.analyze_specific_match(args.match_id)
        
        # Display results
        print("\n" + "=" * 80)
        print("🏟️ SporTic365 스타일 야구 분석")
        print("=" * 80)
        print()
        
        print(f"🏟️ {analysis.home_team} vs {analysis.away_team}")
        print(f"📅 {analysis.match_date}")
        if analysis.venue:
            print(f"🏟️ {analysis.venue}")
        print()
        
        print("📊 SporTic365 요약")
        print("-" * 40)
        print(analysis.summary)
        print()
        
        print("✅ 경기 요약")
        print("-" * 40)
        print(analysis.recent_h2h)
        print()
        
        print("🎯 배당 인사이트")
        print("-" * 40)
        print(analysis.betting_insight)
        print()
        
        print("🔍 분석 포인트")
        print("-" * 40)
        print(analysis.team_form_analysis)
        print()
        
        if analysis.key_factors:
            print("🔑 주요 승부 요인")
            print("-" * 40)
            for i, factor in enumerate(analysis.key_factors, 1):
                print(f"{i}. {factor}")
            print()
        
        print("💡 종합 판단 및 베팅 추천")
        print("-" * 40)
        print(analysis.final_prediction)
        print()
        print(f"🎯 최종 추천: {analysis.recommendation}")
        
        # Confidence visualization
        confidence_stars = "🔴" * min(4, int(analysis.confidence * 4))
        confidence_stars += "⚪" * (
            4 - min(4, int(analysis.confidence * 4))
        )
        print(f"추천도: {confidence_stars} ({analysis.confidence:.1f}/4)")
        print()
        
        # Social media format
        if args.social_media:
            print("📱 소셜 미디어 포맷")
            print("-" * 40)
            social_format = analysis_service.format_for_social_media(analysis)
            print(social_format)
            print()
        
        # Hashtags
        if args.hashtags:
            print("🏷️ 해시태그")
            print("-" * 40)
            print(" ".join(analysis.hashtags))
            print()
        
        print("=" * 80)
        
        logger.info("Real analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Real analysis failed: {e}")
        print(f"Error: {e}")
        sys.exit(1)


def list_command(args, services) -> None:
    """Handle list upcoming matches command."""
    logger = logging.getLogger(__name__)
    
    try:
        repository = services['repository']
        
        # Get target matches
        target_matches = repository.get_sns_target_matches()
        
        if target_matches:
            print(f"\n📋 분석 대상 경기 ({len(target_matches)}개)")
            print("=" * 60)
            
            for i, match_data in enumerate(target_matches, 1):
                match_id = match_data.get("match_id", "Unknown")
                home_team = match_data.get("home_team", "Unknown")
                away_team = match_data.get("away_team", "Unknown")
                match_date = match_data.get("match_date", "Unknown")
                match_time = match_data.get("match_time", "Unknown")
                
                print(f"{i:2d}. {match_id}")
                print(f"    🏟️ {home_team} vs {away_team}")
                print(f"    📅 {match_date} {match_time}")
                print()
        else:
            print("현재 분석 대상 경기가 없습니다.")
            
    except Exception as e:
        logger.error(f"Failed to list matches: {e}")
        print(f"Error: {e}")


def batch_analyze_command(args, services) -> None:
    """Handle batch analysis of all upcoming matches."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting batch analysis of upcoming matches")
        
        analysis_service = services['analysis_service']
        
        # Analyze all upcoming matches
        analyses = analysis_service.analyze_upcoming_matches()
        
        if not analyses:
            print("분석할 경기가 없습니다.")
            return
        
        print(f"\n📊 일괄 분석 결과 ({len(analyses)}개 경기)")
        print("=" * 80)
        
        for i, analysis in enumerate(analyses, 1):
            print(f"\n{i}. {analysis.home_team} vs {analysis.away_team}")
            print(f"   📅 {analysis.match_date}")
            print(f"   🎯 예측: {analysis.final_prediction}")
            print(f"   💡 추천: {analysis.recommendation}")
            
            confidence_stars = "🔴" * min(4, int(analysis.confidence * 4))
            confidence_stars += "⚪" * (
                4 - min(4, int(analysis.confidence * 4))
            )
            confidence_text = f"({analysis.confidence:.1f}/4)"
            print(f"   📊 신뢰도: {confidence_stars} {confidence_text}")
            
            if analysis.key_factors:
                factors_text = ', '.join(analysis.key_factors[:2])
                print(f"   🔑 주요 요인: {factors_text}")
        
        print("\n" + "=" * 80)
        logger.info("Batch analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Batch analysis failed: {e}")
        print(f"Error: {e}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Real Baseball Analytics Platform - "
                    "Using actual Supabase data"
    )
    
    # Global options
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(
        dest="command", 
        help="Available commands"
    )
    
    # Analyze command
    analyze_parser = subparsers.add_parser(
        "analyze", 
        help="Analyze a specific baseball match"
    )
    analyze_parser.add_argument(
        "match_id", 
        type=str, 
        help="Match ID to analyze"
    )
    analyze_parser.add_argument(
        "--social-media", 
        action="store_true",
        help="Include social media formatted output"
    )
    analyze_parser.add_argument(
        "--hashtags", 
        action="store_true",
        help="Show hashtags"
    )
    
    # List command
    subparsers.add_parser("list", help="List upcoming matches")
    
    # Batch analyze command
    subparsers.add_parser(
        "batch", 
        help="Analyze all upcoming matches"
    )
    
    # Parse arguments
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Real Baseball Analytics Platform")
    
    # Create services
    try:
        services = create_real_services()
        logger.info("Services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"Initialization error: {e}")
        print("Please check your Supabase and LLM API configurations.")
        sys.exit(1)
    
    # Execute command
    if args.command == "analyze":
        analyze_command(args, services)
    elif args.command == "list":
        list_command(args, services)
    elif args.command == "batch":
        batch_analyze_command(args, services)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
