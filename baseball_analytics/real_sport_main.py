"""
Real Multi-Sport Analysis Main Application.

This application uses the actual Supabase database structure and provides
sport-specific analysis using appropriate agents:

- Baseball: Uses team_stats table + WDL data
- Soccer/Basketball/Volleyball: Uses WDL data only

Each sport has its own specialized agent that understands the available data.
"""

import argparse
import logging
import sys
from pathlib import Path

import config.config as global_config
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import LLMSettings
from baseball_analytics.agents.sport_specific_agents import MultiSportAnalysisService
from baseball_analytics.domain.models import SportType


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    
    # Create log directory
    Path("logs").mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/multi_sport_analytics.log')
        ]
    )


def create_services():
    """Create services using real Supabase data."""
    logger = logging.getLogger(__name__)
    
    try:
        # Setup Supabase repository
        repository = RealSupabaseRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        # Setup LLM manager
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        
        # Create multi-sport analysis service
        llm = llm_manager.get_model()
        analysis_service = MultiSportAnalysisService(llm, repository)
        
        logger.info("Multi-sport services initialized successfully")
        return {
            'repository': repository,
            'analysis_service': analysis_service,
            'llm_manager': llm_manager
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise


def analyze_command(args, services) -> None:
    """Handle sport-specific match analysis command."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting analysis for match: {args.match_id}")
        
        analysis_service = services['analysis_service']
        repository = services['repository']
        
        # Get match data to determine sport
        match_data = repository.get_match_data(args.match_id)
        if not match_data:
            print(f"❌ Match not found: {args.match_id}")
            return
        
        sport_type = repository.get_sport_type(match_data)
        sport_name = {
            SportType.BASEBALL: "야구",
            SportType.SOCCER: "축구", 
            SportType.BASKETBALL: "농구",
            SportType.VOLLEYBALL: "배구"
        }.get(sport_type, "알 수 없음")
        
        # Perform analysis
        analysis = analysis_service.analyze_match(args.match_id)
        
        # Display results based on sport type
        print("\n" + "=" * 80)
        print(f"🏟️ {sport_name} 전문 분석")
        print("=" * 80)
        print()
        
        print(f"🏟️ {analysis.home_team} vs {analysis.away_team}")
        print(f"📅 {analysis.match_date}")
        print(f"🏆 종목: {sport_name}")
        print()
        
        print("📊 경기 요약")
        print("-" * 40)
        print(analysis.summary)
        print()
        
        # Sport-specific sections
        if sport_type == SportType.BASEBALL:
            print("⚾ 투수 분석")
            print("-" * 40)
            print(analysis.pitcher_analysis)
            print()
            
            print("🏏 타격 분석")
            print("-" * 40)
            print(analysis.batting_analysis)
            print()
            
        elif sport_type == SportType.SOCCER:
            print("⚽ 전술 분석")
            print("-" * 40)
            print(analysis.formation_analysis)
            print()
            
            print("🏥 부상자 현황")
            print("-" * 40)
            print(analysis.injury_report)
            print()
            
        elif sport_type == SportType.BASKETBALL:
            print("🏀 공격 분석")
            print("-" * 40)
            print(analysis.offense_analysis)
            print()
            
            print("🛡️ 수비 분석")
            print("-" * 40)
            print(analysis.defense_analysis)
            print()
            
        elif sport_type == SportType.VOLLEYBALL:
            print("🏐 공격 분석")
            print("-" * 40)
            print(analysis.attack_analysis)
            print()
            
            print("🚫 블로킹 분석")
            print("-" * 40)
            print(analysis.block_analysis)
            print()
        
        # Common sections
        print("📈 최근 폼 분석")
        print("-" * 40)
        print(analysis.recent_form)
        print()
        
        print("🔄 맞대결 분석")
        print("-" * 40)
        print(analysis.h2h_analysis)
        print()
        
        if analysis.key_factors:
            print("🔑 주요 승부 요인")
            print("-" * 40)
            for i, factor in enumerate(analysis.key_factors, 1):
                print(f"{i}. {factor}")
            print()
        
        print("💡 최종 예측 및 추천")
        print("-" * 40)
        print(f"🎯 예측: {analysis.prediction}")
        print(f"💰 추천: {analysis.recommendation}")
        
        # Confidence visualization
        confidence_stars = "🔴" * min(4, int(analysis.confidence * 4))
        confidence_stars += "⚪" * (4 - min(4, int(analysis.confidence * 4)))
        print(f"📊 신뢰도: {confidence_stars} ({analysis.confidence:.1f}/4)")
        print()
        
        # Hashtags
        if args.hashtags:
            print("🏷️ 해시태그")
            print("-" * 40)
            print(" ".join(analysis.hashtags))
            print()
        
        print("=" * 80)
        
        logger.info("Analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        print(f"❌ Error: {e}")
        sys.exit(1)


def list_command(args, services) -> None:
    """Handle list upcoming matches command."""
    logger = logging.getLogger(__name__)
    
    try:
        repository = services['repository']
        
        # Get target matches
        target_matches = repository.get_sns_target_matches()
        
        if target_matches:
            # Group by sport
            sport_groups = {}
            for match_data in target_matches:
                sport_type = repository.get_sport_type(match_data)
                sport_name = {
                    SportType.BASEBALL: "야구",
                    SportType.SOCCER: "축구",
                    SportType.BASKETBALL: "농구", 
                    SportType.VOLLEYBALL: "배구"
                }.get(sport_type, "기타")
                
                if sport_name not in sport_groups:
                    sport_groups[sport_name] = []
                sport_groups[sport_name].append(match_data)
            
            print(f"\n📋 분석 대상 경기 ({len(target_matches)}개)")
            print("=" * 60)
            
            for sport_name, matches in sport_groups.items():
                print(f"\n🏆 {sport_name} ({len(matches)}개)")
                print("-" * 30)
                
                for i, match_data in enumerate(matches, 1):
                    match_id = match_data.get("match_id", "Unknown")
                    home_team = match_data.get("home_team", "Unknown")
                    away_team = match_data.get("away_team", "Unknown")
                    match_date = match_data.get("match_date", "Unknown")
                    match_time = match_data.get("match_time", "Unknown")
                    
                    print(f"  {i:2d}. {match_id}")
                    print(f"      🏟️ {home_team} vs {away_team}")
                    print(f"      📅 {match_date} {match_time}")
                    print()
        else:
            print("현재 분석 대상 경기가 없습니다.")
            
    except Exception as e:
        logger.error(f"Failed to list matches: {e}")
        print(f"❌ Error: {e}")


def batch_command(args, services) -> None:
    """Handle batch analysis of all upcoming matches."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting batch analysis of all target matches")
        
        analysis_service = services['analysis_service']
        
        # Analyze all target matches
        analyses = analysis_service.analyze_all_target_matches()
        
        if not analyses:
            print("분석할 경기가 없습니다.")
            return
        
        # Group results by sport
        sport_groups = {}
        for analysis in analyses:
            sport = analysis.sport
            if sport not in sport_groups:
                sport_groups[sport] = []
            sport_groups[sport].append(analysis)
        
        print(f"\n📊 일괄 분석 결과 ({len(analyses)}개 경기)")
        print("=" * 80)
        
        for sport, sport_analyses in sport_groups.items():
            sport_name = {
                "baseball": "⚾ 야구",
                "soccer": "⚽ 축구",
                "basketball": "🏀 농구",
                "volleyball": "🏐 배구"
            }.get(sport, f"🏆 {sport}")
            
            print(f"\n{sport_name} ({len(sport_analyses)}개)")
            print("-" * 50)
            
            for i, analysis in enumerate(sport_analyses, 1):
                print(f"\n{i}. {analysis.home_team} vs {analysis.away_team}")
                print(f"   📅 {analysis.match_date}")
                print(f"   🎯 예측: {analysis.prediction}")
                print(f"   💡 추천: {analysis.recommendation}")
                
                confidence_stars = "🔴" * min(4, int(analysis.confidence * 4))
                confidence_stars += "⚪" * (4 - min(4, int(analysis.confidence * 4)))
                print(f"   📊 신뢰도: {confidence_stars} ({analysis.confidence:.1f}/4)")
                
                if analysis.key_factors:
                    print(f"   🔑 주요 요인: {', '.join(analysis.key_factors[:2])}")
        
        print("\n" + "=" * 80)
        logger.info("Batch analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Batch analysis failed: {e}")
        print(f"❌ Error: {e}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Multi-Sport Analytics Platform - Real database integration"
    )
    
    # Global options
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze a specific match")
    analyze_parser.add_argument(
        "match_id", 
        type=str, 
        help="Match ID to analyze"
    )
    analyze_parser.add_argument(
        "--hashtags", 
        action="store_true",
        help="Show hashtags"
    )
    
    # List command
    list_parser = subparsers.add_parser("list", help="List upcoming matches by sport")
    
    # Batch analyze command
    batch_parser = subparsers.add_parser("batch", help="Analyze all upcoming matches")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Multi-Sport Analytics Platform")
    
    # Create services
    try:
        services = create_services()
        logger.info("Services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"❌ Initialization error: {e}")
        print("Please check your Supabase and LLM API configurations.")
        sys.exit(1)
    
    # Execute command
    if args.command == "analyze":
        analyze_command(args, services)
    elif args.command == "list":
        list_command(args, services)
    elif args.command == "batch":
        batch_command(args, services)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
