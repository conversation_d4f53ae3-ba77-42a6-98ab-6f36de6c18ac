"""
Real Baseball Analysis Agent using actual Supabase data.

This agent analyzes real baseball matches using data from the Supabase database
and provides comprehensive analysis similar to the SporTic365 format.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from baseball_analytics.infrastructure.supabase_mcp_repository import SupabaseMCPRepository
from baseball_analytics.domain.models import Match

logger = logging.getLogger(__name__)


class RealMatchAnalysis(BaseModel):
    """Real match analysis output structure."""
    
    match_id: str = Field(..., description="경기 ID")
    home_team: str = Field(..., description="홈팀")
    away_team: str = Field(..., description="원정팀")
    match_date: str = Field(..., description="경기 날짜")
    venue: Optional[str] = Field(None, description="경기장")
    
    # Analysis sections
    summary: str = Field(..., description="경기 요약")
    recent_h2h: str = Field(..., description="최근 맞대결 분석")
    team_form_analysis: str = Field(..., description="팀 폼 분석")
    key_factors: List[str] = Field(..., description="주요 승부 요인")
    betting_insight: str = Field(..., description="배당 인사이트")
    final_prediction: str = Field(..., description="최종 예측")
    recommendation: str = Field(..., description="베팅 추천")
    confidence: float = Field(..., ge=0.0, le=1.0, description="신뢰도")
    
    # Hashtags for social media
    hashtags: List[str] = Field(default_factory=list, description="해시태그")


class RealBaseballAnalysisAgent:
    """
    Real baseball analysis agent using actual Supabase data.
    
    This agent provides analysis similar to the SporTic365 format:
    - Recent H2H analysis
    - Team form and performance
    - Betting odds analysis
    - Key factors and predictions
    """
    
    def __init__(self, llm: BaseChatModel, repository: SupabaseMCPRepository):
        self.llm = llm
        self.repository = repository
        self.prompt_template = self._create_analysis_prompt()
    
    def _create_analysis_prompt(self) -> ChatPromptTemplate:
        """Create comprehensive analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 전문 분석가입니다.
            SporTic365 스타일의 상세하고 전문적인 야구 분석을 제공합니다.
            
            분석 스타일:
            - 데이터 기반의 객관적 분석
            - 최근 폼과 맞대결 기록 중시
            - 투수 매치업의 중요성 강조
            - 배당률과 시장 심리 반영
            - 구체적인 베팅 추천 제공
            
            분석 구조:
            1. 경기 요약 (팀 상황, 최근 성적)
            2. 최근 맞대결 분석 (H2H 기록, 패턴)
            3. 팀 폼 분석 (홈/원정 성적, 득실점)
            4. 주요 승부 요인 (투수, 타선, 특이사항)
            5. 배당 인사이트 (시장 평가, 가치 분석)
            6. 최종 예측 및 추천
            
            전문적이고 신뢰할 수 있는 분석을 제공하세요.
            """),
            ("human", """
            다음 경기를 분석해주세요:
            
            경기 정보:
            {match_info}
            
            최근 맞대결 기록:
            {h2h_data}
            
            팀별 WDL 요약:
            홈팀 WDL: {home_wdl}
            원정팀 WDL: {away_wdl}
            
            H2H 콘텐츠:
            {h2h_content}
            
            기존 분석 콘텐츠:
            {existing_content}
            
            베팅 정보:
            {betting_info}
            
            SporTic365 스타일의 상세한 분석을 제공해주세요.
            특히 투수 매치업, 최근 폼, 배당률 분석에 중점을 두세요.
            """)
        ])
    
    def analyze_match(self, match_id: str) -> RealMatchAnalysis:
        """
        Analyze a real match using Supabase data.
        
        Args:
            match_id: The match identifier
            
        Returns:
            RealMatchAnalysis: Comprehensive analysis result
        """
        try:
            logger.info(f"Starting real analysis for match: {match_id}")
            
            # Get match data
            match = self.repository.get_match_by_id(match_id)
            if not match:
                raise ValueError(f"Match not found: {match_id}")
            
            # Collect all available data
            analysis_data = self._collect_analysis_data(match_id, match)
            
            # Generate analysis using LLM
            analysis = self._generate_analysis(analysis_data)
            
            logger.info(f"Real analysis completed for match: {match_id}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error in real match analysis: {e}")
            raise
    
    def _collect_analysis_data(self, match_id: str, match: Match) -> Dict[str, Any]:
        """Collect all available data for analysis."""
        data = {
            "match": match,
            "match_info": self._format_match_info(match),
        }
        
        # Get H2H data
        h2h_summary = self.repository.get_wdl_data(match_id, 'h2h', 'summary')
        h2h_matches = self.repository.get_wdl_data(match_id, 'h2h', 'matches')
        data["h2h_summary"] = h2h_summary
        data["h2h_matches"] = h2h_matches
        data["h2h_data"] = self._format_h2h_data(h2h_summary, h2h_matches)
        
        # Get team WDL data
        home_wdl = self.repository.get_wdl_data(match_id, 'home', 'summary')
        away_wdl = self.repository.get_wdl_data(match_id, 'away', 'summary')
        data["home_wdl"] = self._format_wdl_data(home_wdl, "홈")
        data["away_wdl"] = self._format_wdl_data(away_wdl, "원정")
        
        # Get H2H content
        h2h_content = self.repository.get_h2h_content(match_id)
        data["h2h_content"] = self._format_h2h_content(h2h_content)
        
        # Get existing content
        existing_content = self.repository.get_naver_text(match_id)
        data["existing_content"] = existing_content or "기존 분석 없음"
        
        # Get betting information
        betting_picks = self.repository.get_betting_picks(match_id)
        data["betting_info"] = self._format_betting_info(betting_picks)
        
        return data
    
    def _generate_analysis(self, data: Dict[str, Any]) -> RealMatchAnalysis:
        """Generate analysis using LLM."""
        try:
            # Create structured output chain
            chain = self.prompt_template | self.llm.with_structured_output(RealMatchAnalysis)
            
            # Execute analysis
            result = chain.invoke(data)
            
            # Add generated hashtags
            result.hashtags = self._generate_hashtags(data["match"])
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating analysis: {e}")
            # Return fallback analysis
            return self._create_fallback_analysis(data)
    
    def _format_match_info(self, match: Match) -> str:
        """Format match information for prompt."""
        return f"""
        경기 ID: {match.match_id}
        홈팀: {match.home_team.name}
        원정팀: {match.away_team.name}
        경기 날짜: {match.match_date.strftime('%Y-%m-%d %H:%M')}
        경기장: {match.venue or 'N/A'}
        종목: {match.sport.value}
        """
    
    def _format_h2h_data(self, summary: Optional[Dict], matches: Optional[List]) -> str:
        """Format head-to-head data for prompt."""
        if not summary:
            return "최근 맞대결 데이터가 없습니다."
        
        formatted = "최근 맞대결 요약:\n"
        if isinstance(summary, dict):
            for key, value in summary.items():
                formatted += f"- {key}: {value}\n"
        
        if matches and isinstance(matches, list):
            formatted += "\n최근 경기 결과:\n"
            for i, match in enumerate(matches[:5]):  # Last 5 matches
                if isinstance(match, dict):
                    formatted += f"- 경기 {i+1}: {match}\n"
        
        return formatted
    
    def _format_wdl_data(self, wdl_data: Optional[Dict], team_type: str) -> str:
        """Format WDL data for prompt."""
        if not wdl_data:
            return f"{team_type} WDL 데이터가 없습니다."
        
        formatted = f"{team_type} 성적:\n"
        if isinstance(wdl_data, dict):
            for key, value in wdl_data.items():
                formatted += f"- {key}: {value}\n"
        
        return formatted
    
    def _format_h2h_content(self, content: Optional[Dict]) -> str:
        """Format H2H content for prompt."""
        if not content:
            return "H2H 콘텐츠가 없습니다."
        
        if isinstance(content, dict):
            # Extract Korean content if available
            if 'ko' in content:
                return str(content['ko'])
            else:
                return str(content)
        
        return str(content)
    
    def _format_betting_info(self, betting_data: Optional[Dict]) -> str:
        """Format betting information for prompt."""
        if not betting_data:
            return "베팅 정보가 없습니다."
        
        formatted = "베팅 정보:\n"
        if isinstance(betting_data, dict):
            for key, value in betting_data.items():
                if key not in ['match_id', 'created_at', 'updated_at']:
                    formatted += f"- {key}: {value}\n"
        
        return formatted
    
    def _generate_hashtags(self, match: Match) -> List[str]:
        """Generate hashtags for the analysis."""
        hashtags = ["#KBO", "#야구분석", "#프로야구"]
        
        # Add team hashtags
        home_clean = match.home_team.name.replace(" ", "").replace("히어로즈", "").replace("랜더스", "").replace("트윈스", "").replace("위즈", "").replace("라이온즈", "").replace("자이언츠", "").replace("베어스", "").replace("타이거즈", "").replace("이글스", "").replace("다이노스", "")
        away_clean = match.away_team.name.replace(" ", "").replace("히어로즈", "").replace("랜더스", "").replace("트윈스", "").replace("위즈", "").replace("라이온즈", "").replace("자이언츠", "").replace("베어스", "").replace("타이거즈", "").replace("이글스", "").replace("다이노스", "")
        
        hashtags.extend([f"#{home_clean}", f"#{away_clean}"])
        
        # Add matchup hashtag
        hashtags.append(f"#{home_clean}vs{away_clean}")
        
        # Add date hashtag
        date_str = match.match_date.strftime("%m%d")
        hashtags.append(f"#{date_str}")
        hashtags.append(f"#{date_str}분석")
        
        # Add year hashtag
        year_str = match.match_date.strftime("%Y")
        hashtags.append(f"#{year_str}")
        
        # Add additional relevant hashtags
        hashtags.extend(["#스포틱", "#프리뷰", "#스포츠분석", "#야구경기", "#야구팁"])
        
        return hashtags
    
    def _create_fallback_analysis(self, data: Dict[str, Any]) -> RealMatchAnalysis:
        """Create fallback analysis when LLM fails."""
        match = data["match"]
        
        return RealMatchAnalysis(
            match_id=match.match_id,
            home_team=match.home_team.name,
            away_team=match.away_team.name,
            match_date=match.match_date.strftime('%Y-%m-%d %H:%M'),
            venue=match.venue,
            summary="데이터 부족으로 기본 분석만 제공됩니다.",
            recent_h2h="최근 맞대결 분석 불가",
            team_form_analysis="팀 폼 분석 불가",
            key_factors=["데이터 부족"],
            betting_insight="배당 분석 불가",
            final_prediction="예측 불가",
            recommendation="베팅 보류 권장",
            confidence=0.3,
            hashtags=self._generate_hashtags(match)
        )


class RealAnalysisService:
    """Service for managing real baseball analysis."""
    
    def __init__(self, llm: BaseChatModel, repository: SupabaseMCPRepository):
        self.agent = RealBaseballAnalysisAgent(llm, repository)
        self.repository = repository
    
    def analyze_upcoming_matches(self) -> List[RealMatchAnalysis]:
        """Analyze all upcoming matches."""
        logger.info("Analyzing upcoming matches...")
        
        # Get target matches
        target_matches = self.repository.get_sns_target_matches()
        
        results = []
        for match_data in target_matches:
            try:
                match_id = match_data["match_id"]
                analysis = self.agent.analyze_match(match_id)
                results.append(analysis)
                logger.info(f"Analysis completed for match: {match_id}")
            except Exception as e:
                logger.error(f"Error analyzing match {match_data.get('match_id', 'unknown')}: {e}")
                continue
        
        return results
    
    def analyze_specific_match(self, match_id: str) -> RealMatchAnalysis:
        """Analyze a specific match."""
        return self.agent.analyze_match(match_id)
    
    def format_for_social_media(self, analysis: RealMatchAnalysis) -> str:
        """Format analysis for social media posting."""
        content = f"""
{analysis.home_team} vs {analysis.away_team}: {analysis.final_prediction}

📊 SporTic365 요약
{analysis.summary}

✅ 경기 요약
{analysis.recent_h2h}

🎯 배당 인사이트
{analysis.betting_insight}

🔍 분석 포인트
{analysis.team_form_analysis}

💡 종합 판단 및 베팅 추천
{analysis.recommendation}

🎯 최종 추천: {analysis.final_prediction}
추천도: {'🔴' * min(4, int(analysis.confidence * 4))}{'⚪' * (4 - min(4, int(analysis.confidence * 4)))} ({analysis.confidence:.1f}/4)

{' '.join(analysis.hashtags)}
"""
        return content.strip()
