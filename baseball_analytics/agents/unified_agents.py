"""
Unified Multi-Sport Analysis Agents using actual Supabase database structure.

This module implements specialized agents for all sports based on actual data availability:

Baseball Agents (rich data from team_stats):
- BaseballTeamAgent: Team analysis using team_stats JSONB data
- BaseballPitcherAgent: Pitcher analysis using pitcher_profile/pitcher_stats

Other Sports Agents (WDL data only):
- SoccerAgent: Soccer analysis using WDL data from sportic_contents
- BasketballAgent: Basketball analysis using WDL data
- VolleyballAgent: Volleyball analysis using WDL data

Common Agents:
- H2HAgent: Head-to-head analysis for all sports
- HomeAwayAgent: Home/away form analysis for all sports

Each agent uses actual database fields and follows the tool pattern for modularity.
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from baseball_analytics.domain.models import SportType
from baseball_analytics.infrastructure.unified_database import (
    get_match_data, get_sportic_content, get_team_stats,
    get_wdl_data, get_content_for_match, get_betting_picks, get_sns_target_matches
)
from baseball_analytics.utils.logger import get_logger

logger = get_logger(__name__)


# ========== Analysis Result Models ==========

class SportAnalysisResult(BaseModel):
    """Base analysis result for all sports."""
    
    match_id: str = Field(..., description="경기 ID")
    sport: str = Field(..., description="스포츠 종목")
    home_team: str = Field(..., description="홈팀")
    away_team: str = Field(..., description="원정팀")
    match_date: str = Field(..., description="경기 날짜")
    
    summary: str = Field(..., description="경기 요약")
    key_factors: List[str] = Field(..., description="주요 승부 요인")
    prediction: str = Field(..., description="경기 예측")
    recommendation: str = Field(..., description="베팅 추천")
    confidence: float = Field(..., ge=0.0, le=1.0, description="신뢰도")
    hashtags: List[str] = Field(default_factory=list, description="해시태그")


class BaseballAnalysisResult(SportAnalysisResult):
    """Baseball-specific analysis result with rich team_stats data."""
    
    pitcher_analysis: str = Field(..., description="투수 분석")
    team_analysis: str = Field(..., description="팀 분석")
    h2h_analysis: str = Field(..., description="맞대결 분석")
    betting_analysis: str = Field(..., description="배당 분석")


class GeneralSportAnalysisResult(SportAnalysisResult):
    """General sport analysis result using WDL data only."""
    
    h2h_analysis: str = Field(..., description="맞대결 분석")
    home_form: str = Field(..., description="홈팀 폼")
    away_form: str = Field(..., description="원정팀 폼")
    betting_analysis: str = Field(..., description="배당 분석")


# ========== Data Collection Tools ==========

@tool
def get_match_basic_data(match_id: str) -> Dict[str, Any]:
    """Get basic match data from target_games table."""
    try:
        match_data = get_match_data(match_id)
        if not match_data:
            return {"error": f"Match not found: {match_id}"}
        return match_data
    except Exception as e:
        logger.error(f"Error getting match data: {e}")
        return {"error": str(e)}


@tool
def get_baseball_team_stats_data(match_id: str) -> List[Dict[str, Any]]:
    """Get baseball team statistics from team_stats table (JSONB fields)."""
    try:
        team_stats = get_team_stats(match_id)
        return team_stats if team_stats else []
    except Exception as e:
        logger.error(f"Error getting team stats: {e}")
        return []


@tool
def get_h2h_wdl_data(match_id: str) -> Dict[str, Any]:
    """Get head-to-head WDL data from sportic_contents."""
    try:
        h2h_summary = get_wdl_data(match_id, 'h2h', 'summary')
        h2h_matches = get_wdl_data(match_id, 'h2h', 'matches')
        return {
            "summary": h2h_summary,
            "matches": h2h_matches
        }
    except Exception as e:
        logger.error(f"Error getting H2H data: {e}")
        return {}


@tool
def get_home_away_wdl_data(match_id: str) -> Dict[str, Any]:
    """Get home/away WDL data from sportic_contents."""
    try:
        home_summary = get_wdl_data(match_id, 'home', 'summary')
        away_summary = get_wdl_data(match_id, 'away', 'summary')
        home_matches = get_wdl_data(match_id, 'home', 'matches')
        away_matches = get_wdl_data(match_id, 'away', 'matches')
        
        return {
            "home_summary": home_summary,
            "away_summary": away_summary,
            "home_matches": home_matches,
            "away_matches": away_matches
        }
    except Exception as e:
        logger.error(f"Error getting home/away data: {e}")
        return {}


@tool
def get_betting_data(match_id: str) -> Dict[str, Any]:
    """Get betting picks and odds data."""
    try:
        betting_picks = get_betting_picks(match_id)
        return betting_picks if betting_picks else {}
    except Exception as e:
        logger.error(f"Error getting betting data: {e}")
        return {}


@tool
def get_existing_content(match_id: str) -> str:
    """Get existing analysis content."""
    try:
        content = get_content_for_match(match_id)
        return content if content else "기존 분석 없음"
    except Exception as e:
        logger.error(f"Error getting existing content: {e}")
        return "기존 분석 없음"


# ========== Baseball Agents (Rich Data) ==========

class BaseballPitcherAgent:
    """Baseball pitcher analysis agent using pitcher_profile and pitcher_stats from team_stats."""
    
    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_baseball_team_stats_data, get_h2h_wdl_data]
        self.prompt = self._create_pitcher_prompt()
    
    def _create_pitcher_prompt(self) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 투수 전문 분석가입니다.
            team_stats 테이블의 pitcher_profile과 pitcher_stats JSONB 데이터를 활용하여 
            선발 투수들을 상세히 분석합니다.
            
            분석 요소:
            1. 투수 기본 정보 (이름, 나이, 포지션)
            2. 시즌 성적 (ERA, 승패, 이닝, 피안타, 볼넷, 삼진)
            3. 최근 폼 (최근 5경기 성적)
            4. 상대 타선과의 매치업
            5. 구장 특성 고려
            
            객관적이고 전문적인 투수 분석을 제공하세요.
            """),
            ("human", """
            다음 야구 경기의 투수진을 분석해주세요:
            
            경기 정보:
            {match_info}
            
            팀 통계 데이터 (team_stats):
            {team_stats_data}
            
            H2H 데이터:
            {h2h_data}
            
            투수 매치업을 중심으로 상세한 분석을 제공해주세요.
            """)
        ])
    
    def analyze(self, match_id: str) -> str:
        """Analyze pitchers for the match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            team_stats_data = get_baseball_team_stats_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})
            
            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "team_stats_data": self._format_team_stats(team_stats_data),
                "h2h_data": self._format_h2h_data(h2h_data)
            }
            
            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"Error in pitcher analysis: {e}")
            return f"투수 분석 중 오류가 발생했습니다: {e}"
    
    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"
        
        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """
    
    def _format_team_stats(self, team_stats_data: List[Dict[str, Any]]) -> str:
        if not team_stats_data:
            return "팀 통계 데이터가 없습니다."
        
        formatted = "팀별 투수 정보:\n"
        for stats in team_stats_data:
            team_name = stats.get('team_name', 'Unknown')
            formatted += f"\n{team_name}:\n"
            
            # Pitcher profile
            pitcher_profile = stats.get('pitcher_profile', {})
            if pitcher_profile:
                formatted += f"  투수 프로필: {pitcher_profile}\n"
            
            # Pitcher stats
            pitcher_stats = stats.get('pitcher_stats', {})
            if pitcher_stats:
                formatted += f"  투수 통계: {pitcher_stats}\n"
        
        return formatted
    
    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        if not h2h_data:
            return "H2H 데이터가 없습니다."
        
        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기: {h2h_data['matches']}\n"
        
        return formatted


class BaseballTeamAgent:
    """Baseball team analysis agent using season_stats and recent_matches from team_stats."""
    
    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_baseball_team_stats_data, get_home_away_wdl_data]
        self.prompt = self._create_team_prompt()
    
    def _create_team_prompt(self) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 팀 분석 전문가입니다.
            team_stats 테이블의 season_stats와 recent_matches JSONB 데이터를 활용하여
            팀의 전반적인 성적과 폼을 분석합니다.
            
            분석 요소:
            1. 시즌 성적 (승률, 득점, 실점, 타율, 팀 ERA)
            2. 최근 폼 (최근 10경기 성적)
            3. 홈/원정 성적 차이
            4. 주요 타자들의 컨디션
            5. 불펜 상황
            
            데이터 기반의 객관적인 팀 분석을 제공하세요.
            """),
            ("human", """
            다음 야구 경기의 팀들을 분석해주세요:
            
            경기 정보:
            {match_info}
            
            팀 통계 데이터:
            {team_stats_data}
            
            홈/원정 폼 데이터:
            {home_away_data}
            
            양 팀의 전력과 최근 폼을 비교 분석해주세요.
            """)
        ])
    
    def analyze(self, match_id: str) -> str:
        """Analyze teams for the match."""
        try:
            # Collect data using tools
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            team_stats_data = get_baseball_team_stats_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})
            
            # Format data for prompt
            input_data = {
                "match_info": self._format_match_info(match_data),
                "team_stats_data": self._format_team_stats(team_stats_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }
            
            # Generate analysis
            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"Error in team analysis: {e}")
            return f"팀 분석 중 오류가 발생했습니다: {e}"
    
    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"
        
        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        """
    
    def _format_team_stats(self, team_stats_data: List[Dict[str, Any]]) -> str:
        if not team_stats_data:
            return "팀 통계 데이터가 없습니다."
        
        formatted = "팀별 상세 통계:\n"
        for stats in team_stats_data:
            team_name = stats.get('team_name', 'Unknown')
            formatted += f"\n{team_name}:\n"
            
            # Season stats
            season_stats = stats.get('season_stats', {})
            if season_stats:
                formatted += f"  시즌 통계: {season_stats}\n"
            
            # Recent matches
            recent_matches = stats.get('recent_matches', {})
            if recent_matches:
                formatted += f"  최근 경기: {recent_matches}\n"
            
            # Season summary
            season_summary = stats.get('season_summary', {})
            if season_summary:
                formatted += f"  시즌 요약: {season_summary}\n"
        
        return formatted
    
    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."
        
        formatted = "홈/원정 폼:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 폼: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 폼: {home_away_data['away_summary']}\n"
        
        return formatted


# ========== Common Agents (All Sports) ==========

class H2HAgent:
    """Head-to-head analysis agent for all sports using WDL data."""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_h2h_wdl_data]
        self.prompt = self._create_h2h_prompt()

    def _create_h2h_prompt(self) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 스포츠 맞대결 분석 전문가입니다.
            sportic_contents 테이블의 h2h_wdl_summary와 h2h_wdl_matches 데이터를 활용하여
            두 팀 간의 과거 전적과 패턴을 분석합니다.

            분석 요소:
            1. 전체 맞대결 승률
            2. 최근 맞대결 트렌드
            3. 홈/원정별 맞대결 성적
            4. 득점/실점 패턴
            5. 특이사항 및 주목할 점

            객관적이고 통계적인 맞대결 분석을 제공하세요.
            """),
            ("human", """
            다음 경기의 맞대결 기록을 분석해주세요:

            경기 정보:
            {match_info}

            H2H 데이터:
            {h2h_data}

            과거 전적을 바탕으로 이번 경기의 전망을 제시해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze head-to-head record."""
        try:
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})

            input_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_data": self._format_h2h_data(h2h_data)
            }

            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in H2H analysis: {e}")
            return f"맞대결 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        스포츠: {match_data.get('sports', 'Unknown')}
        """

    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        if not h2h_data:
            return "H2H 데이터가 없습니다."

        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기들: {h2h_data['matches']}\n"

        return formatted


class HomeAwayAgent:
    """Home/away form analysis agent for all sports."""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.tools = [get_home_away_wdl_data]
        self.prompt = self._create_home_away_prompt()

    def _create_home_away_prompt(self) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 스포츠 홈/원정 폼 분석 전문가입니다.
            sportic_contents 테이블의 home_wdl_summary, away_wdl_summary 데이터를 활용하여
            각 팀의 홈/원정 성적과 최근 폼을 분석합니다.

            분석 요소:
            1. 홈팀의 홈구장 성적
            2. 원정팀의 원정 성적
            3. 최근 폼 비교
            4. 홈 어드밴티지 정도
            5. 컨디션 및 트렌드

            홈/원정 특성을 고려한 분석을 제공하세요.
            """),
            ("human", """
            다음 경기의 홈/원정 폼을 분석해주세요:

            경기 정보:
            {match_info}

            홈/원정 데이터:
            {home_away_data}

            홈 어드밴티지와 최근 폼을 종합하여 분석해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze home/away form."""
        try:
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})

            input_data = {
                "match_info": self._format_match_info(match_data),
                "home_away_data": self._format_home_away_data(home_away_data)
            }

            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in home/away analysis: {e}")
            return f"홈/원정 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        스포츠: {match_data.get('sports', 'Unknown')}
        """

    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."

        formatted = "홈/원정 성적:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 홈 성적: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 원정 성적: {home_away_data['away_summary']}\n"
        if home_away_data.get('home_matches'):
            formatted += f"홈팀 최근 경기: {home_away_data['home_matches']}\n"
        if home_away_data.get('away_matches'):
            formatted += f"원정팀 최근 경기: {home_away_data['away_matches']}\n"

        return formatted


# ========== Other Sports Agents (WDL Data Only) ==========

class GeneralSportAgent:
    """General sport analysis agent for soccer, basketball, volleyball using WDL data only."""

    def __init__(self, llm: BaseChatModel, sport_name: str):
        self.llm = llm
        self.sport_name = sport_name
        self.tools = [get_h2h_wdl_data, get_home_away_wdl_data, get_betting_data]
        self.prompt = self._create_sport_prompt()

    def _create_sport_prompt(self) -> ChatPromptTemplate:
        sport_specific_prompts = {
            "soccer": """
            당신은 한국 프로축구(K리그) 전문 분석가입니다.
            WDL 데이터를 중심으로 축구 경기를 분석합니다.

            분석 요소:
            1. 최근 폼 (승점, 승률, 득실점)
            2. 홈/원정 성적 차이
            3. 맞대결 기록 및 패턴
            4. 전술적 특징 (가능한 범위에서)
            5. 부상자 및 출전 정보
            """,
            "basketball": """
            당신은 한국 프로농구(KBL) 전문 분석가입니다.
            WDL 데이터를 중심으로 농구 경기를 분석합니다.

            분석 요소:
            1. 최근 폼 (승률, 득점력, 수비력)
            2. 홈/원정 성적 차이
            3. 맞대결 기록 및 패턴
            4. 공격/수비 스타일 분석
            5. 주요 선수 컨디션
            """,
            "volleyball": """
            당신은 한국 프로배구(V리그) 전문 분석가입니다.
            WDL 데이터를 중심으로 배구 경기를 분석합니다.

            분석 요소:
            1. 최근 폼 (승률, 세트 득실)
            2. 홈/원정 성적 차이
            3. 맞대결 기록 및 패턴
            4. 공격/블로킹 스타일 분석
            5. 주요 선수 컨디션
            """
        }

        system_prompt = sport_specific_prompts.get(self.sport_name, sport_specific_prompts["soccer"])

        return ChatPromptTemplate.from_messages([
            ("system", system_prompt + "\n\n데이터 기반의 객관적인 분석을 제공하세요."),
            ("human", """
            다음 {sport_name} 경기를 분석해주세요:

            경기 정보:
            {match_info}

            H2H 데이터:
            {h2h_data}

            홈/원정 데이터:
            {home_away_data}

            베팅 데이터:
            {betting_data}

            종합적인 경기 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> str:
        """Analyze match for general sports."""
        try:
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            h2h_data = get_h2h_wdl_data.invoke({"match_id": match_id})
            home_away_data = get_home_away_wdl_data.invoke({"match_id": match_id})
            betting_data = get_betting_data.invoke({"match_id": match_id})

            input_data = {
                "sport_name": self.sport_name,
                "match_info": self._format_match_info(match_data),
                "h2h_data": self._format_h2h_data(h2h_data),
                "home_away_data": self._format_home_away_data(home_away_data),
                "betting_data": self._format_betting_data(betting_data)
            }

            response = self.llm.invoke(self.prompt.format(**input_data))
            return str(response.content) if hasattr(response, 'content') else str(response)

        except Exception as e:
            logger.error(f"Error in {self.sport_name} analysis: {e}")
            return f"{self.sport_name} 분석 중 오류가 발생했습니다: {e}"

    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        if "error" in match_data:
            return f"경기 정보 오류: {match_data['error']}"

        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        스포츠: {match_data.get('sports', 'Unknown')}
        """

    def _format_h2h_data(self, h2h_data: Dict[str, Any]) -> str:
        if not h2h_data:
            return "H2H 데이터가 없습니다."

        formatted = "맞대결 기록:\n"
        if h2h_data.get('summary'):
            formatted += f"요약: {h2h_data['summary']}\n"
        if h2h_data.get('matches'):
            formatted += f"최근 경기들: {h2h_data['matches']}\n"

        return formatted

    def _format_home_away_data(self, home_away_data: Dict[str, Any]) -> str:
        if not home_away_data:
            return "홈/원정 데이터가 없습니다."

        formatted = "홈/원정 성적:\n"
        if home_away_data.get('home_summary'):
            formatted += f"홈팀 성적: {home_away_data['home_summary']}\n"
        if home_away_data.get('away_summary'):
            formatted += f"원정팀 성적: {home_away_data['away_summary']}\n"

        return formatted

    def _format_betting_data(self, betting_data: Dict[str, Any]) -> str:
        if not betting_data:
            return "베팅 데이터가 없습니다."

        formatted = "베팅 정보:\n"
        for key, value in betting_data.items():
            if key not in ['id', 'created_at', 'updated_at']:
                formatted += f"{key}: {value}\n"

        return formatted


# ========== Unified Analysis Service ==========

class UnifiedAnalysisService:
    """
    Unified analysis service that automatically selects appropriate agents
    based on sport type and available data.
    """

    def __init__(self, llm: BaseChatModel):
        self.llm = llm

        # Initialize baseball agents (rich data)
        self.baseball_pitcher_agent = BaseballPitcherAgent(llm)
        self.baseball_team_agent = BaseballTeamAgent(llm)

        # Initialize common agents
        self.h2h_agent = H2HAgent(llm)
        self.home_away_agent = HomeAwayAgent(llm)

        # Initialize other sport agents
        self.soccer_agent = GeneralSportAgent(llm, "soccer")
        self.basketball_agent = GeneralSportAgent(llm, "basketball")
        self.volleyball_agent = GeneralSportAgent(llm, "volleyball")

    def analyze_match(self, match_id: str) -> Dict[str, Any]:
        """
        Analyze a match using appropriate agents based on sport type.

        Returns comprehensive analysis with all relevant agent outputs.
        """
        try:
            # Get basic match data to determine sport
            match_data = get_match_basic_data.invoke({"match_id": match_id})
            if "error" in match_data:
                return {"error": match_data["error"]}

            sport = match_data.get('sports', '').lower()

            # Common analysis for all sports
            h2h_analysis = self.h2h_agent.analyze(match_id)
            home_away_analysis = self.home_away_agent.analyze(match_id)

            result = {
                "match_id": match_id,
                "sport": sport,
                "home_team": match_data.get('home_team'),
                "away_team": match_data.get('away_team'),
                "match_date": match_data.get('match_date'),
                "h2h_analysis": h2h_analysis,
                "home_away_analysis": home_away_analysis
            }

            # Sport-specific analysis
            if sport in ['baseball', '야구', 'bseball']:
                # Baseball: Use rich team_stats data
                result["pitcher_analysis"] = self.baseball_pitcher_agent.analyze(match_id)
                result["team_analysis"] = self.baseball_team_agent.analyze(match_id)
                result["analysis_type"] = "baseball_rich"

            elif sport in ['soccer', 'football', '축구']:
                # Soccer: Use WDL data only
                result["sport_analysis"] = self.soccer_agent.analyze(match_id)
                result["analysis_type"] = "soccer_wdl"

            elif sport in ['basketball', '농구']:
                # Basketball: Use WDL data only
                result["sport_analysis"] = self.basketball_agent.analyze(match_id)
                result["analysis_type"] = "basketball_wdl"

            elif sport in ['volleyball', '배구']:
                # Volleyball: Use WDL data only
                result["sport_analysis"] = self.volleyball_agent.analyze(match_id)
                result["analysis_type"] = "volleyball_wdl"

            else:
                # Unknown sport: Use general analysis
                result["sport_analysis"] = self.soccer_agent.analyze(match_id)
                result["analysis_type"] = "general_wdl"

            return result

        except Exception as e:
            logger.error(f"Error in unified analysis: {e}")
            return {"error": f"분석 중 오류가 발생했습니다: {e}"}

    def get_target_matches(self) -> List[Dict[str, Any]]:
        """Get all target matches for analysis."""
        try:
            return get_sns_target_matches()
        except Exception as e:
            logger.error(f"Error getting target matches: {e}")
            return []

    def analyze_all_target_matches(self) -> List[Dict[str, Any]]:
        """Analyze all target matches."""
        target_matches = self.get_target_matches()
        results = []

        for match_data in target_matches:
            try:
                match_id = match_data.get("match_id")
                if match_id:
                    analysis = self.analyze_match(match_id)
                    results.append(analysis)
            except Exception as e:
                logger.error(f"Error analyzing match {match_data.get('match_id', 'unknown')}: {e}")
                continue

        return results
