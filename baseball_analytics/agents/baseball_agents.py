"""
Specialized Baseball Analysis Agents using latest LangChain features.

This module implements individual agents for pitcher analysis, team performance,
and betting odds analysis using structured output and modern LangChain patterns.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field

from baseball_analytics.domain.models import Match, TeamStats, AnalysisInsight
from baseball_analytics.infrastructure.enhanced_database import BaseballAnalysisRepository

logger = logging.getLogger(__name__)


# Structured Output Models for Agents
class PitcherAnalysis(BaseModel):
    """Structured output for pitcher analysis."""
    
    pitcher_name: str = Field(..., description="투수 이름")
    era: float = Field(..., description="평균자책점")
    recent_form: str = Field(..., description="최근 폼 상태 (좋음/보통/나쁨)")
    recent_innings: float = Field(..., description="최근 이닝수")
    recent_earned_runs: int = Field(..., description="최근 자책점")
    season_record: Dict[str, int] = Field(..., description="시즌 승패 기록")
    analysis_summary: str = Field(..., description="투수 분석 요약")
    confidence: float = Field(..., ge=0.0, le=1.0, description="분석 신뢰도")


class TeamPerformanceAnalysis(BaseModel):
    """Structured output for team performance analysis."""
    
    team_name: str = Field(..., description="팀 이름")
    recent_form: str = Field(..., description="최근 폼 (상승/하락/유지)")
    home_away_advantage: str = Field(..., description="홈/원정 우위")
    avg_runs_scored: float = Field(..., description="평균 득점")
    avg_runs_conceded: float = Field(..., description="평균 실점")
    batting_strength: str = Field(..., description="타격 강점")
    pitching_strength: str = Field(..., description="투수 강점")
    key_insights: List[str] = Field(..., description="주요 인사이트")
    confidence: float = Field(..., ge=0.0, le=1.0, description="분석 신뢰도")


class BettingOddsAnalysis(BaseModel):
    """Structured output for betting odds analysis."""
    
    home_odds: float = Field(..., description="홈팀 배당률")
    away_odds: float = Field(..., description="원정팀 배당률")
    home_win_probability: float = Field(..., ge=0.0, le=1.0, description="홈팀 승률")
    away_win_probability: float = Field(..., ge=0.0, le=1.0, description="원정팀 승률")
    market_sentiment: str = Field(..., description="시장 심리 (홈팀 선호/원정팀 선호/균등)")
    value_assessment: str = Field(..., description="가치 평가 (과대평가/적정/과소평가)")
    recommended_bet: str = Field(..., description="추천 베팅")
    confidence: float = Field(..., ge=0.0, le=1.0, description="분석 신뢰도")


class ComprehensiveMatchAnalysis(BaseModel):
    """Comprehensive match analysis combining all agents."""
    
    match_id: str = Field(..., description="경기 ID")
    home_team: str = Field(..., description="홈팀")
    away_team: str = Field(..., description="원정팀")
    match_date: str = Field(..., description="경기 날짜")
    
    pitcher_analysis: Dict[str, PitcherAnalysis] = Field(..., description="투수 분석")
    team_analysis: Dict[str, TeamPerformanceAnalysis] = Field(..., description="팀 분석")
    betting_analysis: BettingOddsAnalysis = Field(..., description="배당 분석")
    
    key_factors: List[str] = Field(..., description="주요 승부 요인")
    prediction: str = Field(..., description="경기 예측")
    recommendation: str = Field(..., description="베팅 추천")
    overall_confidence: float = Field(..., ge=0.0, le=1.0, description="전체 신뢰도")


class PitcherAnalysisAgent:
    """
    Specialized agent for pitcher analysis.
    
    Analyzes starting pitchers' recent form, statistics, and performance trends.
    """
    
    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.prompt_template = self._create_pitcher_prompt()
    
    def _create_pitcher_prompt(self) -> ChatPromptTemplate:
        """Create prompt template for pitcher analysis."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 전문 투수 분석가입니다.
            투수의 최근 폼, 통계, 상대 타선과의 상성을 종합적으로 분석합니다.
            
            분석 기준:
            1. 최근 3경기 투구 내용 (이닝, 자책점, 피안타, 볼넷 등)
            2. 시즌 전체 통계 (ERA, WHIP, 승패 기록)
            3. 상대팀과의 과거 대전 성적
            4. 컨디션 및 부상 여부
            5. 홈/원정 구장 특성
            
            객관적이고 데이터 기반의 분석을 제공하세요.
            """),
            ("human", """
            다음 투수 데이터를 분석해주세요:
            
            투수 정보:
            {pitcher_data}
            
            상대팀 타격 데이터:
            {opponent_batting_data}
            
            과거 대전 기록:
            {h2h_pitching_data}
            
            구장 정보:
            {venue_info}
            
            투수의 현재 상태와 오늘 경기에서의 예상 성과를 분석해주세요.
            """)
        ])
    
    def analyze_pitcher(
        self, 
        pitcher_data: Dict[str, Any],
        opponent_batting_data: Dict[str, Any],
        h2h_data: Dict[str, Any],
        venue_info: Dict[str, Any]
    ) -> PitcherAnalysis:
        """Analyze pitcher performance and form."""
        try:
            # Create structured output chain
            chain = self.prompt_template | self.llm.with_structured_output(PitcherAnalysis)
            
            # Prepare input data
            input_data = {
                "pitcher_data": self._format_pitcher_data(pitcher_data),
                "opponent_batting_data": self._format_batting_data(opponent_batting_data),
                "h2h_pitching_data": self._format_h2h_data(h2h_data),
                "venue_info": self._format_venue_info(venue_info)
            }
            
            # Execute analysis
            result = chain.invoke(input_data)
            
            logger.info(f"Pitcher analysis completed for {result.pitcher_name}")
            return result
            
        except Exception as e:
            logger.error(f"Error in pitcher analysis: {e}")
            # Return fallback analysis
            return self._create_fallback_pitcher_analysis(pitcher_data)
    
    def _format_pitcher_data(self, data: Dict[str, Any]) -> str:
        """Format pitcher data for prompt."""
        return f"""
        이름: {data.get('name', 'Unknown')}
        ERA: {data.get('era', 'N/A')}
        최근 이닝: {data.get('recent_innings', 'N/A')}
        최근 자책점: {data.get('recent_earned_runs', 'N/A')}
        시즌 승패: {data.get('season_record', {})}
        """
    
    def _format_batting_data(self, data: Dict[str, Any]) -> str:
        """Format opponent batting data for prompt."""
        return f"""
        팀 타율: {data.get('team_avg', 'N/A')}
        홈런: {data.get('home_runs', 'N/A')}
        득점: {data.get('runs_scored', 'N/A')}
        """
    
    def _format_h2h_data(self, data: Dict[str, Any]) -> str:
        """Format head-to-head data for prompt."""
        return f"""
        과거 대전 성적: {data.get('record', 'N/A')}
        평균 실점: {data.get('avg_runs_allowed', 'N/A')}
        """
    
    def _format_venue_info(self, data: Dict[str, Any]) -> str:
        """Format venue information for prompt."""
        return f"""
        구장: {data.get('name', 'N/A')}
        투수 유리도: {data.get('pitcher_friendly', 'N/A')}
        """
    
    def _create_fallback_pitcher_analysis(self, pitcher_data: Dict[str, Any]) -> PitcherAnalysis:
        """Create fallback analysis when LLM fails."""
        return PitcherAnalysis(
            pitcher_name=pitcher_data.get('name', 'Unknown'),
            era=pitcher_data.get('era', 4.50),
            recent_form="보통",
            recent_innings=pitcher_data.get('recent_innings', 15.0),
            recent_earned_runs=pitcher_data.get('recent_earned_runs', 7),
            season_record=pitcher_data.get('season_record', {"wins": 0, "losses": 0}),
            analysis_summary="데이터 부족으로 기본 분석만 제공됩니다.",
            confidence=0.3
        )


class TeamPerformanceAgent:
    """
    Specialized agent for team performance analysis.
    
    Analyzes team's recent form, batting/pitching strength, and home/away performance.
    """
    
    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.prompt_template = self._create_team_prompt()
    
    def _create_team_prompt(self) -> ChatPromptTemplate:
        """Create prompt template for team analysis."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 전문 팀 분석가입니다.
            팀의 최근 성적, 타격력, 투수력, 홈/원정 성과를 종합 분석합니다.
            
            분석 요소:
            1. 최근 5경기 성적과 트렌드
            2. 홈/원정 성적 차이
            3. 타격 지표 (타율, 홈런, 득점력)
            4. 투수 지표 (ERA, 실점, 불펜 안정성)
            5. 부상자 현황 및 라인업 변화
            6. 상대팀과의 상성
            
            데이터를 바탕으로 객관적인 분석을 제공하세요.
            """),
            ("human", """
            다음 팀 데이터를 분석해주세요:
            
            팀 기본 정보:
            {team_info}
            
            최근 성적:
            {recent_performance}
            
            홈/원정 성적:
            {home_away_stats}
            
            타격 통계:
            {batting_stats}
            
            투수 통계:
            {pitching_stats}
            
            팀의 현재 상태와 강점/약점을 분석해주세요.
            """)
        ])
    
    def analyze_team(
        self,
        team_info: Dict[str, Any],
        recent_performance: Dict[str, Any],
        home_away_stats: Dict[str, Any],
        batting_stats: Dict[str, Any],
        pitching_stats: Dict[str, Any]
    ) -> TeamPerformanceAnalysis:
        """Analyze team performance comprehensively."""
        try:
            # Create structured output chain
            chain = self.prompt_template | self.llm.with_structured_output(TeamPerformanceAnalysis)
            
            # Prepare input data
            input_data = {
                "team_info": self._format_team_info(team_info),
                "recent_performance": self._format_recent_performance(recent_performance),
                "home_away_stats": self._format_home_away_stats(home_away_stats),
                "batting_stats": self._format_batting_stats(batting_stats),
                "pitching_stats": self._format_pitching_stats(pitching_stats)
            }
            
            # Execute analysis
            result = chain.invoke(input_data)
            
            logger.info(f"Team analysis completed for {result.team_name}")
            return result
            
        except Exception as e:
            logger.error(f"Error in team analysis: {e}")
            # Return fallback analysis
            return self._create_fallback_team_analysis(team_info)
    
    def _format_team_info(self, data: Dict[str, Any]) -> str:
        """Format team information for prompt."""
        return f"""
        팀명: {data.get('name', 'Unknown')}
        홈구장: {data.get('home_venue', 'N/A')}
        """
    
    def _format_recent_performance(self, data: Dict[str, Any]) -> str:
        """Format recent performance data for prompt."""
        return f"""
        최근 5경기: {data.get('wins', 0)}승 {data.get('losses', 0)}패
        평균 득점: {data.get('avg_runs_scored', 'N/A')}
        평균 실점: {data.get('avg_runs_conceded', 'N/A')}
        """
    
    def _format_home_away_stats(self, data: Dict[str, Any]) -> str:
        """Format home/away statistics for prompt."""
        return f"""
        홈 성적: {data.get('home_record', 'N/A')}
        원정 성적: {data.get('away_record', 'N/A')}
        """
    
    def _format_batting_stats(self, data: Dict[str, Any]) -> str:
        """Format batting statistics for prompt."""
        return f"""
        팀 타율: {data.get('team_avg', 'N/A')}
        홈런: {data.get('home_runs', 'N/A')}
        타점: {data.get('rbi', 'N/A')}
        """
    
    def _format_pitching_stats(self, data: Dict[str, Any]) -> str:
        """Format pitching statistics for prompt."""
        return f"""
        팀 ERA: {data.get('team_era', 'N/A')}
        WHIP: {data.get('whip', 'N/A')}
        세이브: {data.get('saves', 'N/A')}
        """
    
    def _create_fallback_team_analysis(self, team_info: Dict[str, Any]) -> TeamPerformanceAnalysis:
        """Create fallback analysis when LLM fails."""
        return TeamPerformanceAnalysis(
            team_name=team_info.get('name', 'Unknown'),
            recent_form="유지",
            home_away_advantage="균등",
            avg_runs_scored=4.0,
            avg_runs_conceded=4.0,
            batting_strength="보통",
            pitching_strength="보통",
            key_insights=["데이터 부족으로 기본 분석만 제공됩니다."],
            confidence=0.3
        )


class BettingOddsAgent:
    """
    Specialized agent for betting odds analysis.

    Analyzes betting odds, market sentiment, and value opportunities.
    """

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.prompt_template = self._create_betting_prompt()

    def _create_betting_prompt(self) -> ChatPromptTemplate:
        """Create prompt template for betting analysis."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 전문 베팅 분석가입니다.
            배당률, 시장 심리, 가치 베팅 기회를 분석합니다.

            분석 요소:
            1. 현재 배당률과 내재 확률
            2. 시장의 편향성 (홈팀/원정팀 선호도)
            3. 실제 경기력 대비 배당률의 적정성
            4. 과거 유사한 상황에서의 결과
            5. 대중의 베팅 패턴과 전문가 의견

            객관적이고 수학적 근거를 바탕으로 분석하세요.
            """),
            ("human", """
            다음 베팅 데이터를 분석해주세요:

            배당률 정보:
            {odds_data}

            팀 실력 비교:
            {team_comparison}

            시장 데이터:
            {market_data}

            과거 유사 상황:
            {historical_odds}

            배당률의 적정성과 베팅 가치를 분석해주세요.
            """)
        ])

    def analyze_betting_odds(
        self,
        odds_data: Dict[str, Any],
        team_comparison: Dict[str, Any],
        market_data: Dict[str, Any],
        historical_data: Dict[str, Any]
    ) -> BettingOddsAnalysis:
        """Analyze betting odds and market sentiment."""
        try:
            # Create structured output chain
            chain = self.prompt_template | self.llm.with_structured_output(BettingOddsAnalysis)

            # Prepare input data
            input_data = {
                "odds_data": self._format_odds_data(odds_data),
                "team_comparison": self._format_team_comparison(team_comparison),
                "market_data": self._format_market_data(market_data),
                "historical_odds": self._format_historical_data(historical_data)
            }

            # Execute analysis
            result = chain.invoke(input_data)

            logger.info("Betting odds analysis completed")
            return result

        except Exception as e:
            logger.error(f"Error in betting odds analysis: {e}")
            # Return fallback analysis
            return self._create_fallback_betting_analysis(odds_data)

    def _format_odds_data(self, data: Dict[str, Any]) -> str:
        """Format odds data for prompt."""
        return f"""
        홈팀 배당: {data.get('home_odds', 'N/A')}
        원정팀 배당: {data.get('away_odds', 'N/A')}
        내재 확률 - 홈팀: {data.get('home_probability', 'N/A')}%
        내재 확률 - 원정팀: {data.get('away_probability', 'N/A')}%
        """

    def _format_team_comparison(self, data: Dict[str, Any]) -> str:
        """Format team comparison data for prompt."""
        return f"""
        홈팀 최근 폼: {data.get('home_form', 'N/A')}
        원정팀 최근 폼: {data.get('away_form', 'N/A')}
        실력 차이: {data.get('skill_gap', 'N/A')}
        """

    def _format_market_data(self, data: Dict[str, Any]) -> str:
        """Format market data for prompt."""
        return f"""
        베팅량 분포: {data.get('betting_distribution', 'N/A')}
        대중 선호도: {data.get('public_preference', 'N/A')}
        """

    def _format_historical_data(self, data: Dict[str, Any]) -> str:
        """Format historical odds data for prompt."""
        return f"""
        유사 상황 결과: {data.get('similar_situations', 'N/A')}
        과거 배당 정확도: {data.get('odds_accuracy', 'N/A')}
        """

    def _create_fallback_betting_analysis(self, odds_data: Dict[str, Any]) -> BettingOddsAnalysis:
        """Create fallback analysis when LLM fails."""
        home_odds = odds_data.get('home_odds', 2.0)
        away_odds = odds_data.get('away_odds', 1.8)

        return BettingOddsAnalysis(
            home_odds=home_odds,
            away_odds=away_odds,
            home_win_probability=1.0 / home_odds if home_odds > 0 else 0.5,
            away_win_probability=1.0 / away_odds if away_odds > 0 else 0.5,
            market_sentiment="균등",
            value_assessment="적정",
            recommended_bet="데이터 부족으로 추천 보류",
            confidence=0.3
        )


class BaseballAnalysisOrchestrator:
    """
    Orchestrates all baseball analysis agents to provide comprehensive match analysis.

    This class coordinates pitcher, team, and betting analysis agents to create
    a complete picture of the upcoming match.
    """

    def __init__(
        self,
        llm: BaseChatModel,
        repository: BaseballAnalysisRepository
    ):
        self.repository = repository

        # Initialize specialized agents
        self.pitcher_agent = PitcherAnalysisAgent(llm)
        self.team_agent = TeamPerformanceAgent(llm)
        self.betting_agent = BettingOddsAgent(llm)

        # Create comprehensive analysis prompt
        self.comprehensive_prompt = self._create_comprehensive_prompt()
        self.llm = llm

    def _create_comprehensive_prompt(self) -> ChatPromptTemplate:
        """Create prompt for comprehensive analysis."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 최고 전문 분석가입니다.
            투수, 팀, 배당 분석을 종합하여 최종 경기 예측과 베팅 추천을 제공합니다.

            종합 분석 기준:
            1. 투수 매치업의 중요도 (40%)
            2. 팀 전력 및 최근 폼 (35%)
            3. 배당률과 시장 가치 (25%)

            신뢰할 수 있고 실용적인 분석을 제공하세요.
            """),
            ("human", """
            다음 개별 분석 결과를 종합하여 최종 분석을 제공해주세요:

            투수 분석:
            {pitcher_analysis}

            팀 분석:
            {team_analysis}

            배당 분석:
            {betting_analysis}

            경기 기본 정보:
            {match_info}

            종합적인 경기 예측과 베팅 추천을 제공해주세요.
            """)
        ])

    def analyze_match_comprehensive(self, match_id: str) -> ComprehensiveMatchAnalysis:
        """
        Perform comprehensive match analysis using all agents.

        Args:
            match_id: The match identifier

        Returns:
            ComprehensiveMatchAnalysis: Complete analysis result
        """
        try:
            logger.info(f"Starting comprehensive analysis for match: {match_id}")

            # Get match data
            match = self.repository.get_match_by_id(match_id)
            if not match:
                raise ValueError(f"Match not found: {match_id}")

            # Collect data for analysis
            pitcher_data = self.repository.get_pitcher_stats(match_id)
            team_stats = self.repository.get_team_stats(match_id)
            betting_odds = self.repository.get_betting_odds(match_id)
            h2h_data = self.repository.get_head_to_head_record(
                match.home_team.id, match.away_team.id
            )

            # Run individual agent analyses
            pitcher_analysis = self._analyze_pitchers(pitcher_data, h2h_data)
            team_analysis = self._analyze_teams(match, team_stats, h2h_data)
            betting_analysis = self._analyze_betting_odds(betting_odds, team_analysis)

            # Create comprehensive analysis
            comprehensive_analysis = self._create_comprehensive_analysis(
                match, pitcher_analysis, team_analysis, betting_analysis
            )

            logger.info(f"Comprehensive analysis completed for match: {match_id}")
            return comprehensive_analysis

        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {e}")
            raise

    def _analyze_pitchers(
        self,
        pitcher_data: Dict[str, Any],
        h2h_data: Dict[str, Any]
    ) -> Dict[str, PitcherAnalysis]:
        """Analyze both starting pitchers."""
        pitcher_analysis = {}

        # Analyze home pitcher
        if "home_pitcher" in pitcher_data:
            pitcher_analysis["home"] = self.pitcher_agent.analyze_pitcher(
                pitcher_data["home_pitcher"],
                {},  # opponent batting data
                h2h_data,
                {}   # venue info
            )

        # Analyze away pitcher
        if "away_pitcher" in pitcher_data:
            pitcher_analysis["away"] = self.pitcher_agent.analyze_pitcher(
                pitcher_data["away_pitcher"],
                {},  # opponent batting data
                h2h_data,
                {}   # venue info
            )

        return pitcher_analysis
