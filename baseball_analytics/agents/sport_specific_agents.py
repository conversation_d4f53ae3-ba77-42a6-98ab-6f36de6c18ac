"""
Sport-Specific Analysis Agents based on actual database structure.

This module implements different agents for different sports based on 
how data is actually stored and accessed in the Supabase database:

- Baseball: Uses team_stats table + WDL data
- Soccer/Basketball/Volleyball: Uses WDL data only

Each sport has its own analysis patterns and data requirements.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository
from baseball_analytics.domain.models import SportType

logger = logging.getLogger(__name__)


class SportAnalysisResult(BaseModel):
    """Base analysis result for all sports."""
    
    match_id: str = Field(..., description="경기 ID")
    sport: str = Field(..., description="스포츠 종목")
    home_team: str = Field(..., description="홈팀")
    away_team: str = Field(..., description="원정팀")
    match_date: str = Field(..., description="경기 날짜")
    
    summary: str = Field(..., description="경기 요약")
    key_factors: List[str] = Field(..., description="주요 승부 요인")
    prediction: str = Field(..., description="경기 예측")
    recommendation: str = Field(..., description="베팅 추천")
    confidence: float = Field(..., ge=0.0, le=1.0, description="신뢰도")
    hashtags: List[str] = Field(default_factory=list, description="해시태그")


class BaseballAnalysisResult(SportAnalysisResult):
    """Baseball-specific analysis result."""
    
    pitcher_analysis: str = Field(..., description="투수 분석")
    batting_analysis: str = Field(..., description="타격 분석")
    recent_form: str = Field(..., description="최근 폼 분석")
    h2h_analysis: str = Field(..., description="맞대결 분석")


class SoccerAnalysisResult(SportAnalysisResult):
    """Soccer-specific analysis result."""
    
    formation_analysis: str = Field(..., description="전술 분석")
    recent_form: str = Field(..., description="최근 폼 분석")
    h2h_analysis: str = Field(..., description="맞대결 분석")
    injury_report: str = Field(..., description="부상자 현황")


class BasketballAnalysisResult(SportAnalysisResult):
    """Basketball-specific analysis result."""
    
    offense_analysis: str = Field(..., description="공격 분석")
    defense_analysis: str = Field(..., description="수비 분석")
    recent_form: str = Field(..., description="최근 폼 분석")
    h2h_analysis: str = Field(..., description="맞대결 분석")


class VolleyballAnalysisResult(SportAnalysisResult):
    """Volleyball-specific analysis result."""
    
    attack_analysis: str = Field(..., description="공격 분석")
    block_analysis: str = Field(..., description="블로킹 분석")
    recent_form: str = Field(..., description="최근 폼 분석")
    h2h_analysis: str = Field(..., description="맞대결 분석")


class BaseballAgent:
    """
    Baseball analysis agent using team_stats table and WDL data.
    
    Baseball has the richest data available with detailed team statistics
    including batting averages, ERAs, runs, hits, errors, etc.
    """
    
    def __init__(self, llm: BaseChatModel, repository: RealSupabaseRepository):
        self.llm = llm
        self.repository = repository
        self.prompt_template = self._create_baseball_prompt()
    
    def _create_baseball_prompt(self) -> ChatPromptTemplate:
        """Create baseball-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로야구(KBO) 전문 분석가입니다.
            team_stats 테이블의 상세한 통계와 WDL 데이터를 활용하여 분석합니다.
            
            분석 요소:
            1. 투수 분석 (ERA, 피안타, 볼넷, 삼진 등)
            2. 타격 분석 (타율, 득점, 안타, 홈런 등)
            3. 수비 분석 (실책, 수비율 등)
            4. 최근 폼 (승패, 득실점 차이)
            5. 맞대결 기록 (H2H 승률, 패턴)
            
            야구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 야구 경기를 분석해주세요:
            
            경기 정보:
            {match_info}
            
            팀 통계 (team_stats):
            {team_stats}
            
            H2H WDL 요약:
            {h2h_wdl}
            
            홈팀 WDL 요약:
            {home_wdl}
            
            원정팀 WDL 요약:
            {away_wdl}
            
            기존 분석 콘텐츠:
            {existing_content}
            
            베팅 정보:
            {betting_info}
            
            야구 전문 분석을 제공해주세요.
            """)
        ])
    
    def analyze(self, match_id: str) -> BaseballAnalysisResult:
        """Analyze baseball match using team_stats and WDL data."""
        try:
            # Get complete match data
            match_data = self.repository.get_complete_match_data(match_id)
            if not match_data:
                raise ValueError(f"Match data not found: {match_id}")
            
            # Prepare analysis data
            analysis_data = {
                "match_info": self._format_match_info(match_data),
                "team_stats": self._format_team_stats(match_data.get('team_stats', [])),
                "h2h_wdl": self._format_wdl_data(match_data.get('h2h_wdl_summary')),
                "home_wdl": self._format_wdl_data(match_data.get('home_wdl_summary')),
                "away_wdl": self._format_wdl_data(match_data.get('away_wdl_summary')),
                "existing_content": match_data.get('naver_text', '기존 분석 없음'),
                "betting_info": self._format_betting_info(match_data.get('betting_picks'))
            }
            
            # Generate analysis
            chain = self.prompt_template | self.llm.with_structured_output(BaseballAnalysisResult)
            result = chain.invoke(analysis_data)
            
            # Add generated hashtags
            result.hashtags = self._generate_baseball_hashtags(match_data)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in baseball analysis: {e}")
            return self._create_fallback_baseball_analysis(match_id, match_data)
    
    def _format_team_stats(self, team_stats: List[Dict[str, Any]]) -> str:
        """Format team_stats data for prompt."""
        if not team_stats:
            return "팀 통계 데이터가 없습니다."
        
        formatted = "팀별 상세 통계:\n"
        for stats in team_stats:
            team_id = stats.get('team_id', 'Unknown')
            formatted += f"\n{team_id} 팀:\n"
            
            # Batting stats
            if stats.get('runs') is not None:
                formatted += f"  득점: {stats['runs']}\n"
            if stats.get('hits') is not None:
                formatted += f"  안타: {stats['hits']}\n"
            if stats.get('batting_average') is not None:
                formatted += f"  타율: {stats['batting_average']:.3f}\n"
            if stats.get('home_runs') is not None:
                formatted += f"  홈런: {stats['home_runs']}\n"
            
            # Pitching stats
            if stats.get('era') is not None:
                formatted += f"  평균자책점: {stats['era']:.2f}\n"
            if stats.get('strikeouts') is not None:
                formatted += f"  삼진: {stats['strikeouts']}\n"
            if stats.get('walks') is not None:
                formatted += f"  볼넷: {stats['walks']}\n"
            
            # Defense stats
            if stats.get('errors') is not None:
                formatted += f"  실책: {stats['errors']}\n"
            
            # Record
            if stats.get('wins') is not None:
                formatted += f"  승: {stats['wins']}\n"
            if stats.get('losses') is not None:
                formatted += f"  패: {stats['losses']}\n"
        
        return formatted
    
    def _generate_baseball_hashtags(self, match_data: Dict[str, Any]) -> List[str]:
        """Generate baseball-specific hashtags."""
        hashtags = ["#KBO", "#야구", "#프로야구", "#야구분석"]
        
        home_team = match_data.get('home_team', '')
        away_team = match_data.get('away_team', '')
        
        # Add team hashtags
        if home_team:
            hashtags.append(f"#{home_team}")
        if away_team:
            hashtags.append(f"#{away_team}")
        
        # Add matchup hashtag
        if home_team and away_team:
            hashtags.append(f"#{home_team}vs{away_team}")
        
        return hashtags
    
    def _create_fallback_baseball_analysis(self, match_id: str, match_data: Dict[str, Any]) -> BaseballAnalysisResult:
        """Create fallback analysis for baseball."""
        return BaseballAnalysisResult(
            match_id=match_id,
            sport="baseball",
            home_team=match_data.get('home_team', 'Unknown'),
            away_team=match_data.get('away_team', 'Unknown'),
            match_date=match_data.get('match_date', ''),
            summary="데이터 부족으로 기본 분석만 제공됩니다.",
            pitcher_analysis="투수 분석 데이터 부족",
            batting_analysis="타격 분석 데이터 부족",
            recent_form="최근 폼 분석 불가",
            h2h_analysis="맞대결 분석 불가",
            key_factors=["데이터 부족"],
            prediction="예측 불가",
            recommendation="베팅 보류 권장",
            confidence=0.3,
            hashtags=self._generate_baseball_hashtags(match_data)
        )


class SoccerAgent:
    """
    Soccer analysis agent using WDL data only.
    
    Soccer analysis focuses on recent form, head-to-head records,
    and tactical analysis based on available WDL data.
    """
    
    def __init__(self, llm: BaseChatModel, repository: RealSupabaseRepository):
        self.llm = llm
        self.repository = repository
        self.prompt_template = self._create_soccer_prompt()
    
    def _create_soccer_prompt(self) -> ChatPromptTemplate:
        """Create soccer-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로축구(K리그) 전문 분석가입니다.
            WDL(승무패) 데이터를 중심으로 축구 경기를 분석합니다.
            
            분석 요소:
            1. 최근 폼 (승점, 승률, 득실점)
            2. 홈/원정 성적 차이
            3. 맞대결 기록 및 패턴
            4. 전술적 특징 (가능한 범위에서)
            5. 부상자 및 출전 정보
            
            축구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 축구 경기를 분석해주세요:
            
            경기 정보:
            {match_info}
            
            H2H WDL 요약:
            {h2h_wdl}
            
            홈팀 WDL 요약:
            {home_wdl}
            
            원정팀 WDL 요약:
            {away_wdl}
            
            H2H 경기 기록:
            {h2h_matches}
            
            기존 분석 콘텐츠:
            {existing_content}
            
            베팅 정보:
            {betting_info}
            
            축구 전문 분석을 제공해주세요.
            """)
        ])
    
    def analyze(self, match_id: str) -> SoccerAnalysisResult:
        """Analyze soccer match using WDL data."""
        try:
            # Get complete match data
            match_data = self.repository.get_complete_match_data(match_id)
            if not match_data:
                raise ValueError(f"Match data not found: {match_id}")
            
            # Prepare analysis data
            analysis_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_wdl": self._format_wdl_data(match_data.get('h2h_wdl_summary')),
                "home_wdl": self._format_wdl_data(match_data.get('home_wdl_summary')),
                "away_wdl": self._format_wdl_data(match_data.get('away_wdl_summary')),
                "h2h_matches": self._format_h2h_matches(match_data.get('h2h_wdl_matches')),
                "existing_content": match_data.get('naver_text', '기존 분석 없음'),
                "betting_info": self._format_betting_info(match_data.get('betting_picks'))
            }
            
            # Generate analysis
            chain = self.prompt_template | self.llm.with_structured_output(SoccerAnalysisResult)
            result = chain.invoke(analysis_data)
            
            # Add generated hashtags
            result.hashtags = self._generate_soccer_hashtags(match_data)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in soccer analysis: {e}")
            return self._create_fallback_soccer_analysis(match_id, match_data)
    
    def _format_h2h_matches(self, h2h_matches: Optional[List[Dict[str, Any]]]) -> str:
        """Format H2H match history."""
        if not h2h_matches:
            return "과거 대전 기록이 없습니다."
        
        formatted = "최근 맞대결 기록:\n"
        for i, match in enumerate(h2h_matches[:5], 1):
            formatted += f"{i}. {match}\n"
        
        return formatted
    
    def _generate_soccer_hashtags(self, match_data: Dict[str, Any]) -> List[str]:
        """Generate soccer-specific hashtags."""
        hashtags = ["#K리그", "#축구", "#프로축구", "#축구분석"]
        
        home_team = match_data.get('home_team', '')
        away_team = match_data.get('away_team', '')
        
        if home_team:
            hashtags.append(f"#{home_team}")
        if away_team:
            hashtags.append(f"#{away_team}")
        
        if home_team and away_team:
            hashtags.append(f"#{home_team}vs{away_team}")
        
        return hashtags
    
    def _create_fallback_soccer_analysis(self, match_id: str, match_data: Dict[str, Any]) -> SoccerAnalysisResult:
        """Create fallback analysis for soccer."""
        return SoccerAnalysisResult(
            match_id=match_id,
            sport="soccer",
            home_team=match_data.get('home_team', 'Unknown'),
            away_team=match_data.get('away_team', 'Unknown'),
            match_date=match_data.get('match_date', ''),
            summary="데이터 부족으로 기본 분석만 제공됩니다.",
            formation_analysis="전술 분석 데이터 부족",
            recent_form="최근 폼 분석 불가",
            h2h_analysis="맞대결 분석 불가",
            injury_report="부상자 정보 없음",
            key_factors=["데이터 부족"],
            prediction="예측 불가",
            recommendation="베팅 보류 권장",
            confidence=0.3,
            hashtags=self._generate_soccer_hashtags(match_data)
        )


# Base methods shared by all agents
class BaseAgent:
    """Base class with shared methods for all sport agents."""
    
    def _format_match_info(self, match_data: Dict[str, Any]) -> str:
        """Format basic match information."""
        return f"""
        경기 ID: {match_data.get('match_id', 'Unknown')}
        홈팀: {match_data.get('home_team', 'Unknown')}
        원정팀: {match_data.get('away_team', 'Unknown')}
        경기 날짜: {match_data.get('match_date', 'Unknown')} {match_data.get('match_time', '')}
        경기장: {match_data.get('venue', 'Unknown')}
        스포츠: {match_data.get('sports', 'Unknown')}
        """
    
    def _format_wdl_data(self, wdl_data: Optional[Dict[str, Any]]) -> str:
        """Format WDL data for prompt."""
        if not wdl_data:
            return "WDL 데이터가 없습니다."
        
        formatted = "WDL 요약:\n"
        for key, value in wdl_data.items():
            formatted += f"- {key}: {value}\n"
        
        return formatted
    
    def _format_betting_info(self, betting_data: Optional[Dict[str, Any]]) -> str:
        """Format betting information."""
        if not betting_data:
            return "베팅 정보가 없습니다."
        
        formatted = "베팅 정보:\n"
        for key, value in betting_data.items():
            if key not in ['match_id', 'created_at', 'updated_at']:
                formatted += f"- {key}: {value}\n"
        
        return formatted


class BasketballAgent(BaseAgent):
    """
    Basketball analysis agent using WDL data only.

    Basketball analysis focuses on recent performance, scoring patterns,
    and head-to-head matchups.
    """

    def __init__(self, llm: BaseChatModel, repository: RealSupabaseRepository):
        self.llm = llm
        self.repository = repository
        self.prompt_template = self._create_basketball_prompt()

    def _create_basketball_prompt(self) -> ChatPromptTemplate:
        """Create basketball-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로농구(KBL) 전문 분석가입니다.
            WDL 데이터를 중심으로 농구 경기를 분석합니다.

            분석 요소:
            1. 최근 폼 (승률, 득점력, 수비력)
            2. 홈/원정 성적 차이
            3. 맞대결 기록 및 패턴
            4. 공격/수비 스타일 분석
            5. 주요 선수 컨디션

            농구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 농구 경기를 분석해주세요:

            경기 정보:
            {match_info}

            H2H WDL 요약:
            {h2h_wdl}

            홈팀 WDL 요약:
            {home_wdl}

            원정팀 WDL 요약:
            {away_wdl}

            기존 분석 콘텐츠:
            {existing_content}

            베팅 정보:
            {betting_info}

            농구 전문 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> BasketballAnalysisResult:
        """Analyze basketball match using WDL data."""
        try:
            match_data = self.repository.get_complete_match_data(match_id)
            if not match_data:
                raise ValueError(f"Match data not found: {match_id}")

            analysis_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_wdl": self._format_wdl_data(match_data.get('h2h_wdl_summary')),
                "home_wdl": self._format_wdl_data(match_data.get('home_wdl_summary')),
                "away_wdl": self._format_wdl_data(match_data.get('away_wdl_summary')),
                "existing_content": match_data.get('naver_text', '기존 분석 없음'),
                "betting_info": self._format_betting_info(match_data.get('betting_picks'))
            }

            chain = self.prompt_template | self.llm.with_structured_output(BasketballAnalysisResult)
            result = chain.invoke(analysis_data)
            result.hashtags = self._generate_basketball_hashtags(match_data)

            return result

        except Exception as e:
            logger.error(f"Error in basketball analysis: {e}")
            return self._create_fallback_basketball_analysis(match_id, match_data)

    def _generate_basketball_hashtags(self, match_data: Dict[str, Any]) -> List[str]:
        """Generate basketball-specific hashtags."""
        hashtags = ["#KBL", "#농구", "#프로농구", "#농구분석"]

        home_team = match_data.get('home_team', '')
        away_team = match_data.get('away_team', '')

        if home_team:
            hashtags.append(f"#{home_team}")
        if away_team:
            hashtags.append(f"#{away_team}")

        return hashtags

    def _create_fallback_basketball_analysis(self, match_id: str, match_data: Dict[str, Any]) -> BasketballAnalysisResult:
        """Create fallback analysis for basketball."""
        return BasketballAnalysisResult(
            match_id=match_id,
            sport="basketball",
            home_team=match_data.get('home_team', 'Unknown'),
            away_team=match_data.get('away_team', 'Unknown'),
            match_date=match_data.get('match_date', ''),
            summary="데이터 부족으로 기본 분석만 제공됩니다.",
            offense_analysis="공격 분석 데이터 부족",
            defense_analysis="수비 분석 데이터 부족",
            recent_form="최근 폼 분석 불가",
            h2h_analysis="맞대결 분석 불가",
            key_factors=["데이터 부족"],
            prediction="예측 불가",
            recommendation="베팅 보류 권장",
            confidence=0.3,
            hashtags=self._generate_basketball_hashtags(match_data)
        )


class VolleyballAgent(BaseAgent):
    """
    Volleyball analysis agent using WDL data only.

    Volleyball analysis focuses on recent performance, attack/block patterns,
    and head-to-head matchups.
    """

    def __init__(self, llm: BaseChatModel, repository: RealSupabaseRepository):
        self.llm = llm
        self.repository = repository
        self.prompt_template = self._create_volleyball_prompt()

    def _create_volleyball_prompt(self) -> ChatPromptTemplate:
        """Create volleyball-specific analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """
            당신은 한국 프로배구(V리그) 전문 분석가입니다.
            WDL 데이터를 중심으로 배구 경기를 분석합니다.

            분석 요소:
            1. 최근 폼 (승률, 세트 득실)
            2. 홈/원정 성적 차이
            3. 맞대결 기록 및 패턴
            4. 공격/블로킹 스타일 분석
            5. 주요 선수 컨디션

            배구 특성을 고려한 전문적인 분석을 제공하세요.
            """),
            ("human", """
            다음 배구 경기를 분석해주세요:

            경기 정보:
            {match_info}

            H2H WDL 요약:
            {h2h_wdl}

            홈팀 WDL 요약:
            {home_wdl}

            원정팀 WDL 요약:
            {away_wdl}

            기존 분석 콘텐츠:
            {existing_content}

            베팅 정보:
            {betting_info}

            배구 전문 분석을 제공해주세요.
            """)
        ])

    def analyze(self, match_id: str) -> VolleyballAnalysisResult:
        """Analyze volleyball match using WDL data."""
        try:
            match_data = self.repository.get_complete_match_data(match_id)
            if not match_data:
                raise ValueError(f"Match data not found: {match_id}")

            analysis_data = {
                "match_info": self._format_match_info(match_data),
                "h2h_wdl": self._format_wdl_data(match_data.get('h2h_wdl_summary')),
                "home_wdl": self._format_wdl_data(match_data.get('home_wdl_summary')),
                "away_wdl": self._format_wdl_data(match_data.get('away_wdl_summary')),
                "existing_content": match_data.get('naver_text', '기존 분석 없음'),
                "betting_info": self._format_betting_info(match_data.get('betting_picks'))
            }

            chain = self.prompt_template | self.llm.with_structured_output(VolleyballAnalysisResult)
            result = chain.invoke(analysis_data)
            result.hashtags = self._generate_volleyball_hashtags(match_data)

            return result

        except Exception as e:
            logger.error(f"Error in volleyball analysis: {e}")
            return self._create_fallback_volleyball_analysis(match_id, match_data)

    def _generate_volleyball_hashtags(self, match_data: Dict[str, Any]) -> List[str]:
        """Generate volleyball-specific hashtags."""
        hashtags = ["#V리그", "#배구", "#프로배구", "#배구분석"]

        home_team = match_data.get('home_team', '')
        away_team = match_data.get('away_team', '')

        if home_team:
            hashtags.append(f"#{home_team}")
        if away_team:
            hashtags.append(f"#{away_team}")

        return hashtags

    def _create_fallback_volleyball_analysis(self, match_id: str, match_data: Dict[str, Any]) -> VolleyballAnalysisResult:
        """Create fallback analysis for volleyball."""
        return VolleyballAnalysisResult(
            match_id=match_id,
            sport="volleyball",
            home_team=match_data.get('home_team', 'Unknown'),
            away_team=match_data.get('away_team', 'Unknown'),
            match_date=match_data.get('match_date', ''),
            summary="데이터 부족으로 기본 분석만 제공됩니다.",
            attack_analysis="공격 분석 데이터 부족",
            block_analysis="블로킹 분석 데이터 부족",
            recent_form="최근 폼 분석 불가",
            h2h_analysis="맞대결 분석 불가",
            key_factors=["데이터 부족"],
            prediction="예측 불가",
            recommendation="베팅 보류 권장",
            confidence=0.3,
            hashtags=self._generate_volleyball_hashtags(match_data)
        )


class SportAnalysisFactory:
    """
    Factory for creating sport-specific analysis agents.

    This factory automatically selects the appropriate agent based on
    the sport type detected from the match data.
    """

    @staticmethod
    def create_agent(sport_type: SportType, llm: BaseChatModel, repository: RealSupabaseRepository):
        """Create appropriate agent for the sport type."""
        if sport_type == SportType.BASEBALL:
            return BaseballAgent(llm, repository)
        elif sport_type == SportType.SOCCER:
            return SoccerAgent(llm, repository)
        elif sport_type == SportType.BASKETBALL:
            return BasketballAgent(llm, repository)
        elif sport_type == SportType.VOLLEYBALL:
            return VolleyballAgent(llm, repository)
        else:
            logger.warning(f"Unknown sport type: {sport_type}, defaulting to baseball")
            return BaseballAgent(llm, repository)

    @staticmethod
    def get_supported_sports() -> List[SportType]:
        """Get list of supported sports."""
        return [SportType.BASEBALL, SportType.SOCCER, SportType.BASKETBALL, SportType.VOLLEYBALL]


class MultiSportAnalysisService:
    """
    Service that handles analysis for all sports using appropriate agents.

    This service automatically detects the sport type and uses the
    corresponding specialized agent for analysis.
    """

    def __init__(self, llm: BaseChatModel, repository: RealSupabaseRepository):
        self.llm = llm
        self.repository = repository
        self.factory = SportAnalysisFactory()

    def analyze_match(self, match_id: str) -> SportAnalysisResult:
        """
        Analyze a match using the appropriate sport-specific agent.

        Args:
            match_id: The match identifier

        Returns:
            SportAnalysisResult: Analysis result specific to the sport
        """
        try:
            # Get match data to determine sport type
            match_data = self.repository.get_match_data(match_id)
            if not match_data:
                raise ValueError(f"Match not found: {match_id}")

            # Determine sport type
            sport_type = self.repository.get_sport_type(match_data)
            logger.info(f"Analyzing {sport_type.value} match: {match_id}")

            # Create appropriate agent
            agent = self.factory.create_agent(sport_type, self.llm, self.repository)

            # Perform analysis
            result = agent.analyze(match_id)

            logger.info(f"Analysis completed for {sport_type.value} match: {match_id}")
            return result

        except Exception as e:
            logger.error(f"Error analyzing match {match_id}: {e}")
            raise

    def analyze_all_target_matches(self) -> List[SportAnalysisResult]:
        """Analyze all target matches using appropriate agents."""
        target_matches = self.repository.get_sns_target_matches()

        results = []
        for match_data in target_matches:
            try:
                match_id = match_data["match_id"]
                result = self.analyze_match(match_id)
                results.append(result)
            except Exception as e:
                logger.error(f"Error analyzing match {match_data.get('match_id', 'unknown')}: {e}")
                continue

        return results
