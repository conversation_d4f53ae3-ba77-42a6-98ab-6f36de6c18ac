"""
Domain models for baseball analytics using Pydantic v2.

This module defines the core business entities and value objects
for baseball analysis and content generation.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator, model_validator


class SportType(str, Enum):
    """Supported sport types."""
    BASEBALL = "baseball"
    SOCCER = "soccer"
    BASKETBALL = "basketball"
    VOLLEYBALL = "volleyball"


class MatchStatus(str, Enum):
    """Match status enumeration."""
    SCHEDULED = "scheduled"
    LIVE = "live"
    FINISHED = "finished"
    CANCELLED = "cancelled"
    POSTPONED = "postponed"


class TeamPosition(str, Enum):
    """Team position in a match."""
    HOME = "home"
    AWAY = "away"


class ContentType(str, Enum):
    """Content type enumeration for content generation."""
    SOCIAL_MEDIA_POST = "social_media_post"
    BLOG_ARTICLE = "blog_article"
    NEWS_SUMMARY = "news_summary"
    MATCH_PREVIEW = "match_preview"
    MATCH_RECAP = "match_recap"
    STATISTICAL_ANALYSIS = "statistical_analysis"


class ContentTone(str, Enum):
    """Content tone enumeration for content generation."""
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    ENTHUSIASTIC = "enthusiastic"
    ANALYTICAL = "analytical"
    HUMOROUS = "humorous"
    FORMAL = "formal"


class Team(BaseModel):
    """Baseball team entity."""
    
    id: str = Field(..., description="Unique team identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Team name")
    short_name: str = Field(..., min_length=1, max_length=10, description="Team abbreviation")
    logo_url: Optional[str] = Field(None, description="Team logo URL")
    founded_year: Optional[int] = Field(None, ge=1800, le=2030, description="Year team was founded")
    
    @field_validator('name', 'short_name')
    @classmethod
    def validate_name_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Name cannot be empty or whitespace only")
        return v.strip()


class Player(BaseModel):
    """Baseball player entity."""
    
    id: str = Field(..., description="Unique player identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Player name")
    team_id: str = Field(..., description="Team identifier")
    position: str = Field(..., description="Player position")
    jersey_number: Optional[int] = Field(None, ge=0, le=99, description="Jersey number")
    birth_date: Optional[datetime] = Field(None, description="Player birth date")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Player name cannot be empty")
        return v.strip()


class Match(BaseModel):
    """Baseball match entity."""
    
    match_id: str = Field(..., description="Unique match identifier")
    sport: SportType = Field(SportType.BASEBALL, description="Sport type")
    home_team: Team = Field(..., description="Home team")
    away_team: Team = Field(..., description="Away team")
    match_date: datetime = Field(..., description="Match date and time")
    status: MatchStatus = Field(MatchStatus.SCHEDULED, description="Match status")
    venue: Optional[str] = Field(None, description="Match venue")
    season: Optional[str] = Field(None, description="Season identifier")
    
    home_score: Optional[int] = Field(None, ge=0, description="Home team score")
    away_score: Optional[int] = Field(None, ge=0, description="Away team score")
    
    @model_validator(mode='after')
    def validate_teams_different(self) -> 'Match':
        if self.home_team.id == self.away_team.id:
            raise ValueError("Home and away teams must be different")
        return self


class TeamStats(BaseModel):
    """Team statistics for analysis."""
    
    team_id: str = Field(..., description="Team identifier")
    match_id: str = Field(..., description="Match identifier")
    
    # Batting statistics
    runs: Optional[int] = Field(None, ge=0, description="Runs scored")
    hits: Optional[int] = Field(None, ge=0, description="Hits")
    errors: Optional[int] = Field(None, ge=0, description="Errors")
    batting_average: Optional[float] = Field(None, ge=0.0, le=1.0, description="Batting average")
    
    # Pitching statistics
    earned_runs: Optional[int] = Field(None, ge=0, description="Earned runs allowed")
    strikeouts: Optional[int] = Field(None, ge=0, description="Strikeouts")
    walks: Optional[int] = Field(None, ge=0, description="Walks allowed")
    era: Optional[float] = Field(None, ge=0.0, description="Earned run average")
    
    # Win-Draw-Loss record
    wins: Optional[int] = Field(None, ge=0, description="Number of wins")
    draws: Optional[int] = Field(None, ge=0, description="Number of draws")
    losses: Optional[int] = Field(None, ge=0, description="Number of losses")


class AnalysisContext(BaseModel):
    """Context data for baseball analysis."""
    
    match: Match = Field(..., description="Match information")
    home_team_stats: List[TeamStats] = Field(default_factory=list, description="Home team statistics")
    away_team_stats: List[TeamStats] = Field(default_factory=list, description="Away team statistics")
    historical_h2h: List[Match] = Field(default_factory=list, description="Head-to-head match history")
    weather_conditions: Optional[Dict[str, Any]] = Field(None, description="Weather conditions")
    additional_context: Dict[str, Any] = Field(default_factory=dict, description="Additional context data")


class AnalysisInsight(BaseModel):
    """Individual analysis insight."""
    
    category: str = Field(..., description="Insight category")
    title: str = Field(..., min_length=1, description="Insight title")
    content: str = Field(..., min_length=1, description="Insight content")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    supporting_data: Dict[str, Any] = Field(default_factory=dict, description="Supporting data")


class PredictionResult(BaseModel):
    """Match prediction result."""
    
    home_win_probability: float = Field(..., ge=0.0, le=1.0, description="Home team win probability")
    away_win_probability: float = Field(..., ge=0.0, le=1.0, description="Away team win probability")
    draw_probability: float = Field(0.0, ge=0.0, le=1.0, description="Draw probability")
    
    predicted_home_score: Optional[float] = Field(None, ge=0.0, description="Predicted home score")
    predicted_away_score: Optional[float] = Field(None, ge=0.0, description="Predicted away score")
    
    @model_validator(mode='after')
    def validate_probabilities_sum(self) -> 'PredictionResult':
        total = self.home_win_probability + self.away_win_probability + self.draw_probability
        if not (0.99 <= total <= 1.01):  # Allow small floating point errors
            raise ValueError(f"Probabilities must sum to 1.0, got {total}")
        return self


class AnalysisResult(BaseModel):
    """Complete analysis result."""
    
    analysis_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique analysis ID")
    match_id: str = Field(..., description="Match identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")
    
    insights: List[AnalysisInsight] = Field(default_factory=list, description="Analysis insights")
    prediction: Optional[PredictionResult] = Field(None, description="Match prediction")
    
    key_points: List[str] = Field(default_factory=list, description="Key analysis points")
    hashtags: List[str] = Field(default_factory=list, description="Suggested hashtags")
    
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall analysis confidence")
    
    @field_validator('hashtags')
    @classmethod
    def validate_hashtags(cls, v: List[str]) -> List[str]:
        """Ensure hashtags start with # and are properly formatted."""
        validated = []
        for tag in v:
            if not tag.startswith('#'):
                tag = f"#{tag}"
            # Remove spaces and special characters except underscore
            tag = ''.join(c for c in tag if c.isalnum() or c in ['#', '_'])
            if len(tag) > 1:  # Must have content after #
                validated.append(tag)
        return validated


class ContentGenerationRequest(BaseModel):
    """Request for content generation."""
    
    analysis_result: AnalysisResult = Field(..., description="Analysis result to base content on")
    content_type: str = Field(..., description="Type of content to generate")
    target_audience: str = Field("general", description="Target audience")
    tone: str = Field("professional", description="Content tone")
    max_length: Optional[int] = Field(None, gt=0, description="Maximum content length")
    include_hashtags: bool = Field(True, description="Whether to include hashtags")


class GeneratedContent(BaseModel):
    """Generated content result."""
    
    content_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique content ID")
    content_type: str = Field(..., description="Type of generated content")
    title: str = Field(..., description="Content title")
    body: str = Field(..., description="Main content body")
    hashtags: List[str] = Field(default_factory=list, description="Content hashtags")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
