"""
Unified Database Module for Multi-Sport Analytics.

This module consolidates all database access functionality and provides
a clean interface for accessing actual Supabase data across all sports.

Integrates functionality from:
- db/database.py (existing functions)
- Enhanced repository pattern
- Sport-specific data access

Database Tables:
- target_games: Match information
- sportic_contents: Analysis content and WDL data
- sportic_sns: Social media content
- sportic_pick: Betting picks
- team_stats: Baseball-specific team statistics (JSONB fields)
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import pytz
from supabase import Client, create_client

import config.config as global_config
from baseball_analytics.utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# Initialize Supabase client
supabase: Optional[Client] = None

if global_config.SUPABASE_URL and global_config.SUPABASE_KEY:
    supabase = create_client(global_config.SUPABASE_URL, global_config.SUPABASE_KEY)
    logger.info("Supabase client initialized successfully")
else:
    logger.error("Supabase URL or Key not configured. Check config/config.py")


# ========== Core Database Functions ==========

def get_match_data(match_id: str, fields: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
    """Get match data from target_games table."""
    if not match_id:
        logger.warning("No match ID provided")
        return None
    
    if not supabase:
        logger.error("Supabase client not initialized")
        return None
    
    select_str = '*' if not fields else ','.join(fields)
    
    try:
        response = (
            supabase.table('target_games')
            .select(select_str)
            .eq('match_id', match_id)
            .execute()
        )
        
        if response.data:
            return response.data[0]
        
        logger.warning(f"No data found for match ID: {match_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving match data: {e}")
        return None


def get_sportic_content(match_id: str, fields: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
    """Get sportic_contents data for a match."""
    if not match_id:
        logger.warning("No match ID provided")
        return None
    
    if not supabase:
        logger.error("Supabase client not initialized")
        return None
    
    select_str = '*' if not fields else ','.join(fields)
    
    try:
        response = (
            supabase.table('sportic_contents')
            .select(select_str)
            .eq('match_id', match_id)
            .execute()
        )
        
        if response.data:
            return response.data[0]
        
        logger.info(f"No sportic_contents found for match: {match_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving sportic_contents: {e}")
        return None


def get_wdl_data(match_id: str, mode: str = 'h2h', data_type: str = 'summary') -> Optional[Any]:
    """Get WDL (Win-Draw-Loss) data from sportic_contents."""
    if not match_id:
        logger.warning(f"No match ID provided for {mode} {data_type}")
        return None
    
    field_name = f"{mode}_wdl_{data_type}"
    
    try:
        response = (
            supabase.table('sportic_contents')
            .select(field_name)
            .eq('match_id', match_id)
            .execute()
        )
        
        if response.data and field_name in response.data[0]:
            return response.data[0][field_name]
        
        logger.info(f"No {field_name} data found for match: {match_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving {mode} WDL {data_type}: {e}")
        return None


def get_team_stats(match_id: str) -> List[Dict[str, Any]]:
    """Get team statistics from team_stats table (baseball only)."""
    if not match_id:
        logger.warning("No match ID provided for team stats")
        return []
    
    if not supabase:
        logger.error("Supabase client not initialized")
        return []
    
    try:
        response = (
            supabase.table("team_stats")
            .select("*")
            .eq("match_id", match_id)
            .execute()
        )
        
        if response.data:
            logger.info(f"Found {len(response.data)} team stats for match {match_id}")
            return response.data
        
        logger.info(f"No team stats found for match: {match_id}")
        return []
        
    except Exception as e:
        logger.error(f"Error retrieving team stats: {e}")
        return []


def get_content_for_match(match_id: str) -> Optional[str]:
    """Get existing content (naver_text or h2h_content) for a match."""
    if not match_id:
        logger.warning("No match ID provided for content")
        return None
    
    try:
        # First try naver_text from sportic_sns
        response = (
            supabase.table('sportic_sns')
            .select('naver_text')
            .eq('match_id', match_id)
            .execute()
        )
        
        if response.data and 'naver_text' in response.data[0]:
            naver_text = response.data[0]['naver_text']
            if isinstance(naver_text, dict) and 'ko' in naver_text:
                return naver_text['ko']
            elif naver_text:
                return str(naver_text)
        
        # Fallback to h2h_content from sportic_contents
        content_response = (
            supabase.table('sportic_contents')
            .select('h2h_content')
            .eq('match_id', match_id)
            .execute()
        )
        
        if content_response.data and 'h2h_content' in content_response.data[0]:
            h2h_content = content_response.data[0]['h2h_content']
            if isinstance(h2h_content, dict):
                return str({k: v for k, v in h2h_content.items() if v})
            elif h2h_content:
                return str(h2h_content)
        
        logger.info(f"No content found for match: {match_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving content for match {match_id}: {e}")
        return None


def get_betting_picks(match_id: str) -> Optional[Dict[str, Any]]:
    """Get betting picks from sportic_pick table."""
    if not match_id:
        logger.warning("No match ID provided for betting picks")
        return None
    
    if not supabase:
        logger.error("Supabase client not initialized")
        return None
    
    try:
        response = (
            supabase.table('sportic_pick')
            .select('*')
            .eq('match_id', match_id)
            .execute()
        )
        
        if response.data:
            return response.data[0]
        
        logger.info(f"No betting picks found for match: {match_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving betting picks: {e}")
        return None


def get_sns_target_matches() -> List[Dict[str, Any]]:
    """
    Get target matches for SNS posting using RPC function.
    
    Returns matches that are:
    - Within creation window for their sport
    - Have content available
    - Not yet posted (depending on configuration)
    """
    if not supabase:
        logger.error("Supabase client not initialized")
        return []
    
    try:
        response = supabase.rpc("get_sns_target_matches").execute()
        
        if not response.data:
            logger.info("No target matches found")
            return []
        
        # Filter matches within creation window
        tz = pytz.timezone('Asia/Seoul')
        now = datetime.now(tz)
        valid_matches = []
        
        for match_data in response.data:
            if _is_within_creation_window(match_data, now, tz):
                valid_matches.append(match_data)
        
        logger.info(f"Found {len(valid_matches)} valid target matches")
        return valid_matches
        
    except Exception as e:
        logger.error(f"Error retrieving SNS target matches: {e}")
        return []


def get_match_by_id(match_id: str) -> Optional[Dict[str, Any]]:
    """Get complete match information by combining target_games and sportic_contents."""
    match_data = get_match_data(match_id)
    if not match_data:
        return None
    
    # Merge with sportic_contents data
    sportic_data = get_sportic_content(
        match_id,
        fields=[
            'h2h_content', 'h2h_wdl_summary', 'home_wdl_summary',
            'away_wdl_summary', 'h2h_wdl_matches', 'home_wdl_matches',
            'away_wdl_matches'
        ]
    )
    
    if sportic_data:
        for key, value in sportic_data.items():
            if value is not None:
                match_data[key] = value
    
    return match_data


# ========== Utility Functions ==========

def _is_within_creation_window(match: Dict[str, Any], now: datetime, tz: pytz.BaseTzInfo) -> bool:
    """Check if match is within creation window for analysis."""
    try:
        sport = match.get('sports', '').lower()
        match_datetime_str = f"{match['match_date']} {match['match_time']}"
        match_datetime = datetime.strptime(match_datetime_str, "%Y-%m-%d %H:%M:%S")
        match_datetime = tz.localize(match_datetime)
        
        # Baseball: 34 hours before to 10 minutes before
        if sport in {'baseball', '야구', 'bseball'}:
            from datetime import timedelta
            start_window = match_datetime - timedelta(hours=34)
            end_window = match_datetime - timedelta(minutes=10)
            return start_window <= now <= end_window
        
        # Other sports: 24 hours before to 10 minutes before
        elif sport in {'soccer', 'football', '축구', 'basketball', '농구', 'volleyball', '배구'}:
            from datetime import timedelta
            start_window = match_datetime - timedelta(hours=24)
            end_window = match_datetime - timedelta(minutes=10)
            return start_window <= now <= end_window
        
        return False
        
    except Exception as e:
        logger.error(f"Error checking creation window: {e}")
        return False


def get_sport_type(match_data: Dict[str, Any]) -> str:
    """Determine sport type from match data."""
    sport_str = match_data.get("sports", "").lower()
    
    if sport_str in {"baseball", "야구", "bseball"}:
        return "baseball"
    elif sport_str in {"soccer", "football", "축구"}:
        return "soccer"
    elif sport_str in {"basketball", "농구"}:
        return "basketball"
    elif sport_str in {"volleyball", "배구"}:
        return "volleyball"
    else:
        logger.warning(f"Unknown sport type: {sport_str}")
        return "unknown"


def is_baseball_match(match_data: Dict[str, Any]) -> bool:
    """Check if match is baseball."""
    return get_sport_type(match_data) == "baseball"


# ========== Legacy Function Aliases ==========
# These maintain compatibility with existing code

def iter_sns_target_matches():
    """Generator that yields target matches one by one."""
    for match in get_sns_target_matches():
        yield match


def iter_baseball_team_stats():
    """Generator that yields baseball team stats for target matches."""
    for match in iter_sns_target_matches():
        if is_baseball_match(match):
            stats = get_team_stats(match["match_id"])
            yield match, stats
