"""
Real Supabase Repository based on actual database structure.

This module implements data access based on the actual Supabase schema:
- target_games: Match information
- sportic_contents: Analysis content and WDL data  
- sportic_sns: Social media content
- sportic_pick: Betting picks
- team_stats: Baseball-specific team statistics

Different sports use different data sources:
- Baseball: team_stats table
- Soccer/Basketball/Volleyball: WDL functions in sportic_contents
"""

import logging
import pytz
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from supabase import Client, create_client
from pydantic import BaseModel, Field, ValidationError

from baseball_analytics.domain.models import (
    Match, Team, TeamStats, SportType, MatchStatus
)

logger = logging.getLogger(__name__)


class RealSupabaseRepository:
    """
    Repository implementation using actual Supabase database structure.
    
    This class provides access to real data exactly as it exists in the database,
    without making assumptions about non-existent tables or fields.
    """
    
    def __init__(self, url: str, key: str):
        self.client = create_client(url, key)
        self.timezone = pytz.timezone('Asia/Seoul')
        logger.info("Real Supabase Repository initialized")
    
    # ========== Core Data Access Methods ==========
    
    def get_sns_target_matches(self) -> List[Dict[str, Any]]:
        """Get target matches using the actual RPC function."""
        try:
            response = self.client.rpc("get_sns_target_matches").execute()
            
            if not response.data:
                logger.info("No target matches found")
                return []
            
            # Filter matches within creation window
            valid_matches = []
            for match_data in response.data:
                if self._is_within_creation_window(match_data):
                    valid_matches.append(match_data)
            
            logger.info(f"Found {len(valid_matches)} valid target matches")
            return valid_matches
            
        except Exception as e:
            logger.error(f"Error retrieving SNS target matches: {e}")
            return []
    
    def get_match_data(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get basic match data from target_games table."""
        try:
            response = self.client.table('target_games').select('*').eq('match_id', match_id).execute()
            
            if response.data:
                return response.data[0]
            
            logger.warning(f"Match not found: {match_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving match data for {match_id}: {e}")
            return None
    
    def get_sportic_content(self, match_id: str, fields: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
        """Get sportic_contents data for a match."""
        try:
            select_str = '*' if not fields else ','.join(fields)
            response = self.client.table('sportic_contents').select(select_str).eq('match_id', match_id).execute()
            
            if response.data:
                return response.data[0]
            
            logger.info(f"No sportic_contents found for match: {match_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving sportic_contents for {match_id}: {e}")
            return None
    
    def get_naver_text(self, match_id: str) -> Optional[str]:
        """Get naver_text from sportic_sns table."""
        try:
            response = self.client.table('sportic_sns').select('naver_text').eq('match_id', match_id).execute()
            
            if response.data and 'naver_text' in response.data[0]:
                naver_text = response.data[0]['naver_text']
                if isinstance(naver_text, dict) and 'ko' in naver_text:
                    return naver_text['ko']
                return str(naver_text) if naver_text else None
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving naver_text for {match_id}: {e}")
            return None
    
    def get_betting_picks(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get betting picks from sportic_pick table."""
        try:
            response = self.client.table('sportic_pick').select('*').eq('match_id', match_id).execute()
            
            if response.data:
                return response.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving betting picks for {match_id}: {e}")
            return None
    
    # ========== Baseball-Specific Methods ==========
    
    def get_team_stats(self, match_id: str) -> List[Dict[str, Any]]:
        """Get team statistics from team_stats table (baseball only)."""
        try:
            response = self.client.table("team_stats").select("*").eq("match_id", match_id).execute()
            
            if response.data:
                logger.info(f"Found {len(response.data)} team stats for match {match_id}")
                return response.data
            
            logger.info(f"No team stats found for match: {match_id}")
            return []
            
        except Exception as e:
            logger.error(f"Error retrieving team stats for {match_id}: {e}")
            return []
    
    # ========== WDL Data Methods (for all sports) ==========
    
    def get_h2h_wdl_summary(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get head-to-head WDL summary."""
        content = self.get_sportic_content(match_id, ['h2h_wdl_summary'])
        return content.get('h2h_wdl_summary') if content else None
    
    def get_home_wdl_summary(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get home team WDL summary."""
        content = self.get_sportic_content(match_id, ['home_wdl_summary'])
        return content.get('home_wdl_summary') if content else None
    
    def get_away_wdl_summary(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get away team WDL summary."""
        content = self.get_sportic_content(match_id, ['away_wdl_summary'])
        return content.get('away_wdl_summary') if content else None
    
    def get_h2h_wdl_matches(self, match_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get head-to-head match history."""
        content = self.get_sportic_content(match_id, ['h2h_wdl_matches'])
        return content.get('h2h_wdl_matches') if content else None
    
    def get_home_wdl_matches(self, match_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get home team recent matches."""
        content = self.get_sportic_content(match_id, ['home_wdl_matches'])
        return content.get('home_wdl_matches') if content else None
    
    def get_away_wdl_matches(self, match_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get away team recent matches."""
        content = self.get_sportic_content(match_id, ['away_wdl_matches'])
        return content.get('away_wdl_matches') if content else None
    
    def get_h2h_content(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get H2H content."""
        content = self.get_sportic_content(match_id, ['h2h_content'])
        return content.get('h2h_content') if content else None
    
    # ========== Sport Detection ==========
    
    def get_sport_type(self, match_data: Dict[str, Any]) -> SportType:
        """Determine sport type from match data."""
        sport_str = match_data.get("sports", "").lower()
        
        if sport_str in {"baseball", "야구", "bseball"}:
            return SportType.BASEBALL
        elif sport_str in {"soccer", "football", "축구"}:
            return SportType.SOCCER
        elif sport_str in {"basketball", "농구"}:
            return SportType.BASKETBALL
        elif sport_str in {"volleyball", "배구"}:
            return SportType.VOLLEYBALL
        else:
            logger.warning(f"Unknown sport type: {sport_str}, defaulting to baseball")
            return SportType.BASEBALL
    
    def is_baseball(self, match_data: Dict[str, Any]) -> bool:
        """Check if match is baseball."""
        return self.get_sport_type(match_data) == SportType.BASEBALL
    
    # ========== Complete Match Data Assembly ==========
    
    def get_complete_match_data(self, match_id: str) -> Optional[Dict[str, Any]]:
        """
        Get complete match data by combining all available sources.
        
        This method assembles data from:
        - target_games (basic match info)
        - sportic_contents (WDL data, H2H content)
        - sportic_sns (naver_text)
        - sportic_pick (betting info)
        - team_stats (for baseball only)
        """
        try:
            # Get basic match data
            match_data = self.get_match_data(match_id)
            if not match_data:
                return None
            
            # Get sportic content
            sportic_data = self.get_sportic_content(match_id)
            if sportic_data:
                match_data.update(sportic_data)
            
            # Get naver text
            naver_text = self.get_naver_text(match_id)
            if naver_text:
                match_data['naver_text'] = naver_text
            
            # Get betting picks
            betting_picks = self.get_betting_picks(match_id)
            if betting_picks:
                match_data['betting_picks'] = betting_picks
            
            # Get team stats if baseball
            if self.is_baseball(match_data):
                team_stats = self.get_team_stats(match_id)
                if team_stats:
                    match_data['team_stats'] = team_stats
            
            return match_data
            
        except Exception as e:
            logger.error(f"Error assembling complete match data for {match_id}: {e}")
            return None
    
    # ========== Utility Methods ==========
    
    def _is_within_creation_window(self, match: Dict[str, Any]) -> bool:
        """Check if match is within creation window for analysis."""
        try:
            sport = match.get('sports', '').lower()
            match_datetime_str = f"{match['match_date']} {match['match_time']}"
            match_datetime = datetime.strptime(match_datetime_str, "%Y-%m-%d %H:%M:%S")
            match_datetime = self.timezone.localize(match_datetime)
            
            now = datetime.now(self.timezone)
            
            # Baseball: 34 hours before to 10 minutes before
            if sport in {'baseball', '야구', 'bseball'}:
                start_window = match_datetime - timedelta(hours=34)
                end_window = match_datetime - timedelta(minutes=10)
                return start_window <= now <= end_window
            
            # Other sports: 24 hours before to 10 minutes before  
            elif sport in {'soccer', 'football', '축구', 'basketball', '농구', 'volleyball', '배구'}:
                start_window = match_datetime - timedelta(hours=24)
                end_window = match_datetime - timedelta(minutes=10)
                return start_window <= now <= end_window
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking creation window: {e}")
            return False
    
    def _get_team_name(self, team_id: str) -> str:
        """Get team name from team ID."""
        team_names = {
            "키움": "키움 히어로즈",
            "SSG": "SSG 랜더스", 
            "LG": "LG 트윈스",
            "KT": "KT 위즈",
            "삼성": "삼성 라이온즈",
            "롯데": "롯데 자이언츠",
            "두산": "두산 베어스",
            "KIA": "KIA 타이거즈",
            "한화": "한화 이글스",
            "NC": "NC 다이노스",
            # Soccer teams
            "울산": "울산 현대",
            "포항": "포항 스틸러스",
            "전북": "전북 현대 모터스",
            "수원FC": "수원 FC",
            "강원": "강원 FC",
            "제주": "제주 유나이티드",
            "인천": "인천 유나이티드",
            "대구": "대구 FC",
            "서울": "FC 서울",
            "김천": "김천 상무",
            "광주": "광주 FC",
            "수원": "수원 삼성 블루윙즈"
        }
        
        return team_names.get(team_id, team_id)
