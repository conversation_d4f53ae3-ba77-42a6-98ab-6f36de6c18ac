"""
Enhanced Database Repository for Baseball Analytics.

This module implements SOLID principles and provides access to the actual
Supabase database structure with proper data models and error handling.
"""

import logging
import pytz
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, ValidationError
from supabase import Client, create_client

from baseball_analytics.cag.context import DataRepository
from baseball_analytics.domain.models import (
    Match, Team, TeamStats, SportType, MatchStatus, AnalysisContext
)

logger = logging.getLogger(__name__)


# Enhanced Data Models for actual database structure
class BaseballMatchData(BaseModel):
    """Enhanced match data model matching actual database structure."""
    
    match_id: str = Field(..., description="Match identifier")
    sports: str = Field(..., description="Sport type")
    home_team: str = Field(..., description="Home team identifier")
    away_team: str = Field(..., description="Away team identifier")
    home_team_name: Optional[str] = Field(None, description="Home team name")
    away_team_name: Optional[str] = Field(None, description="Away team name")
    match_date: str = Field(..., description="Match date")
    match_time: str = Field(..., description="Match time")
    venue: Optional[str] = Field(None, description="Match venue")
    home_score: Optional[int] = Field(None, description="Home team score")
    away_score: Optional[int] = Field(None, description="Away team score")
    
    # Additional fields from sportic_contents
    h2h_content: Optional[Dict[str, Any]] = Field(None, description="Head-to-head content")
    h2h_wdl_summary: Optional[Dict[str, Any]] = Field(None, description="H2H WDL summary")
    home_wdl_summary: Optional[Dict[str, Any]] = Field(None, description="Home WDL summary")
    away_wdl_summary: Optional[Dict[str, Any]] = Field(None, description="Away WDL summary")
    h2h_wdl_matches: Optional[List[Dict[str, Any]]] = Field(None, description="H2H matches")
    home_wdl_matches: Optional[List[Dict[str, Any]]] = Field(None, description="Home matches")
    away_wdl_matches: Optional[List[Dict[str, Any]]] = Field(None, description="Away matches")


class BaseballTeamStatsData(BaseModel):
    """Enhanced team stats model matching actual database structure."""
    
    team_id: str = Field(..., description="Team identifier")
    match_id: str = Field(..., description="Match identifier")
    
    # Batting statistics
    runs: Optional[int] = Field(None, ge=0, description="Runs scored")
    hits: Optional[int] = Field(None, ge=0, description="Hits")
    errors: Optional[int] = Field(None, ge=0, description="Errors")
    batting_average: Optional[float] = Field(None, ge=0.0, le=1.0, description="Batting average")
    
    # Pitching statistics
    earned_runs: Optional[int] = Field(None, ge=0, description="Earned runs allowed")
    strikeouts: Optional[int] = Field(None, ge=0, description="Strikeouts")
    walks: Optional[int] = Field(None, ge=0, description="Walks allowed")
    era: Optional[float] = Field(None, ge=0.0, description="Earned run average")
    
    # Win-Draw-Loss record
    wins: Optional[int] = Field(None, ge=0, description="Number of wins")
    draws: Optional[int] = Field(None, ge=0, description="Number of draws")
    losses: Optional[int] = Field(None, ge=0, description="Number of losses")
    
    # Additional stats that might be in the database
    rbi: Optional[int] = Field(None, ge=0, description="Runs batted in")
    stolen_bases: Optional[int] = Field(None, ge=0, description="Stolen bases")
    home_runs: Optional[int] = Field(None, ge=0, description="Home runs")


class DatabaseConnectionManager:
    """Manages database connections with proper error handling and retry logic."""
    
    def __init__(self, url: str, key: str):
        self._url = url
        self._key = key
        self._client: Optional[Client] = None
        self._connection_attempts = 0
        self._max_attempts = 3
    
    def get_client(self) -> Client:
        """Get or create Supabase client with retry logic."""
        if self._client is None:
            self._connect()
        return self._client
    
    def _connect(self) -> None:
        """Establish connection to Supabase."""
        while self._connection_attempts < self._max_attempts:
            try:
                self._client = create_client(self._url, self._key)
                logger.info("Successfully connected to Supabase")
                return
            except Exception as e:
                self._connection_attempts += 1
                logger.error(f"Connection attempt {self._connection_attempts} failed: {e}")
                if self._connection_attempts >= self._max_attempts:
                    raise ConnectionError(f"Failed to connect after {self._max_attempts} attempts")
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            client = self.get_client()
            # Simple query to test connection
            response = client.table("target_games").select("match_id").limit(1).execute()
            return response is not None
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False


class EnhancedSupabaseRepository(DataRepository):
    """
    Enhanced Supabase repository implementation with SOLID principles.
    
    This repository provides access to the actual database structure
    and implements proper error handling and data validation.
    """
    
    def __init__(self, connection_manager: DatabaseConnectionManager):
        self._connection_manager = connection_manager
        self._timezone = pytz.timezone('Asia/Seoul')
    
    @property
    def client(self) -> Client:
        """Get Supabase client."""
        return self._connection_manager.get_client()
    
    def get_match_by_id(self, match_id: str) -> Optional[Match]:
        """Retrieve match by ID with enhanced data structure."""
        try:
            # Get basic match data
            match_data = self._get_target_game_data(match_id)
            if not match_data:
                logger.warning(f"Match not found: {match_id}")
                return None
            
            # Get additional sportic content
            sportic_data = self._get_sportic_content_data(match_id)
            
            # Merge data
            if sportic_data:
                match_data.update(sportic_data)
            
            # Convert to domain model
            return self._convert_to_match_domain(match_data)
            
        except Exception as e:
            logger.error(f"Error retrieving match {match_id}: {e}")
            return None
    
    def get_team_stats(self, match_id: str) -> List[TeamStats]:
        """Retrieve team statistics for a match."""
        try:
            response = self.client.table("team_stats").select("*").eq("match_id", match_id).execute()
            
            if not response.data:
                logger.info(f"No team stats found for match: {match_id}")
                return []
            
            stats_list = []
            for stats_data in response.data:
                try:
                    # Validate and convert data
                    validated_data = BaseballTeamStatsData(**stats_data)
                    domain_stats = self._convert_to_team_stats_domain(validated_data)
                    stats_list.append(domain_stats)
                except ValidationError as e:
                    logger.warning(f"Invalid team stats data for match {match_id}: {e}")
                    continue
            
            return stats_list
            
        except Exception as e:
            logger.error(f"Error retrieving team stats for match {match_id}: {e}")
            return []
    
    def get_team_by_id(self, team_id: str) -> Optional[Team]:
        """Retrieve team by ID."""
        # For now, create basic team from ID
        # In future, this could query a teams table
        return Team(
            id=team_id,
            name=team_id,
            short_name=team_id[:3].upper()
        )
    
    def get_historical_matches(
        self, 
        home_team_id: str, 
        away_team_id: str, 
        limit: int = 10
    ) -> List[Match]:
        """Retrieve historical matches between two teams."""
        try:
            # Query for matches between the two teams (both directions)
            response = self.client.table("target_games").select("*").or_(
                f"and(home_team.eq.{home_team_id},away_team.eq.{away_team_id}),"
                f"and(home_team.eq.{away_team_id},away_team.eq.{home_team_id})"
            ).order("match_date", desc=True).limit(limit).execute()
            
            matches = []
            for match_data in response.data:
                try:
                    match = self._convert_to_match_domain(match_data)
                    if match:
                        matches.append(match)
                except Exception as e:
                    logger.warning(f"Error converting historical match data: {e}")
                    continue
            
            return matches
            
        except Exception as e:
            logger.error(f"Error retrieving historical matches: {e}")
            return []
    
    def get_sns_target_matches(self) -> List[BaseballMatchData]:
        """Get target matches for SNS posting using RPC function."""
        try:
            response = self.client.rpc("get_sns_target_matches").execute()
            
            if not response.data:
                return []
            
            matches = []
            for row in response.data:
                try:
                    # Filter for baseball matches
                    sport = row.get("sports", "").lower()
                    if sport in {"baseball", "야구", "bseball"}:
                        # Check if within creation window
                        if self._is_within_creation_window(row):
                            validated_match = BaseballMatchData(**row)
                            matches.append(validated_match)
                except ValidationError as e:
                    logger.warning(f"Invalid match data: {e}")
                    continue
            
            return matches
            
        except Exception as e:
            logger.error(f"Error retrieving SNS target matches: {e}")
            return []
    
    def get_wdl_data(self, match_id: str, mode: str = 'h2h', data_type: str = 'summary') -> Optional[Dict[str, Any]]:
        """Get Win-Draw-Loss data for analysis."""
        field_name = f"{mode}_wdl_{data_type}"
        
        try:
            response = self.client.table('sportic_contents').select(field_name).eq('match_id', match_id).execute()
            
            if response.data and field_name in response.data[0]:
                return response.data[0][field_name]
            
            logger.info(f"No {field_name} data found for match: {match_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving {field_name} for match {match_id}: {e}")
            return None
    
    def _get_target_game_data(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get basic match data from target_games table."""
        try:
            response = self.client.table('target_games').select('*').eq('match_id', match_id).execute()
            
            if response.data:
                return response.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving target game data: {e}")
            return None
    
    def _get_sportic_content_data(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get additional content data from sportic_contents table."""
        try:
            fields = [
                'h2h_content', 'h2h_wdl_summary', 'home_wdl_summary',
                'away_wdl_summary', 'h2h_wdl_matches', 'home_wdl_matches',
                'away_wdl_matches'
            ]
            
            response = self.client.table('sportic_contents').select(','.join(fields)).eq('match_id', match_id).execute()
            
            if response.data:
                return response.data[0]
            return None

        except Exception as e:
            logger.error(f"Error retrieving sportic content data: {e}")
            return None

    def _convert_to_match_domain(self, data: Dict[str, Any]) -> Optional[Match]:
        """Convert database data to Match domain model."""
        try:
            # Create team objects
            home_team = Team(
                id=data.get("home_team", "unknown"),
                name=data.get("home_team_name", data.get("home_team", "Unknown")),
                short_name=data.get("home_team", "UNK")[:3].upper()
            )

            away_team = Team(
                id=data.get("away_team", "unknown"),
                name=data.get("away_team_name", data.get("away_team", "Unknown")),
                short_name=data.get("away_team", "UNK")[:3].upper()
            )

            # Parse match date and time
            match_date = self._parse_datetime(
                data.get("match_date"),
                data.get("match_time")
            )

            # Determine sport type
            sport_str = data.get("sports", "baseball").lower()
            sport = SportType.BASEBALL  # Default to baseball
            if sport_str in ["soccer", "football"]:
                sport = SportType.SOCCER
            elif sport_str == "basketball":
                sport = SportType.BASKETBALL
            elif sport_str == "volleyball":
                sport = SportType.VOLLEYBALL

            return Match(
                match_id=data["match_id"],
                sport=sport,
                home_team=home_team,
                away_team=away_team,
                match_date=match_date,
                status=MatchStatus.SCHEDULED,  # Default status
                venue=data.get("venue"),
                season=data.get("season"),
                home_score=data.get("home_score"),
                away_score=data.get("away_score")
            )

        except Exception as e:
            logger.error(f"Error converting match data: {e}")
            return None

    def _convert_to_team_stats_domain(self, data: BaseballTeamStatsData) -> TeamStats:
        """Convert validated data to TeamStats domain model."""
        return TeamStats(
            team_id=data.team_id,
            match_id=data.match_id,
            runs=data.runs,
            hits=data.hits,
            errors=data.errors,
            batting_average=data.batting_average,
            earned_runs=data.earned_runs,
            strikeouts=data.strikeouts,
            walks=data.walks,
            era=data.era,
            wins=data.wins,
            draws=data.draws,
            losses=data.losses
        )

    def _parse_datetime(self, date_str: str, time_str: str = None) -> datetime:
        """Parse date and time strings into datetime object with timezone."""
        try:
            if time_str:
                datetime_str = f"{date_str} {time_str}"
                dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            else:
                dt = datetime.strptime(date_str, "%Y-%m-%d")

            # Localize to Asia/Seoul timezone
            return self._timezone.localize(dt)

        except (ValueError, TypeError) as e:
            logger.warning(f"Error parsing datetime '{date_str} {time_str}': {e}")
            # Return current time as fallback
            return datetime.now(self._timezone)

    def _is_within_creation_window(self, match: Dict[str, Any]) -> bool:
        """Check if match is within creation window for analysis."""
        try:
            sport = match.get('sports', '').lower()
            match_datetime_str = f"{match['match_date']} {match['match_time']}"
            match_datetime = datetime.strptime(match_datetime_str, "%Y-%m-%d %H:%M:%S")
            match_datetime = self._timezone.localize(match_datetime)

            now = datetime.now(self._timezone)

            # Baseball: 34 hours before to 10 minutes before
            if sport in ['baseball', '야구', 'bseball']:
                from datetime import timedelta
                start_window = match_datetime - timedelta(hours=34)
                end_window = match_datetime - timedelta(minutes=10)
                return start_window <= now <= end_window

            # Other sports: 24 hours before to 10 minutes before
            elif sport in ['soccer', 'football', 'basketball', 'volleyball']:
                from datetime import timedelta
                start_window = match_datetime - timedelta(hours=24)
                end_window = match_datetime - timedelta(minutes=10)
                return start_window <= now <= end_window

            return False

        except Exception as e:
            logger.error(f"Error checking creation window: {e}")
            return False


class BaseballAnalysisRepository(EnhancedSupabaseRepository):
    """
    Specialized repository for baseball analysis with additional methods.

    This class extends the base repository with baseball-specific functionality
    including pitcher analysis, team performance metrics, and betting odds.
    """

    def get_pitcher_stats(self, match_id: str) -> Dict[str, Any]:
        """Get pitcher statistics for the match."""
        try:
            # This would query pitcher-specific data
            # For now, return placeholder structure
            return {
                "home_pitcher": {
                    "name": "박주성",
                    "era": 7.36,
                    "recent_innings": 14,
                    "recent_earned_runs": 11,
                    "season_record": {"wins": 0, "losses": 2}
                },
                "away_pitcher": {
                    "name": "박시후",
                    "era": 3.25,
                    "recent_innings": 27.67,
                    "recent_earned_runs": 10,
                    "season_record": {"wins": 2, "losses": 1}
                }
            }
        except Exception as e:
            logger.error(f"Error retrieving pitcher stats: {e}")
            return {}

    def get_betting_odds(self, match_id: str) -> Dict[str, Any]:
        """Get betting odds for the match."""
        try:
            # This would query betting odds data
            # For now, return placeholder structure
            return {
                "home_odds": 2.0,
                "away_odds": 1.8,
                "home_win_probability": 0.44,
                "away_win_probability": 0.56
            }
        except Exception as e:
            logger.error(f"Error retrieving betting odds: {e}")
            return {}

    def get_recent_form(self, team_id: str, games: int = 5) -> Dict[str, Any]:
        """Get recent form for a team."""
        try:
            # This would query recent match results
            # For now, return placeholder structure
            return {
                "recent_games": games,
                "wins": 2,
                "losses": 3,
                "avg_runs_scored": 3.2,
                "avg_runs_conceded": 4.8,
                "form_trend": "declining"
            }
        except Exception as e:
            logger.error(f"Error retrieving recent form for team {team_id}: {e}")
            return {}

    def get_head_to_head_record(self, home_team_id: str, away_team_id: str, games: int = 5) -> Dict[str, Any]:
        """Get head-to-head record between two teams."""
        try:
            historical_matches = self.get_historical_matches(home_team_id, away_team_id, games)

            home_wins = 0
            away_wins = 0
            total_runs = 0

            for match in historical_matches:
                if match.home_score is not None and match.away_score is not None:
                    total_runs += match.home_score + match.away_score

                    if match.home_team.id == home_team_id:
                        if match.home_score > match.away_score:
                            home_wins += 1
                        else:
                            away_wins += 1
                    else:
                        if match.away_score > match.home_score:
                            home_wins += 1
                        else:
                            away_wins += 1

            return {
                "total_games": len(historical_matches),
                "home_wins": home_wins,
                "away_wins": away_wins,
                "avg_total_runs": total_runs / len(historical_matches) if historical_matches else 0,
                "recent_trend": "home_favored" if home_wins > away_wins else "away_favored"
            }

        except Exception as e:
            logger.error(f"Error retrieving H2H record: {e}")
            return {}


class RepositoryFactory:
    """Factory for creating repository instances with proper configuration."""

    @staticmethod
    def create_baseball_repository(url: str, key: str) -> BaseballAnalysisRepository:
        """Create a baseball analysis repository."""
        connection_manager = DatabaseConnectionManager(url, key)
        return BaseballAnalysisRepository(connection_manager)

    @staticmethod
    def create_enhanced_repository(url: str, key: str) -> EnhancedSupabaseRepository:
        """Create an enhanced Supabase repository."""
        connection_manager = DatabaseConnectionManager(url, key)
        return EnhancedSupabaseRepository(connection_manager)
