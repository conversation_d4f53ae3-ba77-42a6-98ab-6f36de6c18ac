"""
Supabase MCP Repository for Real Baseball Analytics.

This module integrates with the actual Supabase database using MCP
and provides access to real data for baseball analysis.
"""

import logging
import pytz
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from supabase import Client, create_client
from pydantic import BaseModel, Field, ValidationError

from baseball_analytics.cag.context import DataRepository
from baseball_analytics.domain.models import (
    Match, Team, TeamStats, SportType, MatchStatus, AnalysisContext
)

logger = logging.getLogger(__name__)


class SupabaseMCPRepository(DataRepository):
    """
    Repository implementation using actual Supabase database with MCP integration.
    
    This class provides access to real data from the existing Supabase schema:
    - target_games: Match information
    - sportic_contents: Analysis content and WDL data
    - sportic_sns: Social media content
    - sportic_pick: Betting picks
    - team_stats: Team statistics (if exists)
    """
    
    def __init__(self, url: str, key: str):
        self.client = create_client(url, key)
        self.timezone = pytz.timezone('Asia/Seoul')
        logger.info("Supabase MCP Repository initialized")
    
    def get_match_by_id(self, match_id: str) -> Optional[Match]:
        """Retrieve match by ID from target_games table."""
        try:
            response = self.client.table("target_games").select("*").eq("match_id", match_id).execute()
            
            if not response.data:
                logger.warning(f"Match not found: {match_id}")
                return None
            
            match_data = response.data[0]
            return self._convert_to_match_domain(match_data)
            
        except Exception as e:
            logger.error(f"Error retrieving match {match_id}: {e}")
            return None
    
    def get_team_stats(self, match_id: str) -> List[TeamStats]:
        """Retrieve team statistics for a match."""
        try:
            # First try team_stats table if it exists
            try:
                response = self.client.table("team_stats").select("*").eq("match_id", match_id).execute()
                if response.data:
                    return self._convert_team_stats_data(response.data)
            except:
                pass  # Table might not exist
            
            # Fallback: create basic stats from match data
            match = self.get_match_by_id(match_id)
            if not match:
                return []
            
            # Create basic team stats
            stats = []
            if match.home_score is not None:
                stats.append(TeamStats(
                    team_id=match.home_team.id,
                    match_id=match_id,
                    runs=match.home_score,
                    hits=None,
                    errors=None
                ))
            
            if match.away_score is not None:
                stats.append(TeamStats(
                    team_id=match.away_team.id,
                    match_id=match_id,
                    runs=match.away_score,
                    hits=None,
                    errors=None
                ))
            
            return stats
            
        except Exception as e:
            logger.error(f"Error retrieving team stats for match {match_id}: {e}")
            return []
    
    def get_team_by_id(self, team_id: str) -> Optional[Team]:
        """Retrieve team by ID."""
        # Create basic team from ID (could be enhanced with teams table)
        return Team(
            id=team_id,
            name=self._get_team_name(team_id),
            short_name=team_id[:3].upper()
        )
    
    def get_historical_matches(
        self, 
        home_team_id: str, 
        away_team_id: str, 
        limit: int = 10
    ) -> List[Match]:
        """Retrieve historical matches between two teams."""
        try:
            # Query for matches between the two teams (both directions)
            response = self.client.table("target_games").select("*").or_(
                f"and(home_team.eq.{home_team_id},away_team.eq.{away_team_id}),"
                f"and(home_team.eq.{away_team_id},away_team.eq.{home_team_id})"
            ).order("match_date", desc=True).limit(limit).execute()
            
            matches = []
            for match_data in response.data:
                try:
                    match = self._convert_to_match_domain(match_data)
                    if match:
                        matches.append(match)
                except Exception as e:
                    logger.warning(f"Error converting historical match data: {e}")
                    continue
            
            return matches
            
        except Exception as e:
            logger.error(f"Error retrieving historical matches: {e}")
            return []
    
    def get_sns_target_matches(self) -> List[Dict[str, Any]]:
        """Get target matches for SNS posting using RPC function."""
        try:
            # Use the existing RPC function
            response = self.client.rpc("get_sns_target_matches").execute()
            
            if not response.data:
                return []
            
            # Filter for baseball matches within creation window
            baseball_matches = []
            for match_data in response.data:
                sport = match_data.get("sports", "").lower()
                if sport in {"baseball", "야구", "bseball"}:
                    if self._is_within_creation_window(match_data):
                        baseball_matches.append(match_data)
            
            return baseball_matches
            
        except Exception as e:
            logger.error(f"Error retrieving SNS target matches: {e}")
            return []
    
    def get_wdl_data(self, match_id: str, mode: str = 'h2h', data_type: str = 'summary') -> Optional[Dict[str, Any]]:
        """Get Win-Draw-Loss data from sportic_contents table."""
        field_name = f"{mode}_wdl_{data_type}"
        
        try:
            response = self.client.table('sportic_contents').select(field_name).eq('match_id', match_id).execute()
            
            if response.data and field_name in response.data[0]:
                return response.data[0][field_name]
            
            logger.info(f"No {field_name} data found for match: {match_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving {field_name} for match {match_id}: {e}")
            return None
    
    def get_h2h_content(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get head-to-head content from sportic_contents table."""
        try:
            response = self.client.table('sportic_contents').select('h2h_content').eq('match_id', match_id).execute()
            
            if response.data and 'h2h_content' in response.data[0]:
                return response.data[0]['h2h_content']
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving h2h_content for match {match_id}: {e}")
            return None
    
    def get_naver_text(self, match_id: str) -> Optional[str]:
        """Get naver text from sportic_sns table."""
        try:
            response = self.client.table('sportic_sns').select('naver_text').eq('match_id', match_id).execute()
            
            if response.data and 'naver_text' in response.data[0]:
                naver_text = response.data[0]['naver_text']
                if isinstance(naver_text, dict) and 'ko' in naver_text:
                    return naver_text['ko']
                return str(naver_text)
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving naver_text for match {match_id}: {e}")
            return None
    
    def get_betting_picks(self, match_id: str) -> Optional[Dict[str, Any]]:
        """Get betting picks from sportic_pick table."""
        try:
            response = self.client.table('sportic_pick').select('*').eq('match_id', match_id).execute()
            
            if response.data:
                return response.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving betting picks for match {match_id}: {e}")
            return None
    
    def _convert_to_match_domain(self, data: Dict[str, Any]) -> Optional[Match]:
        """Convert database data to Match domain model."""
        try:
            # Create team objects
            home_team = Team(
                id=data.get("home_team", "unknown"),
                name=self._get_team_name(data.get("home_team", "unknown")),
                short_name=data.get("home_team", "UNK")[:3].upper()
            )
            
            away_team = Team(
                id=data.get("away_team", "unknown"),
                name=self._get_team_name(data.get("away_team", "unknown")),
                short_name=data.get("away_team", "UNK")[:3].upper()
            )
            
            # Parse match date and time
            match_date = self._parse_datetime(
                data.get("match_date"),
                data.get("match_time")
            )
            
            # Determine sport type
            sport_str = data.get("sports", "baseball").lower()
            sport = SportType.BASEBALL  # Default to baseball
            if sport_str in ["soccer", "football"]:
                sport = SportType.SOCCER
            elif sport_str == "basketball":
                sport = SportType.BASKETBALL
            elif sport_str == "volleyball":
                sport = SportType.VOLLEYBALL
            
            return Match(
                match_id=data["match_id"],
                sport=sport,
                home_team=home_team,
                away_team=away_team,
                match_date=match_date,
                status=MatchStatus.SCHEDULED,  # Default status
                venue=data.get("venue"),
                season=data.get("season"),
                home_score=data.get("home_score"),
                away_score=data.get("away_score")
            )
            
        except Exception as e:
            logger.error(f"Error converting match data: {e}")
            return None
    
    def _convert_team_stats_data(self, data_list: List[Dict[str, Any]]) -> List[TeamStats]:
        """Convert team stats data to domain models."""
        stats_list = []
        for data in data_list:
            try:
                stats = TeamStats(
                    team_id=data["team_id"],
                    match_id=data["match_id"],
                    runs=data.get("runs"),
                    hits=data.get("hits"),
                    errors=data.get("errors"),
                    batting_average=data.get("batting_average"),
                    earned_runs=data.get("earned_runs"),
                    strikeouts=data.get("strikeouts"),
                    walks=data.get("walks"),
                    era=data.get("era"),
                    wins=data.get("wins"),
                    draws=data.get("draws"),
                    losses=data.get("losses")
                )
                stats_list.append(stats)
            except ValidationError as e:
                logger.warning(f"Invalid team stats data: {e}")
                continue
        
        return stats_list
    
    def _parse_datetime(self, date_str: str, time_str: str = None) -> datetime:
        """Parse date and time strings into datetime object with timezone."""
        try:
            if time_str:
                datetime_str = f"{date_str} {time_str}"
                dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            else:
                dt = datetime.strptime(date_str, "%Y-%m-%d")
            
            # Localize to Asia/Seoul timezone
            return self.timezone.localize(dt)
            
        except (ValueError, TypeError) as e:
            logger.warning(f"Error parsing datetime '{date_str} {time_str}': {e}")
            # Return current time as fallback
            return datetime.now(self.timezone)
    
    def _is_within_creation_window(self, match: Dict[str, Any]) -> bool:
        """Check if match is within creation window for analysis."""
        try:
            sport = match.get('sports', '').lower()
            match_datetime_str = f"{match['match_date']} {match['match_time']}"
            match_datetime = datetime.strptime(match_datetime_str, "%Y-%m-%d %H:%M:%S")
            match_datetime = self.timezone.localize(match_datetime)
            
            now = datetime.now(self.timezone)
            
            # Baseball: 34 hours before to 10 minutes before
            if sport in ['baseball', '야구', 'bseball']:
                start_window = match_datetime - timedelta(hours=34)
                end_window = match_datetime - timedelta(minutes=10)
                return start_window <= now <= end_window
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking creation window: {e}")
            return False
    
    def _get_team_name(self, team_id: str) -> str:
        """Get team name from team ID."""
        # Korean baseball team mappings
        team_names = {
            "키움": "키움 히어로즈",
            "SSG": "SSG 랜더스",
            "LG": "LG 트윈스",
            "KT": "KT 위즈",
            "삼성": "삼성 라이온즈",
            "롯데": "롯데 자이언츠",
            "두산": "두산 베어스",
            "KIA": "KIA 타이거즈",
            "한화": "한화 이글스",
            "NC": "NC 다이노스"
        }
        
        return team_names.get(team_id, team_id)
