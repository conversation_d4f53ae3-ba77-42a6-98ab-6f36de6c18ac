"""
Enhanced Database infrastructure layer implementing repository pattern.

This module provides data access implementations following SOLID principles
and integrates with the actual Supabase database structure for baseball analytics.
"""

import logging
import pytz
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Protocol

from pydantic import ValidationError
from supabase import Client, create_client

from baseball_analytics.cag.context import DataRepository
from baseball_analytics.domain.models import (Match, MatchStatus, SportType,
                                              Team, TeamStats, AnalysisContext)
from baseball_analytics.infrastructure.config import get_settings

logger = logging.getLogger(__name__)


class DatabaseConnection(ABC):
    """Abstract database connection interface."""
    
    @abstractmethod
    def connect(self) -> Any:
        """Establish database connection."""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """Close database connection."""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """Check if connection is active."""
        pass


class SupabaseConnection(DatabaseConnection):
    """Supabase database connection implementation."""
    
    def __init__(self, url: str, key: str):
        self._url = url
        self._key = key
        self._client: Optional[Client] = None
    
    def connect(self) -> Client:
        """Establish Supabase connection."""
        if not self._client:
            self._client = create_client(self._url, self._key)
        return self._client
    
    def disconnect(self) -> None:
        """Close Supabase connection."""
        # Supabase client doesn't require explicit disconnection
        self._client = None
    
    def is_connected(self) -> bool:
        """Check if connection is active."""
        return self._client is not None
    
    @property
    def client(self) -> Client:
        """Get the Supabase client."""
        if not self._client:
            self.connect()
        return self._client


class SupabaseRepository(DataRepository):
    """
    Supabase implementation of the data repository.
    
    This class implements the Repository pattern and follows the
    Dependency Inversion Principle by depending on abstractions.
    """
    
    def __init__(self, connection: SupabaseConnection):
        self._connection = connection
    
    def get_match_by_id(self, match_id: str) -> Optional[Match]:
        """Retrieve match by ID from team_stats table."""
        try:
            client = self._connection.client
            
            # Query team_stats table to get match info
            response = client.table("team_stats").select("*").eq("match_id", match_id).execute()
            
            if not response.data:
                logger.warning(f"Match not found in team_stats: {match_id}")
                return None
            
            # Group teams by home/away
            home_team_data = None
            away_team_data = None
            
            for team_data in response.data:
                if team_data.get("team_role") == "home":
                    home_team_data = team_data
                elif team_data.get("team_role") == "away":
                    away_team_data = team_data
            
            if not home_team_data or not away_team_data:
                logger.warning(f"Incomplete team data for match: {match_id}")
                return None
            
            # Convert to domain model using first record for match details
            match_data = {
                "match_id": match_id,
                "sports": home_team_data.get("sports"),
                "league": home_team_data.get("league"),
                "match_date": home_team_data.get("match_date"),
                "match_time": home_team_data.get("match_time"),
                "home_team": home_team_data.get("team_id"),
                "home_team_name": home_team_data.get("team_name"),
                "away_team": away_team_data.get("team_id"),
                "away_team_name": away_team_data.get("team_name")
            }
            
            return self._convert_to_match(match_data)
            
        except Exception as e:
            logger.error(f"Error retrieving match {match_id}: {e}")
            return None
    
    def get_team_stats(self, match_id: str) -> List[TeamStats]:
        """Retrieve team statistics for a match."""
        try:
            client = self._connection.client
            
            response = client.table("team_stats").select("*").eq("match_id", match_id).execute()
            
            if not response.data:
                return []
            
            stats_list = []
            for stats_data in response.data:
                try:
                    stats = self._convert_to_team_stats(stats_data)
                    stats_list.append(stats)
                except ValidationError as e:
                    logger.warning(f"Invalid team stats data: {e}")
                    continue
            
            return stats_list
            
        except Exception as e:
            logger.error(f"Error retrieving team stats for match {match_id}: {e}")
            return []
    
    def get_team_by_id(self, team_id: str) -> Optional[Team]:
        """Retrieve team by ID."""
        try:
            client = self._connection.client
            
            # This would require a teams table in the database
            # For now, we'll create a basic team from available data
            return Team(
                id=team_id,
                name=team_id,  # Placeholder
                short_name=team_id[:3].upper()
            )
            
        except Exception as e:
            logger.error(f"Error retrieving team {team_id}: {e}")
            return None
    
    def get_historical_matches(
        self, 
        home_team_id: str, 
        away_team_id: str, 
        limit: int = 10
    ) -> List[Match]:
        """Retrieve historical matches between two teams."""
        try:
            client = self._connection.client
            
            # Query for matches between the two teams from team_stats
            response = client.table("team_stats").select("*").or_(
                f"team_id.eq.{home_team_id},team_id.eq.{away_team_id}"
            ).order("match_date", desc=True).limit(limit).execute()
            
            matches = []
            for match_data in response.data:
                try:
                    # Get match details for each match_id
                    match = self.get_match_by_id(match_data["match_id"])
                    if match:
                        matches.append(match)
                except ValidationError as e:
                    logger.warning(f"Invalid match data: {e}")
                    continue
            
            return matches
            
        except Exception as e:
            logger.error(f"Error retrieving historical matches: {e}")
            return []

    def get_available_matches(self, limit: int = 50) -> List[str]:
        """Get available matches using RPC function."""
        try:
            client = self._connection.client
            
            # Use RPC function to get target matches with filters
            logger.info("Calling get_target_matches() RPC function")
            response = client.rpc("get_target_matches").execute()
            
            logger.info(f"RPC response: {len(response.data) if response.data else 0} matches found")
            
            if not response.data:
                logger.warning("No target matches found from RPC")
                return []
            
            # Filter for baseball and game_type='W'
            baseball_matches = []
            for match in response.data:
                if (match.get("sports") == "baseball" and 
                    match.get("game_type") == "W"):
                    baseball_matches.append(match["match_id"])
            
            logger.info(f"Found {len(baseball_matches)} baseball matches with game_type='W'")
            
            # Return limited results
            result = baseball_matches[:limit]
            logger.info(f"Returning {len(result)} matches")
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting available matches: {e}")
            return []
    
    def _convert_to_match(self, data: Dict[str, Any]) -> Match:
        """Convert database data to Match domain model."""
        # Create team objects
        home_team = Team(
            id=data.get("home_team", "unknown"),
            name=data.get("home_team_name", data.get("home_team", "Unknown")),
            short_name=data.get("home_team", "UNK")[:3].upper()
        )
        
        away_team = Team(
            id=data.get("away_team", "unknown"),
            name=data.get("away_team_name", data.get("away_team", "Unknown")),
            short_name=data.get("away_team", "UNK")[:3].upper()
        )
        
        # Parse match date
        match_date = self._parse_datetime(
            data.get("match_date"),
            data.get("match_time")
        )
        
        # Determine sport type
        sport_str = data.get("sports", "baseball").lower()
        sport = SportType.BASEBALL  # Default to baseball
        if sport_str in ["soccer", "football"]:
            sport = SportType.SOCCER
        elif sport_str == "basketball":
            sport = SportType.BASKETBALL
        elif sport_str == "volleyball":
            sport = SportType.VOLLEYBALL
        
        return Match(
            match_id=data["match_id"],
            sport=sport,
            home_team=home_team,
            away_team=away_team,
            match_date=match_date,
            status=MatchStatus.SCHEDULED,  # Default status
            venue=data.get("venue"),
            season=data.get("season"),
            home_score=data.get("home_score"),
            away_score=data.get("away_score")
        )
    
    def _convert_to_team_stats(self, data: Dict[str, Any]) -> TeamStats:
        """Convert database data to TeamStats domain model."""
        return TeamStats(
            team_id=data["team_id"],
            match_id=data["match_id"],
            runs=data.get("runs"),
            hits=data.get("hits"),
            errors=data.get("errors"),
            batting_average=data.get("batting_average"),
            earned_runs=data.get("earned_runs"),
            strikeouts=data.get("strikeouts"),
            walks=data.get("walks"),
            era=data.get("era"),
            wins=data.get("wins"),
            draws=data.get("draws"),
            losses=data.get("losses")
        )
    
    def _parse_datetime(self, date_str: str, time_str: str = None) -> datetime:
        """Parse date and time strings into datetime object."""
        try:
            if time_str:
                datetime_str = f"{date_str} {time_str}"
                return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            else:
                return datetime.strptime(date_str, "%Y-%m-%d")
        except (ValueError, TypeError):
            # Return current time as fallback
            return datetime.now()


class CacheRepository(DataRepository):
    """
    Caching decorator for repository implementations.
    
    This implements the Decorator pattern to add caching functionality
    to any repository implementation.
    """
    
    def __init__(self, repository: DataRepository, cache_ttl: int = 300):
        self._repository = repository
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self._cache_ttl = cache_ttl
    
    def get_match_by_id(self, match_id: str) -> Optional[Match]:
        """Get match with caching."""
        cache_key = f"match_{match_id}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        match = self._repository.get_match_by_id(match_id)
        self._cache[cache_key] = match
        self._cache_timestamps[cache_key] = datetime.now()
        
        return match
    
    def get_team_stats(self, match_id: str) -> List[TeamStats]:
        """Get team stats with caching."""
        cache_key = f"team_stats_{match_id}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        stats = self._repository.get_team_stats(match_id)
        self._cache[cache_key] = stats
        self._cache_timestamps[cache_key] = datetime.now()
        
        return stats
    
    def get_team_by_id(self, team_id: str) -> Optional[Team]:
        """Get team with caching."""
        cache_key = f"team_{team_id}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        team = self._repository.get_team_by_id(team_id)
        self._cache[cache_key] = team
        self._cache_timestamps[cache_key] = datetime.now()
        
        return team
    
    def get_historical_matches(
        self, 
        home_team_id: str, 
        away_team_id: str, 
        limit: int = 10
    ) -> List[Match]:
        """Get historical matches with caching."""
        cache_key = f"historical_{home_team_id}_{away_team_id}_{limit}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        matches = self._repository.get_historical_matches(home_team_id, away_team_id, limit)
        self._cache[cache_key] = matches
        self._cache_timestamps[cache_key] = datetime.now()
        
        return matches
    
    def get_available_matches(self, limit: int = 50) -> List[str]:
        """Get available matches with caching."""
        cache_key = f"available_matches_{limit}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        matches = self._repository.get_available_matches(limit)
        self._cache[cache_key] = matches
        self._cache_timestamps[cache_key] = datetime.now()
        
        return matches
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self._cache:
            return False
        
        timestamp = self._cache_timestamps.get(cache_key)
        if not timestamp:
            return False
        
        age = (datetime.now() - timestamp).total_seconds()
        return age < self._cache_ttl
    
    def clear_cache(self) -> None:
        """Clear all cached data."""
        self._cache.clear()
        self._cache_timestamps.clear()
