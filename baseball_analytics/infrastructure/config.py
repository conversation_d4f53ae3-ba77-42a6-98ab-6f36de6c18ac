"""
Configuration management for the baseball analytics platform.

This module provides centralized configuration management using Pydantic.
"""

from pathlib import Path
from typing import List, Optional

from pydantic import Field, field_validator

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    supabase_url: str = Field(
        default="https://ozdeoipuyiadspcxlvnb.supabase.co",
        description="Supabase project URL"
    )
    supabase_key: str = Field(
        default="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.-P8Od96nAjlOBGyvnhEr0YAWQDegcI1eDCBa1AGulnM",
        description="Supabase API key"
    )
    cache_ttl: int = Field(300, description="Cache TTL in seconds")
    
    class Config:
        env_prefix = "DB_"
        case_sensitive = False


class LLMSettings(BaseSettings):
    """LLM provider configuration settings."""
    
    openai_api_key: Optional[str] = Field(
        None, description="OpenAI API key"
    )
    google_api_key: Optional[str] = Field(
        None, description="Google API key"
    )
    anthropic_api_key: Optional[str] = Field(
        None, description="Anthropic API key"
    )
    
    default_provider: str = Field(
        "openai", description="Default LLM provider"
    )
    default_model: str = Field(
        "gpt-4o-mini", description="Default model name"
    )
    default_temperature: float = Field(
        0.3, ge=0.0, le=2.0, description="Default temperature"
    )
    default_max_tokens: Optional[int] = Field(
        None, gt=0, description="Default max tokens"
    )
    
    class Config:
        env_prefix = "LLM_"
        case_sensitive = False


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""
    
    level: str = Field("INFO", description="Logging level")
    format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    file_enabled: bool = Field(True, description="Enable file logging")
    file_path: str = Field(
        "logs/baseball_analytics.log", description="Log file path"
    )
    file_max_size: int = Field(
        10 * 1024 * 1024, description="Max log file size in bytes"
    )
    file_backup_count: int = Field(
        5, description="Number of backup log files"
    )
    console_enabled: bool = Field(
        True, description="Enable console logging"
    )
    
    class Config:
        env_prefix = "LOG_"
        case_sensitive = False
    
    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(
                f"Invalid log level. Must be one of: {valid_levels}"
            )
        return v.upper()


class AnalysisSettings(BaseSettings):
    """Analysis configuration settings."""
    
    max_historical_matches: int = Field(
        10, ge=1, description="Max historical matches to analyze"
    )
    confidence_threshold: float = Field(
        0.5, ge=0.0, le=1.0, description="Minimum confidence threshold"
    )
    analysis_timeout: int = Field(
        60, gt=0, description="Analysis timeout in seconds"
    )
    enable_caching: bool = Field(
        True, description="Enable analysis result caching"
    )
    
    class Config:
        env_prefix = "ANALYSIS_"
        case_sensitive = False


class ContentSettings(BaseSettings):
    """Content generation configuration settings."""
    
    default_content_type: str = Field(
        "social_media_post", description="Default content type"
    )
    default_tone: str = Field(
        "professional", description="Default content tone"
    )
    default_audience: str = Field(
        "general", description="Default target audience"
    )
    max_hashtags: int = Field(
        8, ge=1, description="Maximum number of hashtags"
    )
    enable_content_caching: bool = Field(
        True, description="Enable content caching"
    )
    
    class Config:
        env_prefix = "CONTENT_"
        case_sensitive = False


class SecuritySettings(BaseSettings):
    """Security configuration settings."""
    
    api_key_header: str = Field(
        "X-API-Key", description="API key header name"
    )
    rate_limit_requests: int = Field(
        100, gt=0, description="Rate limit requests per minute"
    )
    rate_limit_window: int = Field(
        60, gt=0, description="Rate limit window in seconds"
    )
    cors_origins: List[str] = Field(
        ["*"], description="CORS allowed origins"
    )
    
    class Config:
        env_prefix = "SECURITY_"
        case_sensitive = False


class ApplicationSettings(BaseSettings):
    """Main application configuration."""
    
    app_name: str = Field(
        "Baseball Analytics Platform", description="Application name"
    )
    app_version: str = Field(
        "2.0.0", description="Application version"
    )
    debug: bool = Field(False, description="Debug mode")
    environment: str = Field("production", description="Environment name")
    
    # Sub-configurations
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    llm: LLMSettings = Field(default_factory=LLMSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    analysis: AnalysisSettings = Field(default_factory=AnalysisSettings)
    content: ContentSettings = Field(default_factory=ContentSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    
    class Config:
        env_prefix = "APP_"
        case_sensitive = False
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        valid_envs = ['development', 'testing', 'staging', 'production']
        if v.lower() not in valid_envs:
            raise ValueError(
                f"Invalid environment. Must be one of: {valid_envs}"
            )
        return v.lower()


def load_settings(env_file: Optional[str] = None) -> ApplicationSettings:
    """
    Load application settings from environment variables and .env file.
    
    Args:
        env_file: Path to .env file (optional)
        
    Returns:
        ApplicationSettings: Loaded configuration
    """
    if env_file:
        return ApplicationSettings(_env_file=env_file)
    else:
        return ApplicationSettings()


def create_directories(settings: ApplicationSettings) -> None:
    """
    Create necessary directories based on configuration.
    
    Args:
        settings: Application settings
    """
    # Create log directory
    if settings.logging.file_enabled:
        log_path = Path(settings.logging.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)


def validate_settings(settings: ApplicationSettings) -> List[str]:
    """
    Validate application settings and return list of issues.
    
    Args:
        settings: Application settings to validate
        
    Returns:
        List[str]: List of validation issues (empty if valid)
    """
    issues = []
    
    # Check required API keys
    if not settings.database.supabase_url:
        issues.append("Database URL is required")
    
    if not settings.database.supabase_key:
        issues.append("Database key is required")
    
    # LLM configuration is optional - skip validation for now
    
    return issues


# Global settings instance
_settings: Optional[ApplicationSettings] = None


def get_settings() -> ApplicationSettings:
    """
    Get the global settings instance.
    
    Returns:
        ApplicationSettings: Global settings
    """
    global _settings
    if _settings is None:
        _settings = load_settings()
    return _settings


def set_settings(settings: ApplicationSettings) -> None:
    """
    Set the global settings instance.
    
    Args:
        settings: Settings to set as global
    """
    global _settings
    _settings = settings


def reload_settings(env_file: Optional[str] = None) -> ApplicationSettings:
    """
    Reload settings from environment.
    
    Args:
        env_file: Path to .env file (optional)
        
    Returns:
        ApplicationSettings: Reloaded settings
    """
    global _settings
    _settings = load_settings(env_file)
    return _settings
