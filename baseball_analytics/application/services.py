"""
Application services implementing use cases for baseball analytics.

This module contains the application layer services that orchestrate
the domain logic and coordinate between different layers.
"""

import logging
from datetime import datetime
from typing import List, Optional

from baseball_analytics.cag.analysis import AnalysisEngine
from baseball_analytics.cag.context import ContextManager, DataRepository
from baseball_analytics.cag.generation import ContentGenerationEngine
from baseball_analytics.domain.models import (AnalysisContext, AnalysisResult,
                                              ContentGenerationRequest,
                                              ContentTone, ContentType,
                                              GeneratedContent)
from baseball_analytics.infrastructure.llm_providers import LLMManager

logger = logging.getLogger(__name__)


class BaseballAnalysisService:
    """
    Main service for baseball analysis operations.
    
    This service implements the Application Service pattern and coordinates
    between the CAG layers to provide complete analysis functionality.
    """
    
    def __init__(
        self,
        context_manager: ContextManager,
        analysis_engine: AnalysisEngine,
        content_engine: ContentGenerationEngine
    ):
        self._context_manager = context_manager
        self._analysis_engine = analysis_engine
        self._content_engine = content_engine
    
    def analyze_match(self, match_id: str) -> AnalysisResult:
        """
        Perform complete analysis for a baseball match.
        
        Args:
            match_id: The match identifier
            
        Returns:
            AnalysisResult: Complete analysis result
            
        Raises:
            ValueError: If match is not found or analysis fails
        """
        try:
            logger.info(f"Starting analysis for match: {match_id}")
            
            # Step 1: Collect context
            context = self._context_manager.get_analysis_context(match_id)
            
            # Step 2: Validate context
            if not self._context_manager.validate_context(context):
                raise ValueError(f"Invalid context for match: {match_id}")
            
            # Step 3: Perform analysis
            result = self._analysis_engine.analyze(context)
            
            logger.info(f"Analysis completed for match: {match_id}")
            return result
            
        except Exception as e:
            logger.error(f"Analysis failed for match {match_id}: {e}")
            raise ValueError(f"Failed to analyze match {match_id}: {e}")
    
    def generate_content(
        self,
        analysis_result: AnalysisResult,
        content_type: str,
        target_audience: str = "general",
        tone: str = "professional",
        max_length: Optional[int] = None,
        include_hashtags: bool = True
    ) -> GeneratedContent:
        """
        Generate content based on analysis results.
        
        Args:
            analysis_result: The analysis result to base content on
            content_type: Type of content to generate
            target_audience: Target audience for the content
            tone: Content tone
            max_length: Maximum content length
            include_hashtags: Whether to include hashtags
            
        Returns:
            GeneratedContent: Generated content
            
        Raises:
            ValueError: If content generation fails
        """
        try:
            logger.info(f"Generating {content_type} content for match: {analysis_result.match_id}")
            
            request = ContentGenerationRequest(
                analysis_result=analysis_result,
                content_type=content_type,
                target_audience=target_audience,
                tone=tone,
                max_length=max_length,
                include_hashtags=include_hashtags
            )
            
            content = self._content_engine.generate_content(request)
            
            logger.info(f"Content generation completed for match: {analysis_result.match_id}")
            return content
            
        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            raise ValueError(f"Failed to generate content: {e}")
    
    def analyze_and_generate_content(
        self,
        match_id: str,
        content_type: str = ContentType.SOCIAL_MEDIA_POST,
        **content_kwargs
    ) -> tuple[AnalysisResult, GeneratedContent]:
        """
        Perform analysis and generate content in one operation.
        
        Args:
            match_id: The match identifier
            content_type: Type of content to generate
            **content_kwargs: Additional content generation parameters
            
        Returns:
            tuple: (AnalysisResult, GeneratedContent)
        """
        # Perform analysis
        analysis_result = self.analyze_match(match_id)
        
        # Generate content
        content = self.generate_content(
            analysis_result,
            content_type,
            **content_kwargs
        )
        
        return analysis_result, content


class MatchSchedulingService:
    """Service for managing match scheduling and analysis timing."""
    
    def __init__(self, repository: DataRepository):
        self._repository = repository
    
    def get_upcoming_matches(self, days_ahead: int = 7) -> List[str]:
        """
        Get list of available match IDs for analysis.
        
        Args:
            days_ahead: Number of days to look ahead (not used currently)
            
        Returns:
            List[str]: List of available match IDs
        """
        # Get available matches from target_games
        try:
            logger.info("Getting available matches from repository")
            matches = self._repository.get_available_matches(50)
            logger.info(f"Found {len(matches)} matches from repository")
            return matches
        except AttributeError:
            logger.warning("Repository doesn't support get_available_matches")
            return []
        except Exception as e:
            logger.error(f"Error getting available matches: {e}")
            return []
    
    def is_analysis_time(self, match_id: str) -> bool:
        """
        Check if it's the right time to analyze a match.
        
        Args:
            match_id: The match identifier
            
        Returns:
            bool: True if analysis should be performed
        """
        match = self._repository.get_match_by_id(match_id)
        if not match:
            return False
        
        # Implement timing logic based on match date
        # For baseball: analyze 16 hours before to 10 minutes before
        now = datetime.now()
        time_until_match = (match.match_date - now).total_seconds()
        
        # 16 hours = 57600 seconds, 10 minutes = 600 seconds
        return 600 <= time_until_match <= 57600


class ContentPublishingService:
    """Service for managing content publishing to different platforms."""
    
    def __init__(self):
        self._publishers = {}
    
    def register_publisher(self, platform: str, publisher) -> None:
        """Register a content publisher for a platform."""
        self._publishers[platform] = publisher
    
    def publish_content(self, content: GeneratedContent, platforms: List[str]) -> dict:
        """
        Publish content to specified platforms.
        
        Args:
            content: The content to publish
            platforms: List of platform names
            
        Returns:
            dict: Publishing results by platform
        """
        results = {}
        
        for platform in platforms:
            if platform in self._publishers:
                try:
                    publisher = self._publishers[platform]
                    result = publisher.publish(content)
                    results[platform] = {"success": True, "result": result}
                except Exception as e:
                    results[platform] = {"success": False, "error": str(e)}
            else:
                results[platform] = {"success": False, "error": "Publisher not registered"}
        
        return results


class AnalyticsService:
    """Service for tracking and analyzing system performance."""
    
    def __init__(self):
        self._metrics = {}
    
    def track_analysis_performance(self, match_id: str, duration: float, success: bool) -> None:
        """Track analysis performance metrics."""
        if "analysis" not in self._metrics:
            self._metrics["analysis"] = []
        
        self._metrics["analysis"].append({
            "match_id": match_id,
            "duration": duration,
            "success": success,
            "timestamp": datetime.now()
        })
    
    def track_content_generation(self, content_type: str, duration: float, success: bool) -> None:
        """Track content generation metrics."""
        if "content_generation" not in self._metrics:
            self._metrics["content_generation"] = []
        
        self._metrics["content_generation"].append({
            "content_type": content_type,
            "duration": duration,
            "success": success,
            "timestamp": datetime.now()
        })
    
    def get_performance_summary(self) -> dict:
        """Get performance summary statistics."""
        summary = {}
        
        for metric_type, metrics in self._metrics.items():
            if metrics:
                total_count = len(metrics)
                success_count = sum(1 for m in metrics if m["success"])
                avg_duration = sum(m["duration"] for m in metrics) / total_count
                
                summary[metric_type] = {
                    "total_count": total_count,
                    "success_rate": success_count / total_count,
                    "average_duration": avg_duration
                }
        
        return summary


class ApplicationServiceFactory:
    """Factory for creating application services with proper dependencies."""
    
    @staticmethod
    def create_analysis_service(
        repository: DataRepository,
        llm_manager: LLMManager
    ) -> BaseballAnalysisService:
        """Create a complete baseball analysis service."""
        from baseball_analytics.cag.analysis import AnalysisEngineFactory
        from baseball_analytics.cag.context import ContextManager
        from baseball_analytics.cag.generation import ContentGenerationFactory

        # Create context manager
        context_manager = ContextManager(repository)
        
        # Create analysis engine
        llm = llm_manager.get_model()
        analysis_engine = AnalysisEngineFactory.create_langchain_engine(llm)
        
        # Create content generation engine
        content_engine = ContentGenerationFactory.create_default_engine(llm)
        
        return BaseballAnalysisService(
            context_manager,
            analysis_engine,
            content_engine
        )
    
    @staticmethod
    def create_scheduling_service(repository: DataRepository) -> MatchSchedulingService:
        """Create a match scheduling service."""
        return MatchSchedulingService(repository)
    
    @staticmethod
    def create_publishing_service() -> ContentPublishingService:
        """Create a content publishing service."""
        return ContentPublishingService()
    
    @staticmethod
    def create_analytics_service() -> AnalyticsService:
        """Create an analytics service."""
        return AnalyticsService()
