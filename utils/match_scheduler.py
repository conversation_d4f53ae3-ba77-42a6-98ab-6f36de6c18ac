from datetime import datetime, timed<PERSON><PERSON>


def is_within_creation_window(match: dict, now: datetime, tz) -> bool:
    """
    경기 생성 가능 시간 윈도우 내에 있는지 판단
    - 축구, 농구, 배구: 경기 시작 24시간 전 ~ 시작 10분 전
    - 야구: 경기 시작 16시간 전 ~ 시작 10분 전
    Args:
        match (dict): 경기 정보
        now (datetime): 현재 시간 (KST)
        tz: pytz.timezone('Asia/Seoul') 등 타임존 객체
    Returns:
        bool: 생성 가능 시간대면 True, 아니면 False
    """
    sport = match.get('sports', '').lower()
    match_datetime_str = f"{match['match_date']} {match['match_time']}"
    try:
        match_datetime = datetime.strptime(match_datetime_str, "%Y-%m-%d %H:%M:%S")
        match_datetime = tz.localize(match_datetime)
        if sport in ['soccer', 'football', 'basketball', 'volleyball']:
            start_window = match_datetime - timedelta(hours=24)
        elif sport == 'baseball' or sport == '야구':
            start_window = match_datetime - timedelta(hours=34)
        else:
            return False
        end_window = match_datetime - timedelta(minutes=10)
        return start_window <= now <= end_window
    except Exception as e:
        # 로깅은 호출부에서 처리
        return False 