"""
로깅 설정 모듈

이 모듈은 모든 플랫폼(네이버, 티스토리, 블로거 등)에서 공통으로 사용할
중앙 집중식 로깅 시스템을 제공합니다.
"""
import datetime
import glob
import logging
import os
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path

# 상수 정의
DEFAULT_LOG_LEVEL = logging.DEBUG
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
CONSOLE_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
FILE_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
MAX_LOG_DAYS = 2  # 로그 보관 일수 (2일)

# 로그 레벨 매핑
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

# 로거 캐시 - 동일한 이름의 로거 재사용
_loggers = {}
_handlers_added = set()  # 중복 핸들러 방지

def cleanup_old_logs(log_dir, max_days=MAX_LOG_DAYS):
    """
    오래된 로그 파일을 정리합니다.

    Args:
        log_dir: 로그 디렉토리 경로
        max_days: 보관할 최대 일수 (기본값: 2일)
    """
    if not log_dir.exists():
        return

    today = datetime.datetime.now()

    # sportic_sns_YYYYMMDD.log 패턴의 파일들 확인
    log_pattern = str(log_dir / "sportic_sns_*.log*")
    log_files = glob.glob(log_pattern)

    for log_file in log_files:
        try:
            # 파일명에서 날짜 추출 (sportic_sns_20250607.log)
            filename = Path(log_file).name
            if 'sportic_sns_' in filename:
                date_part = filename.split('sportic_sns_')[1].split('.')[0]
                if len(date_part) == 8 and date_part.isdigit():
                    year = int(date_part[:4])
                    month = int(date_part[4:6])
                    day = int(date_part[6:8])
                    file_date = datetime.datetime(year, month, day)

                    # 날짜 차이 계산
                    date_diff = (today - file_date).days

                    # max_days일 이상 지난 파일 삭제
                    if date_diff >= max_days:
                        try:
                            os.remove(log_file)
                            print(f"오래된 로그 파일 삭제: {log_file}")
                        except Exception as e:
                            print(f"로그 파일 삭제 중 오류: {e}")
        except (ValueError, IndexError) as e:
            continue  # 잘못된 형식의 파일은 무시

def setup_root_logger(log_level=DEFAULT_LOG_LEVEL):
    """
    루트 로거를 설정합니다.

    Args:
        log_level: 로깅 레벨 (기본값: logging.WARNING)

    Returns:
        logging.Logger: 설정된 루트 로거
    """
    # 로그 디렉토리 생성
    log_dir = Path(__file__).resolve().parent.parent / 'logs'
    os.makedirs(log_dir, exist_ok=True)

    # 오래된 로그 파일 정리
    cleanup_old_logs(log_dir)

    # 로그 파일 경로 - 날짜가 포함된 파일명
    today = datetime.datetime.now().strftime('%Y%m%d')
    log_file = log_dir / f'sportic_sns_{today}.log'

    # 루트 로거 설정
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 기존 핸들러 제거 (중복 방지)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 콘솔 핸들러 (stdout)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_format = logging.Formatter(CONSOLE_LOG_FORMAT)
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)

    # 파일 핸들러 (회전 로그)
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(log_level)
    file_format = logging.Formatter(FILE_LOG_FORMAT)
    file_handler.setFormatter(file_format)
    root_logger.addHandler(file_handler)

    return root_logger

def get_logger(name, log_level=None):
    """
    지정된 이름으로 로거를 가져옵니다. 이미 생성된 로거는 캐시에서 재사용합니다.

    Args:
        name: 로거 이름 (예: 'naver', 'tistory', 'blogger')
        log_level: 로거 레벨 (기본값: None, 루트 로거의 레벨 사용)

    Returns:
        logging.Logger: 설정된 로거
    """
    # 이미 생성된 로거가 있으면 반환
    if name in _loggers:
        logger = _loggers[name]
        # 로그 레벨 설정이 요청된 경우에만 변경
        if log_level is not None:
            logger.setLevel(log_level)
        return logger

    # 새 로거 생성
    logger = logging.getLogger(name)

    # 상위 로거로 이벤트 전파 방지 (중복 로그 방지)
    logger.propagate = False

    # 로그 레벨 설정 (지정된 값 또는 루트 로거 상속)
    if log_level is not None:
        logger.setLevel(log_level)
    else:
        logger.setLevel(logging.getLogger().level)

    # 중복 핸들러 방지 - 핸들러 ID 체크
    handler_id = f"{name}"
    if handler_id not in _handlers_added:
        # 루트 로거의 핸들러 복사하여 사용
        root_logger = logging.getLogger()
        for handler in root_logger.handlers:
            logger.addHandler(handler)
        _handlers_added.add(handler_id)

    # 로거 캐시에 저장
    _loggers[name] = logger

    return logger

def set_log_level(logger_name=None, level='info'):
    """
    로거의 로그 레벨을 설정합니다.

    Args:
        logger_name: 로거 이름 (None이면 루트 로거)
        level: 로그 레벨 ('debug', 'info', 'warning', 'error', 'critical')
    """
    log_level = LOG_LEVELS.get(level.lower(), logging.WARNING)

    if logger_name is None or logger_name == 'root':
        # 루트 로거 레벨 설정
        logging.getLogger().setLevel(log_level)

        # 모든 핸들러의 레벨도 맞춰서 설정
        root_logger = logging.getLogger()
        for handler in root_logger.handlers:
            handler.setLevel(log_level)

        # 모든 캐시된 로거의 레벨도 업데이트
        for _, logger in _loggers.items():
            logger.setLevel(log_level)
    else:
        # 특정 로거 레벨 설정
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)

        # 로거 캐시 업데이트
        if logger_name in _loggers:
            _loggers[logger_name].setLevel(log_level)

def get_file_handler(name, log_level=logging.WARNING):
    """
    특정 모듈 전용 파일 핸들러를 생성합니다.

    Args:
        name: 모듈 이름 (예: 'naver', 'tistory')
        log_level: 로그 레벨

    Returns:
        logging.Handler: 파일 핸들러
    """
    # 로그 디렉토리 생성
    log_dir = Path(__file__).resolve().parent.parent / 'logs'
    os.makedirs(log_dir, exist_ok=True)

    # 파일명에 날짜와 타임스탬프 포함
    today = datetime.datetime.now().strftime('%Y%m%d')
    timestamp = datetime.datetime.now().strftime('%H%M%S')
    log_file = log_dir / f'{name}_{today}_{timestamp}.txt'

    # 파일 핸들러 생성
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3
    )
    file_handler.setLevel(log_level)
    file_format = logging.Formatter(FILE_LOG_FORMAT)
    file_handler.setFormatter(file_format)

    return file_handler

def init_logging(log_level='WARNING', log_dir=None, log_file_base=None, log_to_console=True, log_retention_days=2):
    """
    로깅 시스템 초기화

    Args:
        log_level (str): 로깅 레벨 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        log_dir (str): 로그 디렉토리 경로 (기본값: 현재 모듈 기준 상위 디렉토리의 logs 폴더)
        log_file_base (str): 로그 파일 이름 접두어 (기본값: sportic_sns)
        log_to_console (bool): 콘솔 출력 여부
        log_retention_days (int): 로그 파일 보관 일수
    """
    # 로그 레벨과 포맷 설정
    level = LOG_LEVELS.get(log_level.lower(), DEFAULT_LOG_LEVEL)
    format_str = DEFAULT_LOG_FORMAT

    # 루트 로거 설정
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # 기존 핸들러 제거 (중복 방지)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 콘솔 핸들러 추가
    if log_to_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(logging.Formatter(format_str))
        root_logger.addHandler(console_handler)

    # 외부 라이브러리 로그 레벨 조정
    # httpx 로그는 WARNING 레벨 이상만 표시 (INFO 레벨 로그 숨김)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    print(f"로깅 시스템 초기화 완료: 레벨={logging.getLevelName(level)}, 로그 보관={log_retention_days}일")

# 애플리케이션 시작 시 루트 로거 초기화
def init_root_logging(level=DEFAULT_LOG_LEVEL):
    """
    애플리케이션 로깅 시스템을 초기화합니다.

    Args:
        level: 기본 로그 레벨

    Returns:
        루트 로거
    """
    # 오래된 로그 정리 후 로거 설정
    root_logger = setup_root_logger(level)

    # httpx 라이브러리 로그 레벨 조정 (HTTP 요청 로그 감추기)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    print(f"로깅 시스템 초기화 완료: 레벨={logging.getLevelName(level)}, 로그 보관={MAX_LOG_DAYS}일")
    return root_logger

# 모듈 임포트 시 자동으로 루트 로거 초기화
root_logger = init_root_logging()
