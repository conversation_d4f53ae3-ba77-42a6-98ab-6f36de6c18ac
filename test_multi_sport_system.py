#!/usr/bin/env python3
"""
Test script for the multi-sport analysis system.

This script tests the integration with actual Supabase data
and verifies that sport-specific agents work correctly.
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config.config as global_config
from baseball_analytics.infrastructure.real_supabase_repository import RealSupabaseRepository
from baseball_analytics.infrastructure.llm_providers import setup_llm_manager
from baseball_analytics.infrastructure.config import LLMSettings
from baseball_analytics.agents.sport_specific_agents import MultiSportAnalysisService
from baseball_analytics.domain.models import SportType

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_database_connection():
    """Test Supabase database connection and data availability."""
    print("🔍 Testing database connection...")
    
    try:
        repository = RealSupabaseRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        # Test basic query
        target_matches = repository.get_sns_target_matches()
        print(f"✅ Database connection successful")
        print(f"📊 Found {len(target_matches)} target matches")
        
        if target_matches:
            # Group by sport and test data availability
            sport_counts = {}
            test_matches = {}
            
            for match_data in target_matches:
                sport_type = repository.get_sport_type(match_data)
                sport_name = {
                    SportType.BASEBALL: "야구",
                    SportType.SOCCER: "축구",
                    SportType.BASKETBALL: "농구",
                    SportType.VOLLEYBALL: "배구"
                }.get(sport_type, "기타")
                
                sport_counts[sport_name] = sport_counts.get(sport_name, 0) + 1
                
                # Store first match of each sport for testing
                if sport_name not in test_matches:
                    test_matches[sport_name] = match_data["match_id"]
            
            print("\n📈 Sport distribution:")
            for sport, count in sport_counts.items():
                print(f"   {sport}: {count}개")
            
            # Test data retrieval for each sport
            print("\n🔍 Testing data availability by sport:")
            for sport_name, match_id in test_matches.items():
                print(f"\n   {sport_name} ({match_id}):")
                
                # Test basic match data
                match_data = repository.get_match_data(match_id)
                print(f"     ✅ Match data: {match_data['home_team']} vs {match_data['away_team']}")
                
                # Test WDL data
                h2h_wdl = repository.get_h2h_wdl_summary(match_id)
                home_wdl = repository.get_home_wdl_summary(match_id)
                away_wdl = repository.get_away_wdl_summary(match_id)
                print(f"     📈 H2H WDL: {'✅' if h2h_wdl else '❌'}")
                print(f"     🏠 Home WDL: {'✅' if home_wdl else '❌'}")
                print(f"     🚌 Away WDL: {'✅' if away_wdl else '❌'}")
                
                # Test team_stats for baseball
                if repository.get_sport_type(match_data) == SportType.BASEBALL:
                    team_stats = repository.get_team_stats(match_id)
                    print(f"     ⚾ Team stats: {'✅' if team_stats else '❌'} ({len(team_stats)} records)")
                
                # Test content data
                h2h_content = repository.get_h2h_content(match_id)
                naver_text = repository.get_naver_text(match_id)
                betting_picks = repository.get_betting_picks(match_id)
                print(f"     📝 H2H content: {'✅' if h2h_content else '❌'}")
                print(f"     📱 Naver text: {'✅' if naver_text else '❌'}")
                print(f"     💰 Betting picks: {'✅' if betting_picks else '❌'}")
            
            return True, test_matches
        else:
            print("⚠️  No target matches found")
            return True, {}
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False, {}


def test_llm_connection():
    """Test LLM API connection."""
    print("\n🤖 Testing LLM connection...")
    
    try:
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Test simple query
        test_response = llm.invoke("안녕하세요. 간단한 테스트입니다.")
        print(f"✅ LLM connection successful")
        print(f"🔤 Test response: {str(test_response)[:50]}...")
        
        return True, llm
        
    except Exception as e:
        print(f"❌ LLM connection failed: {e}")
        return False, None


def test_sport_specific_analysis(test_matches: dict):
    """Test sport-specific analysis agents."""
    print(f"\n⚽ Testing sport-specific analysis...")
    
    try:
        # Setup components
        repository = RealSupabaseRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Create multi-sport analysis service
        analysis_service = MultiSportAnalysisService(llm, repository)
        
        # Test each sport
        for sport_name, match_id in test_matches.items():
            print(f"\n   🔄 Testing {sport_name} analysis ({match_id})...")
            
            try:
                analysis = analysis_service.analyze_match(match_id)
                
                print(f"     ✅ Analysis completed")
                print(f"     📊 Match: {analysis.home_team} vs {analysis.away_team}")
                print(f"     🎯 Prediction: {analysis.prediction}")
                print(f"     📈 Confidence: {analysis.confidence:.2f}")
                print(f"     🏷️  Hashtags: {len(analysis.hashtags)} generated")
                
                # Check sport-specific fields
                if sport_name == "야구" and hasattr(analysis, 'pitcher_analysis'):
                    print(f"     ⚾ Pitcher analysis: ✅")
                elif sport_name == "축구" and hasattr(analysis, 'formation_analysis'):
                    print(f"     ⚽ Formation analysis: ✅")
                elif sport_name == "농구" and hasattr(analysis, 'offense_analysis'):
                    print(f"     🏀 Offense analysis: ✅")
                elif sport_name == "배구" and hasattr(analysis, 'attack_analysis'):
                    print(f"     🏐 Attack analysis: ✅")
                
            except Exception as e:
                print(f"     ❌ Analysis failed: {e}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ Sport-specific analysis test failed: {e}")
        return False


def test_batch_analysis():
    """Test batch analysis of multiple sports."""
    print("\n📊 Testing batch analysis...")
    
    try:
        # Setup components
        repository = RealSupabaseRepository(
            global_config.SUPABASE_URL,
            global_config.SUPABASE_KEY
        )
        
        llm_settings = LLMSettings(
            openai_api_key=global_config.OPENAI_API_KEY,
            google_api_key=global_config.GEMINI_API_KEY,
            default_provider="openai" if global_config.OPENAI_API_KEY else "google"
        )
        llm_manager = setup_llm_manager(llm_settings)
        llm = llm_manager.get_model()
        
        # Create analysis service
        analysis_service = MultiSportAnalysisService(llm, repository)
        
        # Perform batch analysis
        print("🔄 Running batch analysis...")
        analyses = analysis_service.analyze_all_target_matches()
        
        print(f"✅ Batch analysis completed")
        print(f"📊 Analyzed {len(analyses)} matches")
        
        # Group by sport
        sport_counts = {}
        for analysis in analyses:
            sport = analysis.sport
            sport_counts[sport] = sport_counts.get(sport, 0) + 1
        
        print("\n📈 Analysis results by sport:")
        for sport, count in sport_counts.items():
            sport_name = {
                "baseball": "야구",
                "soccer": "축구", 
                "basketball": "농구",
                "volleyball": "배구"
            }.get(sport, sport)
            print(f"   {sport_name}: {count}개")
        
        # Show sample results
        print("\n📋 Sample results:")
        for analysis in analyses[:3]:
            print(f"   🏟️ {analysis.home_team} vs {analysis.away_team} ({analysis.sport})")
            print(f"      🎯 {analysis.prediction}")
            print(f"      📈 Confidence: {analysis.confidence:.2f}")
        
        return True, analyses
        
    except Exception as e:
        print(f"❌ Batch analysis test failed: {e}")
        return False, None


def main():
    """Run all tests."""
    print("🚀 Starting Multi-Sport Analysis System Tests")
    print("=" * 60)
    
    # Test 1: Database connection
    db_ok, test_matches = test_database_connection()
    if not db_ok:
        print("❌ Database tests failed. Please check your Supabase configuration.")
        return False
    
    # Test 2: LLM connection
    llm_ok, llm = test_llm_connection()
    if not llm_ok:
        print("❌ LLM tests failed. Please check your API keys.")
        return False
    
    # Test 3: Sport-specific analysis (if we have test matches)
    if test_matches:
        analysis_ok = test_sport_specific_analysis(test_matches)
        if not analysis_ok:
            print("❌ Sport-specific analysis tests failed.")
            return False
        
        # Test 4: Batch analysis
        batch_ok, batch_analyses = test_batch_analysis()
        if not batch_ok:
            print("❌ Batch analysis tests failed.")
            return False
    else:
        print("⚠️  Skipping analysis tests (no target matches available)")
    
    print("\n🎉 All tests completed successfully!")
    print("=" * 60)
    print("✅ Database connection: OK")
    print("✅ LLM connection: OK")
    if test_matches:
        print("✅ Sport-specific analysis: OK")
        print("✅ Batch analysis: OK")
    print("\n🚀 Multi-sport system is ready for use!")
    
    # Show usage examples
    print("\n📖 Usage Examples:")
    print("=" * 60)
    print("# List available matches by sport:")
    print("python run_multi_sport.py list")
    print()
    if test_matches:
        # Show example for each sport
        for sport_name, match_id in list(test_matches.items())[:2]:
            print(f"# Analyze {sport_name} match:")
            print(f"python run_multi_sport.py analyze {match_id}")
            print()
    print("# Batch analyze all matches:")
    print("python run_multi_sport.py batch")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
